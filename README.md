# Program Manager

A comprehensive building automation system for climate control, energy balance, and time-based scheduling.

## Quick Start

```bash
# Build everything
make all

# Run main program
./bin/program-manager

# For weather simulation + CEB testing
./scripts/run_weather_simulator.sh    # Terminal 1
./bin/program-manager                 # Terminal 2 (select option 2)
make monitor-ceb                      # Terminal 3 (optional)

# For interactive CEB testing
make run-ceb-test
```

## 📖 Complete Commands Reference

**See [COMMANDS.md](COMMANDS.md) for all available commands, usage instructions, and examples.**

The COMMANDS.md file contains:

-   All build and run commands
-   Weather simulation setup
-   CEB testing procedures
-   Redis data management
-   Troubleshooting guides
-   Complete workflow examples

## System Components

-   **Clock Program** - Astronomical calculations (sunrise/sunset, time management)
-   **Diurnal Program** - Time-based setpoint scheduling with dawn/dusk relative timing
-   **CEB Program** - Climate Energy Balance for HVAC control (9 outputs)
-   **Weather Simulation** - Realistic weather data generation for testing
-   **Interactive Testing** - Custom input testing for CEB system

## Key Features

-   ✅ Sunrise/sunset based scheduling (not civil twilight)
-   ✅ Interactive CEB testing with 9 outputs
-   ✅ Weather simulation with realistic daily cycles
-   ✅ Redis-based data storage and communication
-   ✅ Comprehensive command reference in COMMANDS.md
-   ✅ Coordinates: 17.407104033722273, 78.38716849147556 (Hyderabad, India)

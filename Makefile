# Build all programs
all: program-manager test-ceb-interactive test-clock weather-simulator

# Build main program
program-manager:
	go build -o bin/program-manager .

# Build test programs
test-ceb-interactive:
	go build -o bin/test-ceb-interactive test/test_ceb_interactive.go

test-clock:
	go build -o bin/test-clock test/test_clock.go

# Build weather simulation tools
weather-simulator:
	go build -o bin/weather_simulator scripts/weather_simulator.go
	go build -o bin/test_weather_data scripts/test_weather_data.go
	go build -o bin/monitor_ceb_weather scripts/monitor_ceb_weather.go

zone-sensor-simulator:
	go build -o bin/zone_sensor_simulator scripts/zone_sensor_simulator.go

# Run weather simulation
run-weather-sim: weather-simulator
	./bin/weather_simulator

# Test weather data
test-weather: weather-simulator
	./bin/test_weather_data

# Monitor CEB weather data flow
monitor-ceb: weather-simulator
	./bin/monitor_ceb_weather

# Run interactive CEB test
run-ceb-test: test-ceb-interactive
	./bin/test-ceb-interactive

# Run zone sensor simulator
run-zone-sensors: zone-sensor-simulator
	./scripts/run_zone_sensor_simulator.sh

run-zone-sensors-hot: zone-sensor-simulator
	./scripts/run_zone_sensor_simulator.sh hot

run-zone-sensors-cold: zone-sensor-simulator
	./scripts/run_zone_sensor_simulator.sh cold

run-zone-sensors-humid: zone-sensor-simulator
	./scripts/run_zone_sensor_simulator.sh humid

# Run tests
test: test-redis test-diurnal-unit test-clock-unit test-ceb-unit

test-redis:
	go test ./redis -v

test-diurnal-unit:
	go test ./diurnal -v

test-clock-unit:
	go test ./clock -v

test-ceb-unit:
	go test ./ceb -v

# Clean build artifacts
clean:
	rm -rf bin/

# Create bin directory
bin:
	mkdir -p bin

.PHONY: all program-manager test-ceb test-diurnal test-clock weather-simulator run-weather-sim test-weather test test-redis test-diurnal-unit test-clock-unit test-ceb-unit clean

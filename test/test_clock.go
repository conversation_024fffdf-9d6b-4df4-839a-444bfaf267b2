package main

import (
	"context"
	"log"

	"program-manager/redis"
)

// test_clock.go - A simple test script to check Clock Program data in Redis
func main() {
	// Initialize Redis
	redis.Initialize()
	ctx := context.Background()

	log.Println("Checking Clock Program data in Redis...")

	// Clock data keys to check
	clockKeys := []struct {
		key         string
		description string
		dataType    string
	}{
		{"clock:current_time", "Current Time", "string"},
		{"clock:date", "Date", "string"},
		{"clock:day_of_week", "Day of Week", "string"},
		{"clock:day_of_year", "Day of Year", "float"},
		{"clock:dawn", "Dawn Time", "string"},
		{"clock:dusk", "Dusk Time", "string"},
		{"clock:solar_noon", "Solar Noon", "string"},
		{"clock:day_length", "Day Length (hours)", "float"},
		{"clock:sun_elevation", "Sun Elevation (degrees)", "float"},
		{"clock:sun_azimuth", "Sun Azimuth (degrees)", "float"},
		{"clock:is_daytime", "Is Daytime", "float"},
		{"clock:last_ntp_sync", "Last NTP Sync", "string"},
		{"clock:ntp_sync_status", "NTP Sync Status", "string"},
	}

	log.Println("\nClock Program Data:")
	log.Println("==================")

	for _, item := range clockKeys {
		if item.dataType == "string" {
			if val, err := redis.GetString(ctx, item.key); err == nil {
				log.Printf("%-25s: %s", item.description, val)
			} else {
				log.Printf("%-25s: Not available (%v)", item.description, err)
			}
		} else if item.dataType == "float" {
			if val, err := redis.GetFloat(ctx, item.key); err == nil {
				if item.key == "clock:is_daytime" {
					daytime := "No"
					if val == 1.0 {
						daytime = "Yes"
					}
					log.Printf("%-25s: %s", item.description, daytime)
				} else {
					log.Printf("%-25s: %.2f", item.description, val)
				}
			} else {
				log.Printf("%-25s: Not available (%v)", item.description, err)
			}
		}
	}

	log.Println("\nTest completed!")
}

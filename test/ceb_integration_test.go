package main

import (
	"context"
	"testing"

	"program-manager/ceb"
	"program-manager/config"
	"program-manager/redis"
	"program-manager/types"
)

// TestCEBIntegrationWithRedis tests full CEB system integration with Redis
func TestCEBIntegrationWithRedis(t *testing.T) {
	// Initialize Redis for testing
	redis.Initialize()
	ctx := context.Background()

	// Create test configuration
	cebConfig := config.CreateEnhancedCEBConfig()
	controller := ceb.NewCEBController(cebConfig)

	// Set up test data in Redis
	setupIntegrationTestData(ctx)

	// Run one processing cycle
	err := controller.ProcessCycle(ctx)
	if err != nil {
		t.Fatalf("ProcessCycle failed: %v", err)
	}

	// Verify all 9 outputs are stored in Redis
	verifyAll9Outputs(ctx, t)

	// Verify output values are reasonable
	verifyOutputValues(ctx, t)
}

// TestCEBHeatingScenario tests heating demand scenario
func TestCEBHeatingScenario(t *testing.T) {
	redis.Initialize()
	ctx := context.Background()

	cebConfig := config.CreateEnhancedCEBConfig()
	controller := ceb.NewCEBController(cebConfig)

	// Set up cold scenario requiring heating
	setupColdScenario(ctx, t)

	err := controller.ProcessCycle(ctx)
	if err != nil {
		t.Fatalf("ProcessCycle failed: %v", err)
	}

	// Get Redis topics configuration
	topics := cebConfig.RedisTopics

	// Should have heating demand
	heatTempControl, err := redis.GetFloat(ctx, topics.HeatTempControl)
	if err != nil {
		t.Fatalf("Failed to get heat_temp_control: %v", err)
	}

	if heatTempControl <= 0 {
		t.Error("Expected heating demand in cold scenario")
	}

	// Heating system temperature should be above minimum
	heatSysTemp, err := redis.GetFloat(ctx, topics.HeatingSystemTempRequest)
	if err != nil {
		t.Fatalf("Failed to get heating_system_temp_request: %v", err)
	}

	if heatSysTemp < 30.0 {
		t.Errorf("Heating system temperature should be >= 30°C, got %.1f", heatSysTemp)
	}

	// Highest heat request should match temperature control demand
	highestHeat, err := redis.GetFloat(ctx, topics.HighestHeatRequest)
	if err != nil {
		t.Fatalf("Failed to get highest_heat_request: %v", err)
	}

	if highestHeat != heatTempControl {
		t.Errorf("Highest heat request should match temp control demand: %.1f vs %.1f", highestHeat, heatTempControl)
	}
}

// TestCEBCoolingScenario tests cooling demand scenario
func TestCEBCoolingScenario(t *testing.T) {
	redis.Initialize()
	ctx := context.Background()

	cebConfig := config.CreateEnhancedCEBConfig()
	controller := ceb.NewCEBController(cebConfig)

	// Set up hot scenario requiring cooling
	setupHotScenario(ctx, t)

	err := controller.ProcessCycle(ctx)
	if err != nil {
		t.Fatalf("ProcessCycle failed: %v", err)
	}

	// Get Redis topics configuration
	topics := cebConfig.RedisTopics

	// Should have ventilation demand for cooling
	ventTempControl, err := redis.GetFloat(ctx, topics.VentTempControl)
	if err != nil {
		t.Fatalf("Failed to get vent_temp_control: %v", err)
	}

	if ventTempControl <= 0 {
		t.Error("Expected ventilation demand in hot scenario")
	}

	// Should have minimal heating demand
	heatTempControl, err := redis.GetFloat(ctx, topics.HeatTempControl)
	if err != nil {
		t.Fatalf("Failed to get heat_temp_control: %v", err)
	}

	if heatTempControl > 5.0 {
		t.Errorf("Expected minimal heating demand in hot scenario, got %.1f", heatTempControl)
	}
}

// TestCEBDehumidificationScenario tests dehumidification scenario
func TestCEBDehumidificationScenario(t *testing.T) {
	redis.Initialize()
	ctx := context.Background()

	cebConfig := config.CreateEnhancedCEBConfig()
	controller := ceb.NewCEBController(cebConfig)

	// Set up high humidity scenario
	setupHighHumidityScenario(ctx, t)

	err := controller.ProcessCycle(ctx)
	if err != nil {
		t.Fatalf("ProcessCycle failed: %v", err)
	}

	// Get Redis topics configuration
	topics := cebConfig.RedisTopics

	// Should have both heating and ventilation for humidity control
	heatHumidControl, err := redis.GetFloat(ctx, topics.HeatHumidityControl)
	if err != nil {
		t.Fatalf("Failed to get heat_humidity_control: %v", err)
	}

	ventHumidControl, err := redis.GetFloat(ctx, topics.VentHumidityControl)
	if err != nil {
		t.Fatalf("Failed to get vent_humidity_control: %v", err)
	}

	if heatHumidControl <= 0 {
		t.Error("Expected heating demand for humidity control")
	}

	if ventHumidControl <= 0 {
		t.Error("Expected ventilation demand for humidity control")
	}

	// Test dehumidification interlock - ventilation should be reduced when heating is active
	if ventHumidControl >= heatHumidControl {
		t.Log("Note: Dehumidification interlock may be reducing ventilation demand")
	}
}

// TestCEBFeedForwardEffects tests feed-forward adjustments
func TestCEBFeedForwardEffects(t *testing.T) {
	redis.Initialize()
	ctx := context.Background()

	cebConfig := config.CreateEnhancedCEBConfig()
	controller := ceb.NewCEBController(cebConfig)

	// Test outdoor temperature effect
	setupColdOutdoorScenario(ctx, t)

	err := controller.ProcessCycle(ctx)
	if err != nil {
		t.Fatalf("ProcessCycle failed: %v", err)
	}

	// Get Redis topics configuration
	topics := cebConfig.RedisTopics

	// Get baseline heating demand
	_, err = redis.GetFloat(ctx, topics.HeatTempControl)
	if err != nil {
		t.Fatalf("Failed to get baseline heat demand: %v", err)
	}

	// Now test with high light levels
	setupHighLightScenario(ctx, t)

	err = controller.ProcessCycle(ctx)
	if err != nil {
		t.Fatalf("ProcessCycle failed: %v", err)
	}

	// Light should increase ventilation and may reduce heating
	ventWithLight, err := redis.GetFloat(ctx, topics.VentTempControl)
	if err != nil {
		t.Fatalf("Failed to get vent demand with light: %v", err)
	}

	if ventWithLight <= 0 {
		t.Error("Expected light effect to increase ventilation demand")
	}
}

// TestCEBEmergencyStop tests emergency stop functionality
func TestCEBEmergencyStop(t *testing.T) {
	redis.Initialize()
	ctx := context.Background()

	cebConfig := config.CreateEnhancedCEBConfig()
	cebConfig.SafetyLimits.EmergencyStop = true // Enable emergency stop
	controller := ceb.NewCEBController(cebConfig)

	// Set up scenario that would normally create demands
	setupColdScenario(ctx, t)

	err := controller.ProcessCycle(ctx)
	if err != nil {
		t.Fatalf("ProcessCycle failed: %v", err)
	}

	// Get Redis topics configuration
	topics := cebConfig.RedisTopics

	// All outputs should be zero due to emergency stop
	outputs := []string{
		topics.HeatTempControl,
		topics.HeatHumidityControl,
		topics.VentTempControl,
		topics.VentHumidityControl,
		topics.HighestHeatRequest,
		topics.HighestVentRequest,
	}

	for _, key := range outputs {
		value, err := redis.GetFloat(ctx, key)
		if err != nil {
			t.Fatalf("Failed to get %s: %v", key, err)
		}

		if value != 0.0 {
			t.Errorf("Emergency stop should zero %s, got %.1f", key, value)
		}
	}
}

// Helper functions for setting up test scenarios

// Helper function to set setpoint values using purpose-based lookup
func setSetpointValue(ctx context.Context, topics types.RedisTopicsConfig, purpose types.SetpointPurpose, value float64) {
	if setpoint, found := topics.GetSetpointByPurpose(purpose); found {
		redis.SetFloat(ctx, setpoint.RedisKey, value)
	}
}

func setupIntegrationTestData(ctx context.Context) {
	// Get default Redis topics configuration
	topics := config.CreateDefaultRedisTopicsConfig()

	// Set reasonable setpoints
	setSetpointValue(ctx, topics, types.PurposeHeatingTarget, 22.0)
	setSetpointValue(ctx, topics, types.PurposeCoolingTarget, 26.0)
	setSetpointValue(ctx, topics, types.PurposeDehumidifyTarget, 70.0)
	setSetpointValue(ctx, topics, types.PurposeHumidifyTarget, 35.0)
	setSetpointValue(ctx, topics, types.PurposeMaxDehumidVent, 20.0)
	setSetpointValue(ctx, topics, types.PurposeMaxDehumidHeat, 10.0)
	setSetpointValue(ctx, topics, types.PurposeCO2Target, 800.0)

	// Set moderate sensor readings
	redis.SetFloat(ctx, topics.SensorTemperature, 23.0)
	redis.SetFloat(ctx, topics.SensorHumidity, 65.0)
	redis.SetFloat(ctx, topics.WeatherOutdoorTemp, 15.0)
	redis.SetFloat(ctx, topics.WeatherLightLevel, 300.0)
}

func setupColdScenario(ctx context.Context, t *testing.T) {
	topics := config.CreateDefaultRedisTopicsConfig()
	setSetpointValue(ctx, topics, types.PurposeHeatingTarget, 22.0)
	setSetpointValue(ctx, topics, types.PurposeCoolingTarget, 26.0)
	setSetpointValue(ctx, topics, types.PurposeDehumidifyTarget, 70.0)

	redis.SetFloat(ctx, topics.SensorTemperature, 19.0) // Below heating target
	redis.SetFloat(ctx, topics.SensorHumidity, 60.0)
	redis.SetFloat(ctx, topics.WeatherOutdoorTemp, 5.0)  // Cold outside
	redis.SetFloat(ctx, topics.WeatherLightLevel, 100.0) // Low light
}

func setupHotScenario(ctx context.Context, t *testing.T) {
	topics := config.CreateDefaultRedisTopicsConfig()
	setSetpointValue(ctx, topics, types.PurposeHeatingTarget, 22.0)
	setSetpointValue(ctx, topics, types.PurposeCoolingTarget, 26.0)
	setSetpointValue(ctx, topics, types.PurposeDehumidifyHeatTarget, 70.0)
	setSetpointValue(ctx, topics, types.PurposeDehumidifyVentTarget, 35.0)
	setSetpointValue(ctx, topics, types.PurposeMaxDehumidVent, 20.0)
	setSetpointValue(ctx, topics, types.PurposeMaxDehumidHeat, 10.0)
	setSetpointValue(ctx, topics, types.PurposeCO2Target, 800.0)

	redis.SetFloat(ctx, topics.SensorTemperature, 28.0) // Above cooling target
	redis.SetFloat(ctx, topics.SensorHumidity, 60.0)
	redis.SetFloat(ctx, topics.WeatherOutdoorTemp, 30.0) // Hot outside
	redis.SetFloat(ctx, topics.WeatherLightLevel, 800.0) // High light
}

func setupHighHumidityScenario(ctx context.Context, t *testing.T) {
	topics := config.CreateDefaultRedisTopicsConfig()
	setSetpointValue(ctx, topics, types.PurposeHeatingTarget, 22.0)
	setSetpointValue(ctx, topics, types.PurposeCoolingTarget, 26.0)
	setSetpointValue(ctx, topics, types.PurposeDehumidifyHeatTarget, 70.0)

	redis.SetFloat(ctx, topics.SensorTemperature, 23.0) // In deadband
	redis.SetFloat(ctx, topics.SensorHumidity, 80.0)    // Above dehumidify target
	redis.SetFloat(ctx, topics.WeatherOutdoorTemp, 15.0)
	redis.SetFloat(ctx, topics.WeatherLightLevel, 300.0)
}

func setupColdOutdoorScenario(ctx context.Context, t *testing.T) {
	topics := config.CreateDefaultRedisTopicsConfig()
	setSetpointValue(ctx, topics, types.PurposeHeatingTarget, 22.0)
	setSetpointValue(ctx, topics, types.PurposeCoolingTarget, 26.0)
	setSetpointValue(ctx, topics, types.PurposeDehumidifyTarget, 70.0)

	redis.SetFloat(ctx, topics.SensorTemperature, 21.0) // Slightly below target
	redis.SetFloat(ctx, topics.SensorHumidity, 65.0)
	redis.SetFloat(ctx, topics.WeatherOutdoorTemp, -5.0) // Very cold outside
	redis.SetFloat(ctx, topics.WeatherLightLevel, 50.0)  // Low light
}

func setupHighLightScenario(ctx context.Context, t *testing.T) {
	topics := config.CreateDefaultRedisTopicsConfig()
	setSetpointValue(ctx, topics, types.PurposeHeatingTarget, 22.0)
	setSetpointValue(ctx, topics, types.PurposeCoolingTarget, 26.0)
	setSetpointValue(ctx, topics, types.PurposeDehumidifyHeatTarget, 70.0)
	setSetpointValue(ctx, topics, types.PurposeDehumidifyVentTarget, 35.0)
	setSetpointValue(ctx, topics, types.PurposeMaxDehumidVent, 20.0)
	setSetpointValue(ctx, topics, types.PurposeMaxDehumidHeat, 10.0)
	setSetpointValue(ctx, topics, types.PurposeCO2Target, 800.0)

	redis.SetFloat(ctx, topics.SensorTemperature, 21.0) // Slightly below target
	redis.SetFloat(ctx, topics.SensorHumidity, 65.0)
	redis.SetFloat(ctx, topics.WeatherOutdoorTemp, 10.0)
	redis.SetFloat(ctx, topics.WeatherLightLevel, 900.0) // Very high light
}

func verifyAll9Outputs(ctx context.Context, t *testing.T) {
	// Get default Redis topics configuration
	topics := config.CreateDefaultRedisTopicsConfig()

	outputs := []string{
		topics.VentTempControl,
		topics.VentHumidityControl,
		topics.HighestVentRequest,
		topics.SumVentRequests,
		topics.HeatTempControl,
		topics.HeatHumidityControl,
		topics.HighestHeatRequest,
		topics.SumHeatRequests,
		topics.HeatingSystemTempRequest,
	}

	for i, key := range outputs {
		_, err := redis.GetFloat(ctx, key)
		if err != nil {
			t.Errorf("Output %d (%s) not found in Redis: %v", i+1, key, err)
		}
	}
}

func verifyOutputValues(ctx context.Context, t *testing.T) {
	// Get default Redis topics configuration
	topics := config.CreateDefaultRedisTopicsConfig()

	// Verify percentage outputs are in valid range (0-100%)
	percentageOutputs := []string{
		topics.VentTempControl,
		topics.VentHumidityControl,
		topics.HighestVentRequest,
		topics.SumVentRequests,
		topics.HeatTempControl,
		topics.HeatHumidityControl,
		topics.HighestHeatRequest,
		topics.SumHeatRequests,
	}

	for _, key := range percentageOutputs {
		value, err := redis.GetFloat(ctx, key)
		if err != nil {
			t.Errorf("Failed to get %s: %v", key, err)
			continue
		}

		if value < 0 || value > 100 {
			t.Errorf("%s should be 0-100%%, got %.1f", key, value)
		}
	}

	// Verify heating system temperature is in valid range (30-80°C)
	heatSysTemp, err := redis.GetFloat(ctx, topics.HeatingSystemTempRequest)
	if err != nil {
		t.Errorf("Failed to get heating_system_temp_request: %v", err)
	} else if heatSysTemp < 30.0 || heatSysTemp > 80.0 {
		t.Errorf("Heating system temperature should be 30-80°C, got %.1f", heatSysTemp)
	}
}

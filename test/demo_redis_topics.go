package main

import (
	"context"
	"fmt"
	"log"

	"program-manager/config"
	"program-manager/redis"
	"program-manager/types"
)

// demo_redis_topics.go - Demonstrates the new Redis topics configuration system
func main() {
	// Initialize Redis
	redis.Initialize()
	ctx := context.Background()

	fmt.Println("=== CEB Redis Topics Configuration Demo ===\n")

	// Load the default Redis topics configuration
	topics := config.CreateDefaultRedisTopicsConfig()

	fmt.Println("📋 Configured Redis Topics:")
	fmt.Println("\n🔽 Input Topics - Setpoints (up to 8 configurable):")
	for i, setpoint := range topics.Setpoints {
		status := "✅ Enabled"
		if !setpoint.Enabled {
			status = "❌ Disabled"
		}
		fmt.Printf("  %d. %-20s -> %-30s (default: %.1f) %s\n",
			i+1, setpoint.Name, setpoint.RedisKey, setpoint.DefaultValue, status)
	}

	fmt.Println("\n🔽 Input Topics - Sensors:")
	fmt.Printf("  Sensor Temperature:         %s\n", topics.SensorTemperature)
	fmt.Printf("  Sensor Humidity:            %s\n", topics.SensorHumidity)
	fmt.Printf("  Weather Outdoor Temp:       %s\n", topics.WeatherOutdoorTemp)
	fmt.Printf("  Weather Light Level:        %s\n", topics.WeatherLightLevel)

	fmt.Println("\n🔼 Output Topics (CEB results):")
	fmt.Printf("  Vent Temp Control:          %s\n", topics.VentTempControl)
	fmt.Printf("  Vent Humidity Control:      %s\n", topics.VentHumidityControl)
	fmt.Printf("  Highest Vent Request:       %s\n", topics.HighestVentRequest)
	fmt.Printf("  Sum Vent Requests:          %s\n", topics.SumVentRequests)
	fmt.Printf("  Heat Temp Control:          %s\n", topics.HeatTempControl)
	fmt.Printf("  Heat Humidity Control:      %s\n", topics.HeatHumidityControl)
	fmt.Printf("  Highest Heat Request:       %s\n", topics.HighestHeatRequest)
	fmt.Printf("  Sum Heat Requests:          %s\n", topics.SumHeatRequests)
	fmt.Printf("  Heating System Temp Req:    %s\n", topics.HeatingSystemTempRequest)
	fmt.Printf("  Integrated Temperature:     %s\n", topics.IntegratedTemp)
	fmt.Printf("  Integrated Humidity:        %s\n", topics.IntegratedHumidity)

	fmt.Println("\n✅ Benefits of this configuration system:")
	fmt.Println("  • All Redis topics are centrally configured")
	fmt.Println("  • Easy to change topic names without code changes")
	fmt.Println("  • Consistent topic usage across all CEB components")
	fmt.Println("  • Support for different environments (dev/test/prod)")
	fmt.Println("  • Clean, streamlined topic management")

	// Demonstrate setting and getting values using configured topics
	fmt.Println("\n🧪 Testing Redis operations with configured topics:")

	// Test setpoint operations
	if heatingSetpoint, found := topics.GetSetpointByPurpose(types.PurposeHeatingTarget); found {
		if err := redis.SetFloat(ctx, heatingSetpoint.RedisKey, 22.0); err != nil {
			log.Printf("Error setting heating target: %v", err)
		} else {
			fmt.Printf("  ✓ Set %s: %.1f°C\n", heatingSetpoint.Name, 22.0)
		}

		if val, err := redis.GetFloat(ctx, heatingSetpoint.RedisKey); err == nil {
			fmt.Printf("  ✓ Read %s: %.1f°C\n", heatingSetpoint.Name, val)
		}
	}

	// Test additional setpoints using purpose-based lookup
	setpointTests := []struct {
		purpose types.SetpointPurpose
		value   float64
		unit    string
	}{
		{types.PurposeCoolingTarget, 26.0, "°C"},
		{types.PurposeDehumidifyHeatTarget, 70.0, "%"},
		{types.PurposeDehumidifyVentTarget, 35.0, "%"},
		{types.PurposeMaxDehumidVent, 20.0, "%"},
		{types.PurposeMaxDehumidHeat, 10.0, "%"},
		{types.PurposeCO2Target, 800.0, "ppm"},
	}

	for _, test := range setpointTests {
		if setpoint, found := topics.GetSetpointByPurpose(test.purpose); found && setpoint.Enabled {
			if err := redis.SetFloat(ctx, setpoint.RedisKey, test.value); err != nil {
				log.Printf("Error setting %s (%s): %v", setpoint.Name, test.purpose, err)
			} else {
				fmt.Printf("  ✓ Set %s (%s): %.1f%s\n", setpoint.Name, test.purpose, test.value, test.unit)
			}
		}
	}

	// Set some sensor test values
	if err := redis.SetFloat(ctx, topics.SensorTemperature, 22.5); err != nil {
		log.Printf("Error setting temperature: %v", err)
	} else {
		fmt.Printf("  ✓ Set temperature sensor: %.1f°C\n", 22.5)
	}

	if err := redis.SetFloat(ctx, topics.SensorHumidity, 65.0); err != nil {
		log.Printf("Error setting humidity: %v", err)
	} else {
		fmt.Printf("  ✓ Set humidity sensor: %.1f%%\n", 65.0)
	}

	// Read back the sensor values
	if temp, err := redis.GetFloat(ctx, topics.SensorTemperature); err == nil {
		fmt.Printf("  ✓ Read temperature sensor: %.1f°C\n", temp)
	}

	if humidity, err := redis.GetFloat(ctx, topics.SensorHumidity); err == nil {
		fmt.Printf("  ✓ Read humidity sensor: %.1f%%\n", humidity)
	}

	fmt.Println("\n🎯 The CEB system now uses these configured topics instead of hardcoded strings!")
	fmt.Println("📁 Configuration files:")
	fmt.Println("  • config/cebConfig.json - includes redisTopics section")
	fmt.Println("  • config/redisTopicsConfig.json - standalone topics config")
}

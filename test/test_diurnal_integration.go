package main

import (
	"context"
	"os"
	"testing"
	"time"

	"program-manager/config"
	"program-manager/diurnal"
	"program-manager/types"
)

// TestDiurnalIntegration tests end-to-end diurnal functionality
func TestDiurnalIntegration(t *testing.T) {
	// Create a test configuration file
	testConfig := types.DiurnalSetpointConfig{
		Instances: []types.DiurnalInstance{
			{
				InstanceId:  "1",
				ProgramName: "Integration Test Diurnal",
				Enabled:     true,
				HubId:       "1",
				ZoneId:      "1",
				Periods: []types.Period{
					{
						PeriodId:     "1",
						Name:         "Morning Period",
						Enabled:      true,
						PeriodStatus: "active",
						StartTime:    "08:00",
						EndTime:      "12:00",
						ActiveDays: map[string]bool{
							"monday":    true,
							"tuesday":   true,
							"wednesday": true,
							"thursday":  true,
							"friday":    true,
							"saturday":  false,
							"sunday":    false,
						},
						Setpoints: map[string]float64{
							"heatingTarget":  20.0,
							"coolingTarget":  25.0,
							"humidifyTarget": 60.0,
						},
					},
					{
						PeriodId:     "2",
						Name:         "Afternoon Period",
						Enabled:      true,
						PeriodStatus: "active",
						StartTime:    "13:00",
						EndTime:      "17:00",
						ActiveDays: map[string]bool{
							"monday":    true,
							"tuesday":   true,
							"wednesday": true,
							"thursday":  true,
							"friday":    true,
							"saturday":  false,
							"sunday":    false,
						},
						Setpoints: map[string]float64{
							"heatingTarget":  22.0,
							"coolingTarget":  27.0,
							"humidifyTarget": 65.0,
						},
					},
				},
			},
			{
				InstanceId:  "2",
				ProgramName: "Disabled Instance",
				Enabled:     false,
				HubId:       "1",
				ZoneId:      "1",
				Periods: []types.Period{
					{
						PeriodId:     "1",
						Name:         "Disabled Period",
						Enabled:      true,
						PeriodStatus: "inactive",
						StartTime:    "10:00",
						EndTime:      "14:00",
						ActiveDays:   map[string]bool{"monday": true},
						Setpoints:    map[string]float64{"temperature": 18.0},
					},
				},
			},
		},
	}

	testFile := "test_integration_config.json"

	// Clean up after test
	defer func() {
		os.Remove(testFile)
	}()

	// Test 1: Save and load configuration
	t.Run("SaveAndLoadConfig", func(t *testing.T) {
		err := config.SaveDiurnalConfig(testFile, testConfig)
		if err != nil {
			t.Fatalf("Failed to save test config: %v", err)
		}

		loadedConfig, err := config.LoadDiurnalConfig(testFile)
		if err != nil {
			t.Fatalf("Failed to load test config: %v", err)
		}

		if len(loadedConfig.Instances) != 2 {
			t.Errorf("Expected 2 instances, got %d", len(loadedConfig.Instances))
		}
	})

	// Test 2: Configuration validation
	t.Run("ValidateConfig", func(t *testing.T) {
		err := config.ValidateLoadedConfig(testConfig)
		if err != nil {
			t.Errorf("Valid configuration failed validation: %v", err)
		}
	})

	// Test 3: Diurnal state calculation for different times
	t.Run("DiurnalStateCalculation", func(t *testing.T) {
		instance := testConfig.Instances[0] // Enabled instance

		testTimes := []struct {
			name          string
			time          time.Time
			expectedState types.PeriodState
		}{
			{
				name:          "Monday 10:00 - in morning period",
				time:          time.Date(2024, 6, 17, 10, 0, 0, 0, time.UTC), // Monday
				expectedState: types.StateInPeriod,
			},
			{
				name:          "Monday 12:30 - ramping between periods",
				time:          time.Date(2024, 6, 17, 12, 30, 0, 0, time.UTC), // Monday
				expectedState: types.StateRamping,
			},
			{
				name:          "Monday 15:00 - in afternoon period",
				time:          time.Date(2024, 6, 17, 15, 0, 0, 0, time.UTC), // Monday
				expectedState: types.StateInPeriod,
			},
			{
				name:          "Monday 20:00 - idle time",
				time:          time.Date(2024, 6, 17, 20, 0, 0, 0, time.UTC), // Monday
				expectedState: types.StateIdle,
			},
			{
				name:          "Saturday 10:00 - inactive day",
				time:          time.Date(2024, 6, 22, 10, 0, 0, 0, time.UTC), // Saturday
				expectedState: types.StateIdle,
			},
		}

		for _, tt := range testTimes {
			t.Run(tt.name, func(t *testing.T) {
				state, _, err := diurnal.GetCurrentState(tt.time, instance)
				if err != nil {
					t.Errorf("Error getting current state: %v", err)
				}
				if state != tt.expectedState {
					t.Errorf("Expected state %v, got %v", tt.expectedState, state)
				}
			})
		}
	})

	// Test 4: Disabled instance behavior
	t.Run("DisabledInstanceBehavior", func(t *testing.T) {
		disabledInstance := testConfig.Instances[1] // Disabled instance

		// Monday 12:00 - would be active if enabled
		testTime := time.Date(2024, 6, 17, 12, 0, 0, 0, time.UTC)

		state, period, err := diurnal.GetCurrentState(testTime, disabledInstance)
		if err != nil {
			t.Errorf("Error getting state for disabled instance: %v", err)
		}

		if state != types.StateIdle {
			t.Errorf("Expected StateIdle for disabled instance, got %v", state)
		}

		if period != nil {
			t.Errorf("Expected no period for disabled instance, got period %s", period.PeriodId)
		}
	})

	// Test 5: Ramp rate calculation
	t.Run("RampRateCalculation", func(t *testing.T) {
		period1 := testConfig.Instances[0].Periods[0] // Morning period
		period2 := testConfig.Instances[0].Periods[1] // Afternoon period

		// Test heating target ramp rate
		rampRate, err := diurnal.CalculateRampRate(period1, period2, "heatingTarget")
		if err != nil {
			t.Errorf("Error calculating ramp rate: %v", err)
		}

		// Expected: (22.0 - 20.0) / (13:00 - 12:00) = 2.0 / 60 minutes = 0.0333 per minute
		expectedRate := 2.0 / 60.0
		tolerance := 0.001

		if abs(rampRate-expectedRate) > tolerance {
			t.Errorf("Expected ramp rate %.4f, got %.4f", expectedRate, rampRate)
		}

		// Test missing setpoint
		_, err = diurnal.CalculateRampRate(period1, period2, "nonexistentSetpoint")
		if err == nil {
			t.Error("Expected error for nonexistent setpoint")
		}
	})

	// Test 6: Process instances (mock Redis operations)
	t.Run("ProcessInstances", func(t *testing.T) {
		ctx := context.Background()

		// Note: This test would normally require Redis to be running
		// For now, we'll just test that the function doesn't panic
		// In a real environment, you'd want to use a test Redis instance

		defer func() {
			if r := recover(); r != nil {
				t.Errorf("ProcessInstances panicked: %v", r)
			}
		}()

		// This will likely fail due to Redis connection, but shouldn't panic
		diurnal.ProcessInstances(ctx, testConfig, nil)
	})
}

// TestRelativeTimeIntegration tests relative time functionality
func TestRelativeTimeIntegration(t *testing.T) {
	// Create configuration with relative times
	relativeConfig := types.DiurnalSetpointConfig{
		Instances: []types.DiurnalInstance{
			{
				InstanceId:  "1",
				ProgramName: "Relative Time Test",
				Enabled:     true,
				HubId:       "1",
				ZoneId:      "1",
				Periods: []types.Period{
					{
						PeriodId:     "1",
						Name:         "Pre-Dawn Period",
						Enabled:      true,
						PeriodStatus: "active",
						StartTime:    "01:00 beforeDawn",
						EndTime:      "02:00 afterDawn",
						ActiveDays:   map[string]bool{"monday": true},
						Setpoints:    map[string]float64{"temperature": 18.0},
					},
					{
						PeriodId:     "2",
						Name:         "Pre-Dusk Period",
						Enabled:      true,
						PeriodStatus: "active",
						StartTime:    "01:00 beforeDusk",
						EndTime:      "02:00 afterDusk",
						ActiveDays:   map[string]bool{"monday": true},
						Setpoints:    map[string]float64{"temperature": 20.0},
					},
				},
			},
		},
	}

	// Test configuration validation with relative times
	t.Run("ValidateRelativeTimeConfig", func(t *testing.T) {
		err := config.ValidateLoadedConfig(relativeConfig)
		if err != nil {
			t.Errorf("Relative time configuration failed validation: %v", err)
		}
	})

	// Test ramp rate calculation with relative times
	t.Run("RelativeTimeRampRate", func(t *testing.T) {
		period1 := relativeConfig.Instances[0].Periods[0]
		period2 := relativeConfig.Instances[0].Periods[1]

		// This should work even with relative times (they get resolved internally)
		_, err := diurnal.CalculateRampRate(period1, period2, "temperature")
		if err != nil {
			t.Logf("Ramp rate calculation with relative times: %v (expected for relative times)", err)
			// This is expected to work once relative time resolution is fully implemented
		}
	})
}

// Helper function for absolute value
func abs(x float64) float64 {
	if x < 0 {
		return -x
	}
	return x
}

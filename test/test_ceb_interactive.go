package main

import (
	"bufio"
	"context"
	"fmt"
	"log"
	"os"
	"strconv"
	"strings"

	"program-manager/ceb"
	"program-manager/config"
	"program-manager/redis"
)

// CEBTestInputs represents all the input values for CEB testing
type CEBTestInputs struct {
	// Sensor inputs
	ZoneTemperature    float64
	ZoneHumidity       float64
	OutdoorTemperature float64
	LightLevel         float64
	ShadePosition      float64

	// Setpoint inputs
	HeatingTarget               float64
	CoolingTarget               float64
	DehumidifyVentilationTarget float64
	DehumidifyHeatingTarget     float64
	MaxDehumidifyVentilation    float64
	MaxDehumidifyHeating        float64
}

// CEBTestOutputs represents the 9 CEB outputs
type CEBTestOutputs struct {
	VentTempControl          float64 // 1. Ventilation Required for Temperature Control
	VentHumidityControl      float64 // 2. Ventilation Required for Humidity Control
	HighestVentRequest       float64 // 3. Highest Ventilation Request
	SumVentRequests          float64 // 4. Sum of Ventilation Requests
	HeatTempControl          float64 // 5. Heating Required for Temperature Control
	HeatHumidityControl      float64 // 6. Heating Required for Humidity Control
	HighestHeatRequest       float64 // 7. Highest Heating Request
	SumHeatRequests          float64 // 8. Sum of Heating Requests
	HeatingSystemTempRequest float64 // 9. Current Temperature Request to Heating System
}

func main() {
	fmt.Println("🏭 CEB Interactive Test - Enter Input Values and See 9 Outputs")
	fmt.Println("==============================================================")
	fmt.Println()

	// Initialize Redis
	redis.Initialize()
	ctx := context.Background()

	// Create CEB controller with enhanced configuration
	cebConfig := config.CreateEnhancedCEBConfig()
	controller := ceb.NewCEBController(cebConfig)

	scanner := bufio.NewScanner(os.Stdin)

	for {
		fmt.Println("\n🔧 CEB Test Menu:")
		fmt.Println("1. Enter custom input values")
		fmt.Println("2. Use preset scenario (Hot Day)")
		fmt.Println("3. Use preset scenario (Cold Day)")
		fmt.Println("4. Use preset scenario (High Humidity)")
		fmt.Println("5. Exit")
		fmt.Print("\nSelect option (1-5): ")

		scanner.Scan()
		choice := strings.TrimSpace(scanner.Text())

		switch choice {
		case "1":
			inputs := getCustomInputs(scanner)
			runCEBTest(ctx, controller, inputs)
		case "2":
			inputs := getHotDayPreset()
			fmt.Println("\n🌞 Using Hot Day Preset...")
			runCEBTest(ctx, controller, inputs)
		case "3":
			inputs := getColdDayPreset()
			fmt.Println("\n❄️ Using Cold Day Preset...")
			runCEBTest(ctx, controller, inputs)
		case "4":
			inputs := getHighHumidityPreset()
			fmt.Println("\n💧 Using High Humidity Preset...")
			runCEBTest(ctx, controller, inputs)
		case "5":
			fmt.Println("\n👋 Goodbye!")
			return
		default:
			fmt.Println("❌ Invalid choice. Please select 1-5.")
		}
	}
}

func getCustomInputs(scanner *bufio.Scanner) CEBTestInputs {
	inputs := CEBTestInputs{}

	fmt.Println("\n📊 Enter Input Values:")
	fmt.Println("======================")

	// Sensor inputs
	fmt.Println("\n🌡️ Sensor Inputs:")
	inputs.ZoneTemperature = getFloatInput(scanner, "Zone Temperature (°C)", 22.0)
	inputs.ZoneHumidity = getFloatInput(scanner, "Zone Humidity (%)", 55.0)
	inputs.OutdoorTemperature = getFloatInput(scanner, "Outdoor Temperature (°C)", 25.0)
	inputs.LightLevel = getFloatInput(scanner, "Light Level (lux)", 500.0)
	inputs.ShadePosition = getFloatInput(scanner, "Shade Position (%)", 0.0)

	// Setpoint inputs
	fmt.Println("\n🎯 Setpoint Inputs:")
	inputs.HeatingTarget = getFloatInput(scanner, "Heating Target (°C)", 20.0)
	inputs.CoolingTarget = getFloatInput(scanner, "Cooling Target (°C)", 24.0)
	inputs.DehumidifyVentilationTarget = getFloatInput(scanner, "Dehumidify Ventilation Target (%)", 60.0)
	inputs.DehumidifyHeatingTarget = getFloatInput(scanner, "Dehumidify Heating Target (%)", 70.0)
	inputs.MaxDehumidifyVentilation = getFloatInput(scanner, "Max Dehumidify Ventilation (%)", 10.0)
	inputs.MaxDehumidifyHeating = getFloatInput(scanner, "Max Dehumidify Heating (%)", 5.0)

	return inputs
}

func getFloatInput(scanner *bufio.Scanner, prompt string, defaultValue float64) float64 {
	fmt.Printf("  %s [default: %.1f]: ", prompt, defaultValue)
	scanner.Scan()
	input := strings.TrimSpace(scanner.Text())

	if input == "" {
		return defaultValue
	}

	value, err := strconv.ParseFloat(input, 64)
	if err != nil {
		fmt.Printf("    ⚠️ Invalid input, using default: %.1f\n", defaultValue)
		return defaultValue
	}

	return value
}

func getHotDayPreset() CEBTestInputs {
	return CEBTestInputs{
		ZoneTemperature:             26.5,  // Hot zone
		ZoneHumidity:                45.0,  // Low humidity
		OutdoorTemperature:          35.0,  // Very hot outside
		LightLevel:                  800.0, // Bright sunlight
		ShadePosition:               0.0,   // Shade retracted
		HeatingTarget:               20.0,
		CoolingTarget:               24.0, // Need cooling
		DehumidifyVentilationTarget: 60.0,
		DehumidifyHeatingTarget:     70.0,
		MaxDehumidifyVentilation:    10.0,
		MaxDehumidifyHeating:        5.0,
	}
}

func getColdDayPreset() CEBTestInputs {
	return CEBTestInputs{
		ZoneTemperature:             18.0,  // Cold zone
		ZoneHumidity:                65.0,  // Higher humidity
		OutdoorTemperature:          5.0,   // Cold outside
		LightLevel:                  100.0, // Low light
		ShadePosition:               0.0,   // Shade retracted
		HeatingTarget:               20.0,  // Need heating
		CoolingTarget:               24.0,
		DehumidifyVentilationTarget: 60.0,
		DehumidifyHeatingTarget:     70.0,
		MaxDehumidifyVentilation:    10.0,
		MaxDehumidifyHeating:        5.0,
	}
}

func getHighHumidityPreset() CEBTestInputs {
	return CEBTestInputs{
		ZoneTemperature:             22.0,  // Normal temperature
		ZoneHumidity:                75.0,  // High humidity - needs dehumidification
		OutdoorTemperature:          20.0,  // Moderate outside
		LightLevel:                  300.0, // Moderate light
		ShadePosition:               0.0,   // Shade retracted
		HeatingTarget:               20.0,
		CoolingTarget:               24.0,
		DehumidifyVentilationTarget: 60.0, // Target below current humidity
		DehumidifyHeatingTarget:     70.0, // Target below current humidity
		MaxDehumidifyVentilation:    10.0,
		MaxDehumidifyHeating:        5.0,
	}
}

func runCEBTest(ctx context.Context, controller *ceb.CEBController, inputs CEBTestInputs) {
	fmt.Println("\n🔄 Running CEB Calculation...")
	fmt.Println("==============================")

	// Store input values in Redis
	if err := storeInputsInRedis(ctx, inputs); err != nil {
		log.Printf("❌ Error storing inputs in Redis: %v", err)
		return
	}

	// Enable verbose logging for detailed output
	controller.SetVerboseLogging(true)

	// Run CEB processing cycle
	if err := controller.ProcessCycle(ctx); err != nil {
		log.Printf("❌ Error running CEB cycle: %v", err)
		return
	}

	// Read and display outputs
	outputs, err := readOutputsFromRedis(ctx)
	if err != nil {
		log.Printf("❌ Error reading outputs from Redis: %v", err)
		return
	}

	displayResults(inputs, outputs)
}

func storeInputsInRedis(ctx context.Context, inputs CEBTestInputs) error {
	// Store sensor values
	if err := redis.SetFloat(ctx, "hub:h1:io:sensorTemperature", inputs.ZoneTemperature); err != nil {
		return err
	}
	if err := redis.SetFloat(ctx, "hub:h1:io:sensorHumidity", inputs.ZoneHumidity); err != nil {
		return err
	}
	if err := redis.SetFloat(ctx, "hub:h1:instance:I1:weather:outdoorTemperature", inputs.OutdoorTemperature); err != nil {
		return err
	}
	if err := redis.SetFloat(ctx, "hub:h1:instance:I1:weather:lightLevelNI", inputs.LightLevel); err != nil {
		return err
	}
	if err := redis.SetFloat(ctx, "hub:h1:zone:z1:shade:shadePosition", inputs.ShadePosition); err != nil {
		return err
	}

	// Store setpoint values
	if err := redis.SetFloat(ctx, "hub:h1:zone:z1:instance:I1:setpoint:heatingTarget", inputs.HeatingTarget); err != nil {
		return err
	}
	if err := redis.SetFloat(ctx, "hub:h1:zone:z1:instance:I1:setpoint:coolingTarget", inputs.CoolingTarget); err != nil {
		return err
	}
	if err := redis.SetFloat(ctx, "hub:h1:zone:z1:instance:I1:setpoint:dehumidifyVentTarget", inputs.DehumidifyVentilationTarget); err != nil {
		return err
	}
	if err := redis.SetFloat(ctx, "hub:h1:zone:z1:instance:I1:setpoint:dehumidifyHeatTarget", inputs.DehumidifyHeatingTarget); err != nil {
		return err
	}
	if err := redis.SetFloat(ctx, "hub:h1:zone:z1:instance:I1:setpoint:maxDehumidVent", inputs.MaxDehumidifyVentilation); err != nil {
		return err
	}
	if err := redis.SetFloat(ctx, "hub:h1:zone:z1:instance:I1:setpoint:maxDehumidHeat", inputs.MaxDehumidifyHeating); err != nil {
		return err
	}

	return nil
}

func readOutputsFromRedis(ctx context.Context) (CEBTestOutputs, error) {
	outputs := CEBTestOutputs{}

	var err error
	outputs.VentTempControl, err = redis.GetFloat(ctx, "hub:h1:zone:z1:instance:I1:ceb:ventTempControl")
	if err != nil {
		return outputs, err
	}

	outputs.VentHumidityControl, err = redis.GetFloat(ctx, "hub:h1:zone:z1:instance:I1:ceb:ventHumidityControl")
	if err != nil {
		return outputs, err
	}

	outputs.HighestVentRequest, err = redis.GetFloat(ctx, "hub:h1:zone:z1:instance:I1:ceb:highestVentRequest")
	if err != nil {
		return outputs, err
	}

	outputs.SumVentRequests, err = redis.GetFloat(ctx, "hub:h1:zone:z1:instance:I1:ceb:sumVentRequests")
	if err != nil {
		return outputs, err
	}

	outputs.HeatTempControl, err = redis.GetFloat(ctx, "hub:h1:zone:z1:instance:I1:ceb:heatTempControl")
	if err != nil {
		return outputs, err
	}

	outputs.HeatHumidityControl, err = redis.GetFloat(ctx, "hub:h1:zone:z1:instance:I1:ceb:heatHumidityControl")
	if err != nil {
		return outputs, err
	}

	outputs.HighestHeatRequest, err = redis.GetFloat(ctx, "hub:h1:zone:z1:instance:I1:ceb:highestHeatRequest")
	if err != nil {
		return outputs, err
	}

	outputs.SumHeatRequests, err = redis.GetFloat(ctx, "hub:h1:zone:z1:instance:I1:ceb:sumHeatRequests")
	if err != nil {
		return outputs, err
	}

	outputs.HeatingSystemTempRequest, err = redis.GetFloat(ctx, "hub:h1:zone:z1:instance:I1:ceb:heatingSystemTempRequest")
	if err != nil {
		return outputs, err
	}

	return outputs, nil
}

func displayResults(inputs CEBTestInputs, outputs CEBTestOutputs) {
	fmt.Println("\n📊 CEB Test Results")
	fmt.Println("===================")

	// Display inputs summary
	fmt.Println("\n📥 Input Values:")
	fmt.Printf("  Zone Temperature:        %6.1f °C\n", inputs.ZoneTemperature)
	fmt.Printf("  Zone Humidity:           %6.1f %%\n", inputs.ZoneHumidity)
	fmt.Printf("  Outdoor Temperature:     %6.1f °C\n", inputs.OutdoorTemperature)
	fmt.Printf("  Light Level:             %6.0f lux\n", inputs.LightLevel)
	fmt.Printf("  Heating Target:          %6.1f °C\n", inputs.HeatingTarget)
	fmt.Printf("  Cooling Target:          %6.1f °C\n", inputs.CoolingTarget)
	fmt.Printf("  Dehumidify Vent Target:  %6.1f %%\n", inputs.DehumidifyVentilationTarget)
	fmt.Printf("  Dehumidify Heat Target:  %6.1f %%\n", inputs.DehumidifyHeatingTarget)

	// Display the 9 CEB outputs
	fmt.Println("\n📤 CEB Outputs (9 Values):")
	fmt.Printf("  1. Vent Temp Control:         %6.1f %%\n", outputs.VentTempControl)
	fmt.Printf("  2. Vent Humidity Control:     %6.1f %%\n", outputs.VentHumidityControl)
	fmt.Printf("  3. Highest Vent Request:      %6.1f %%\n", outputs.HighestVentRequest)
	fmt.Printf("  4. Sum of Vent Requests:      %6.1f %%\n", outputs.SumVentRequests)
	fmt.Printf("  5. Heat Temp Control:         %6.1f %%\n", outputs.HeatTempControl)
	fmt.Printf("  6. Heat Humidity Control:     %6.1f %%\n", outputs.HeatHumidityControl)
	fmt.Printf("  7. Highest Heat Request:      %6.1f %%\n", outputs.HighestHeatRequest)
	fmt.Printf("  8. Sum of Heat Requests:      %6.1f %%\n", outputs.SumHeatRequests)
	fmt.Printf("  9. Heating System Temp Req:   %6.1f °C\n", outputs.HeatingSystemTempRequest)

	// Analysis
	fmt.Println("\n🔍 Analysis:")
	if outputs.HighestVentRequest > 0 {
		fmt.Printf("  🌬️  Ventilation needed: %.1f%% (for ", outputs.HighestVentRequest)
		if outputs.VentTempControl > outputs.VentHumidityControl {
			fmt.Println("temperature control)")
		} else {
			fmt.Println("humidity control)")
		}
	}

	if outputs.HighestHeatRequest > 0 {
		fmt.Printf("  🔥 Heating needed: %.1f%% (for ", outputs.HighestHeatRequest)
		if outputs.HeatTempControl > outputs.HeatHumidityControl {
			fmt.Println("temperature control)")
		} else {
			fmt.Println("humidity control)")
		}
	}

	if outputs.HighestVentRequest == 0 && outputs.HighestHeatRequest == 0 {
		fmt.Println("  ✅ System in balance - no heating or cooling needed")
	}
}

package monitoring

import (
	"testing"
)

// TestCalculateCurrentSetpoint tests the current setpoint calculation
func TestCalculateCurrentSetpoint(t *testing.T) {
	tests := []struct {
		name           string
		startSetpoint  float64
		rampRate       float64
		minutesElapsed float64
		expected       float64
	}{
		{
			name:           "Positive ramp rate",
			startSetpoint:  12.0,
			rampRate:       0.1667, // 10 degrees over 60 minutes
			minutesElapsed: 60.0,
			expected:       22.002, // 12 + (0.1667 * 60) ≈ 22.002
		},
		{
			name:           "Negative ramp rate",
			startSetpoint:  25.0,
			rampRate:       -0.1667, // -10 degrees over 60 minutes
			minutesElapsed: 60.0,
			expected:       14.998, // 25 + (-0.1667 * 60) ≈ 14.998
		},
		{
			name:           "Zero ramp rate",
			startSetpoint:  20.0,
			rampRate:       0.0,
			minutesElapsed: 60.0,
			expected:       20.0, // No change
		},
		{
			name:           "Zero minutes elapsed",
			startSetpoint:  15.0,
			rampRate:       0.5,
			minutesElapsed: 0.0,
			expected:       15.0, // No change
		},
		{
			name:           "Partial time elapsed",
			startSetpoint:  10.0,
			rampRate:       0.2, // 0.2 degrees per minute
			minutesElapsed: 30.0,
			expected:       16.0, // 10 + (0.2 * 30) = 16
		},
		{
			name:           "Large ramp rate",
			startSetpoint:  0.0,
			rampRate:       10.0, // 10 degrees per minute
			minutesElapsed: 5.0,
			expected:       50.0, // 0 + (10 * 5) = 50
		},
		{
			name:           "Fractional minutes",
			startSetpoint:  20.0,
			rampRate:       0.1,
			minutesElapsed: 5.5,
			expected:       20.55, // 20 + (0.1 * 5.5) = 20.55
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := CalculateCurrentSetpoint(tt.startSetpoint, tt.rampRate, tt.minutesElapsed)

			// Allow for small floating point differences
			if abs(result-tt.expected) > 0.01 {
				t.Errorf("Expected %.3f, got %.3f", tt.expected, result)
			}
		})
	}
}

// TestCalculateCurrentSetpointEdgeCases tests edge cases
func TestCalculateCurrentSetpointEdgeCases(t *testing.T) {
	// Test with very large numbers
	result := CalculateCurrentSetpoint(1000000.0, 0.001, 1000000.0)
	expected := 1001000.0 // 1000000 + (0.001 * 1000000) = 1000000 + 1000 = 1001000
	if abs(result-expected) > 0.01 {
		t.Errorf("Large numbers test: Expected %.3f, got %.3f", expected, result)
	}

	// Test with very small numbers
	result = CalculateCurrentSetpoint(0.001, 0.0001, 10.0)
	expected = 0.002 // 0.001 + (0.0001 * 10)
	if abs(result-expected) > 0.0001 {
		t.Errorf("Small numbers test: Expected %.6f, got %.6f", expected, result)
	}

	// Test with negative start setpoint
	result = CalculateCurrentSetpoint(-10.0, 0.5, 20.0)
	expected = 0.0 // -10 + (0.5 * 20)
	if abs(result-expected) > 0.01 {
		t.Errorf("Negative start setpoint test: Expected %.3f, got %.3f", expected, result)
	}

	// Test with negative minutes elapsed (shouldn't happen in practice but should still work)
	result = CalculateCurrentSetpoint(20.0, 0.1, -10.0)
	expected = 19.0 // 20 + (0.1 * -10)
	if abs(result-expected) > 0.01 {
		t.Errorf("Negative minutes elapsed test: Expected %.3f, got %.3f", expected, result)
	}
}

// TestCalculateCurrentSetpointFormula verifies the mathematical formula
func TestCalculateCurrentSetpointFormula(t *testing.T) {
	// Test the basic formula: current = start + (rate * time)
	testCases := []struct {
		start, rate, time, expected float64
	}{
		{0, 1, 1, 1},        // 0 + (1 * 1) = 1
		{5, 2, 3, 11},       // 5 + (2 * 3) = 11
		{10, -1, 5, 5},      // 10 + (-1 * 5) = 5
		{100, 0.5, 10, 105}, // 100 + (0.5 * 10) = 105
	}

	for i, tc := range testCases {
		result := CalculateCurrentSetpoint(tc.start, tc.rate, tc.time)
		if result != tc.expected {
			t.Errorf("Test case %d: Expected %.1f, got %.1f", i+1, tc.expected, result)
		}
	}
}

// TestCalculateCurrentSetpointConsistency tests that the function is consistent
func TestCalculateCurrentSetpointConsistency(t *testing.T) {
	// Same inputs should always produce same outputs
	start := 15.5
	rate := 0.25
	time := 42.3

	result1 := CalculateCurrentSetpoint(start, rate, time)
	result2 := CalculateCurrentSetpoint(start, rate, time)
	result3 := CalculateCurrentSetpoint(start, rate, time)

	if result1 != result2 || result2 != result3 {
		t.Errorf("Function is not consistent: got %.6f, %.6f, %.6f", result1, result2, result3)
	}

	// Test that order of operations doesn't matter for equivalent calculations
	// (start + rate*time) should equal (start + time*rate)
	result4 := start + (rate * time)
	if abs(result1-result4) > 0.000001 {
		t.Errorf("Mathematical inconsistency: function result %.6f vs manual calculation %.6f", result1, result4)
	}
}

// Helper function to calculate absolute value
func abs(x float64) float64 {
	if x < 0 {
		return -x
	}
	return x
}

// TestCalculateCurrentSetpointRealWorldScenarios tests realistic scenarios
func TestCalculateCurrentSetpointRealWorldScenarios(t *testing.T) {
	scenarios := []struct {
		name           string
		description    string
		startSetpoint  float64
		rampRate       float64
		minutesElapsed float64
		expected       float64
	}{
		{
			name:           "Morning warmup",
			description:    "Temperature rising from 18°C to 24°C over 2 hours",
			startSetpoint:  18.0,
			rampRate:       0.05, // (24-18)/(2*60) = 6/120 = 0.05 per minute
			minutesElapsed: 60.0, // After 1 hour
			expected:       21.0, // 18 + (0.05 * 60) = 21°C
		},
		{
			name:           "Evening cooldown",
			description:    "Temperature dropping from 24°C to 18°C over 3 hours",
			startSetpoint:  24.0,
			rampRate:       -0.0333, // (18-24)/(3*60) = -6/180 = -0.0333 per minute
			minutesElapsed: 90.0,    // After 1.5 hours
			expected:       21.003,  // 24 + (-0.0333 * 90) ≈ 21.003°C
		},
		{
			name:           "Humidity adjustment",
			description:    "Humidity rising from 60% to 70% over 30 minutes",
			startSetpoint:  60.0,
			rampRate:       0.333, // (70-60)/30 = 10/30 = 0.333 per minute
			minutesElapsed: 15.0,  // After 15 minutes
			expected:       65.0,  // 60 + (0.333 * 15) ≈ 65%
		},
		{
			name:           "Quick adjustment",
			description:    "Fast setpoint change over 5 minutes",
			startSetpoint:  20.0,
			rampRate:       2.0,  // 10 degree change over 5 minutes = 2 per minute
			minutesElapsed: 2.5,  // After 2.5 minutes
			expected:       25.0, // 20 + (2.0 * 2.5) = 25°C
		},
	}

	for _, scenario := range scenarios {
		t.Run(scenario.name, func(t *testing.T) {
			result := CalculateCurrentSetpoint(scenario.startSetpoint, scenario.rampRate, scenario.minutesElapsed)

			if abs(result-scenario.expected) > 0.01 {
				t.Errorf("%s: Expected %.3f, got %.3f", scenario.description, scenario.expected, result)
			} else {
				t.Logf("%s: ✓ %.1f°C/%% after %.1f minutes", scenario.description, result, scenario.minutesElapsed)
			}
		})
	}
}

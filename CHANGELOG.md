# Changelog

All notable changes to the Program Manager project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [2.0.0] - 2025-06-23

### Added
- **Interactive CEB Testing System** - Complete interactive test with custom inputs and preset scenarios
  - Hot Day preset (high temperatures requiring cooling)
  - Cold Day preset (low temperatures requiring heating)  
  - High Humidity preset (high humidity requiring dehumidification)
  - Custom input mode for manual testing
  - All 9 CEB outputs displayed with analysis
- **Weather Simulation System** - Realistic weather data generation for testing
  - Daily temperature cycles with realistic patterns
  - Light level simulation with dawn/dusk transitions
  - Integration with CEB system for realistic testing
  - Multiple startup methods (script, make, manual)
- **Enhanced CEB Configuration** - New Redis key format and structure
  - Updated Redis topics for better organization
  - Configurable setpoint names support
  - Enhanced input/output key mapping
- **Comprehensive Commands Reference** - COMMANDS.md as primary documentation
  - All build and run commands
  - Complete workflow examples
  - Troubleshooting guides
  - Redis data management
- **Clock Program Enhancements** - Sunrise/sunset based scheduling
  - Uses actual sunrise/sunset times (not civil twilight)
  - Automatic startup with main program
  - Hyderabad, India coordinates (17.407104033722273, 78.38716849147556)
- **Modular Architecture** - Separated concerns into focused modules
  - `ceb/` - Climate Energy Balance logic
  - `clock/` - Astronomical calculations
  - `diurnal/` - Time-based setpoint management
  - `monitoring/` - Real-time monitoring
  - `redis/` - Redis operations
  - `types/` - Data structures

### Changed
- **CEB System** - Complete rewrite with enhanced functionality
  - 9 official outputs as per requirements
  - Verbose logging for detailed analysis
  - Improved PID control logic
  - Better integration with weather data
- **Redis Key Format** - Updated to new standardized format
  - `hub:1:zone:1:instance:1:setpoint:name` format
  - Consistent naming across all components
  - TTL support for setpoint keys (60 minutes)
- **Configuration System** - Enhanced JSON configurations
  - `cebConfigEnhanced.json` with new Redis topics
  - `diurnalConfigEnhanced.json` with improved structure
  - `clockConfig.json` with timezone and location settings
- **Build System** - Improved Makefile with better targets
  - `make all` - Build everything
  - `make run-ceb-test` - Interactive CEB testing
  - `make monitor-ceb` - Monitor CEB integration
  - `make test-weather` - Test weather data
- **Documentation Structure** - Consolidated and simplified
  - COMMANDS.md as primary reference
  - Simplified README.md with quick start
  - Essential technical docs preserved

### Removed
- **Legacy CEB Implementation** - Old cebConfig.json format
- **Redundant Documentation** - Multiple overlapping docs consolidated
  - TEST_COMMANDS.md (merged into COMMANDS.md)
  - CEB_TEST_SUMMARY.md (merged into COMMANDS.md)
  - weather_simulation_guide.md (merged into COMMANDS.md)
  - ceb_interactive_test_guide.md (merged into COMMANDS.md)
  - And 8+ other redundant documentation files
- **Broken Test Files** - Non-functional test utilities
  - test/test_ceb.go (replaced by interactive test)
- **Unused Scripts** - Redundant demo and documentation files
- **Legacy Redis Topics** - Old key formats no longer supported

### Fixed
- **Build Issues** - All build targets now work correctly
- **Redis Integration** - Proper key format and TTL handling
- **Clock Data** - Correct sunrise/sunset calculations
- **CEB Calculations** - Accurate 9-output generation
- **Weather Integration** - Proper data flow from simulation to CEB

### Technical Improvements
- **Code Organization** - Clear module separation and responsibilities
- **Error Handling** - Improved error messages and recovery
- **Testing** - Interactive testing replaces broken unit tests
- **Configuration** - Centralized and validated configuration loading
- **Redis Operations** - Encapsulated and reusable Redis functions
- **Monitoring** - Real-time monitoring of system integration

## [1.0.0] - 2025-06-22

### Added
- Initial Program Manager implementation
- Basic CEB (Climate Energy Balance) system
- Diurnal setpoint management
- Clock program with basic time functions
- Redis integration for data storage
- Configuration file support

### Features
- Program selection menu
- Basic setpoint scheduling
- Temperature and humidity control
- Redis-based data persistence
- JSON configuration files

---

## Migration Guide

### From 1.x to 2.0

1. **Update Configuration Files**
   - Replace `cebConfig.json` with `cebConfigEnhanced.json`
   - Update Redis key formats to new standard
   - Review coordinate settings (now defaults to Hyderabad)

2. **Update Build Process**
   - Use `make all` instead of individual builds
   - Use `make run-ceb-test` for CEB testing
   - Remove references to old test files

3. **Update Documentation References**
   - Use COMMANDS.md as primary reference
   - Remove bookmarks to deleted documentation files
   - Update any scripts referencing old file paths

4. **Redis Key Updates**
   - Old: `hub:1:zone:z1:instance:i1:setpoint:name`
   - New: `hub:1:zone:1:instance:1:setpoint:name`

5. **Testing Workflow**
   - Replace old CEB tests with interactive testing
   - Use weather simulation for realistic testing
   - Follow new workflow examples in COMMANDS.md

## Support

For questions about changes or migration:
- See COMMANDS.md for complete usage instructions
- Check docs/ folder for technical details
- Review configuration examples in config/ folder

## Contributors

- Enhanced CEB system implementation
- Weather simulation development
- Interactive testing system
- Documentation consolidation
- Modular architecture design

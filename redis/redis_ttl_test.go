package redis

import (
	"context"
	"testing"
	"time"

	"program-manager/types"
)

// TestSetpointTTL tests that setpoints are stored with 60-minute TTL
func TestSetpointTTL(t *testing.T) {
	// Initialize Redis for testing
	Initialize()
	ctx := context.Background()

	// Test SetFloat TTL
	t.Run("SetFloat TTL", func(t *testing.T) {
		testKey := "test:setpoint:ttl"
		testValue := 25.5

		err := SetFloat(ctx, testKey, testValue)
		if err != nil {
			t.Fatalf("Failed to set float: %v", err)
		}

		// Check TTL
		ttl := Client.TTL(ctx, testKey).Val()
		if ttl <= 0 {
			t.Fatalf("TTL not set or key doesn't exist: %v", ttl)
		}

		// TTL should be approximately 60 minutes (allowing for small timing differences)
		expectedTTL := 60 * time.Minute
		if ttl < 59*time.Minute || ttl > expectedTTL {
			t.Errorf("TTL is not 60 minutes: got %v, expected ~%v", ttl, expectedTTL)
		}

		t.Logf("SetFloat TTL: %v (%.1f minutes)", ttl, ttl.Minutes())

		// Clean up
		Client.Del(ctx, testKey)
	})

	// Test StoreSetpoints TTL
	t.Run("StoreSetpoints TTL", func(t *testing.T) {
		instance := types.DiurnalInstance{
			InstanceId: "999", // Use unique ID for testing
			HubId:      "1",
			ZoneId:     "1",
			Enabled:    true,
		}

		period := types.Period{
			PeriodId: "1",
			Setpoints: map[string]float64{
				"testHeatingTarget": 20.0,
				"testCoolingTarget": 24.0,
			},
		}

		err := StoreSetpoints(ctx, instance, period)
		if err != nil {
			t.Fatalf("Failed to store setpoints: %v", err)
		}

		// Check TTL for each setpoint
		testKeys := []string{
			"hub:1:zone:1:instance:999:setpoint:testHeatingTarget",
			"hub:1:zone:1:instance:999:setpoint:testCoolingTarget",
		}

		for _, key := range testKeys {
			ttl := Client.TTL(ctx, key).Val()
			if ttl <= 0 {
				t.Errorf("TTL not set for key %s: %v", key, ttl)
				continue
			}

			// TTL should be approximately 60 minutes
			expectedTTL := 60 * time.Minute
			if ttl < 59*time.Minute || ttl > expectedTTL {
				t.Errorf("TTL for %s is not 60 minutes: got %v, expected ~%v", key, ttl, expectedTTL)
			}

			t.Logf("Key %s TTL: %v (%.1f minutes)", key, ttl, ttl.Minutes())

			// Verify value can be retrieved
			value, err := GetFloat(ctx, key)
			if err != nil {
				t.Errorf("Failed to retrieve value for %s: %v", key, err)
			} else {
				t.Logf("Retrieved %s = %.1f", key, value)
			}
		}

		// Clean up
		for _, key := range testKeys {
			Client.Del(ctx, key)
		}
	})
}

// TestTTLBehavior tests TTL behavior over time (quick test)
func TestTTLBehavior(t *testing.T) {
	Initialize()
	ctx := context.Background()

	testKey := "test:ttl:behavior"
	testValue := 42.0

	// Store value with TTL
	err := SetFloat(ctx, testKey, testValue)
	if err != nil {
		t.Fatalf("Failed to set float: %v", err)
	}

	// Check initial TTL
	initialTTL := Client.TTL(ctx, testKey).Val()
	t.Logf("Initial TTL: %v", initialTTL)

	// Wait a short time and check TTL again
	time.Sleep(2 * time.Second)
	laterTTL := Client.TTL(ctx, testKey).Val()
	t.Logf("TTL after 2 seconds: %v", laterTTL)

	// TTL should have decreased
	if laterTTL >= initialTTL {
		t.Errorf("TTL should have decreased: initial=%v, later=%v", initialTTL, laterTTL)
	}

	// Value should still be retrievable
	value, err := GetFloat(ctx, testKey)
	if err != nil {
		t.Errorf("Failed to retrieve value: %v", err)
	} else if value != testValue {
		t.Errorf("Value mismatch: got %v, expected %v", value, testValue)
	}

	// Clean up
	Client.Del(ctx, testKey)
}

// TestAllSetpointTTLs tests that all setpoint-related storage functions use 60-minute TTL
func TestAllSetpointTTLs(t *testing.T) {
	Initialize()
	ctx := context.Background()

	// Test modern setpoint format TTL
	t.Run("Modern Setpoint Format TTL", func(t *testing.T) {
		instance := types.DiurnalInstance{
			InstanceId: "888",
			HubId:      "1",
			ZoneId:     "1",
			Enabled:    true,
		}

		period := types.Period{
			PeriodId: "1",
			Setpoints: map[string]float64{
				"testSetpoint": 22.0,
			},
		}

		err := StoreSetpoints(ctx, instance, period)
		if err != nil {
			t.Fatalf("Failed to store setpoints: %v", err)
		}

		// Check modern setpoint key TTL
		modernKey := "hub:1:zone:1:instance:888:setpoint:testSetpoint"
		ttl := Client.TTL(ctx, modernKey).Val()

		if ttl <= 0 {
			t.Errorf("Modern setpoint key TTL not set: %v", ttl)
		} else if ttl < 59*time.Minute || ttl > 60*time.Minute {
			t.Errorf("Modern setpoint key TTL is not 60 minutes: got %v", ttl)
		} else {
			t.Logf("Modern setpoint key TTL: %v (%.1f minutes)", ttl, ttl.Minutes())
		}

		// Clean up
		Client.Del(ctx, modernKey)
	})

	// Test SetString TTL (used by monitoring system)
	t.Run("SetString TTL", func(t *testing.T) {
		testKey := "test:string:ttl"
		testValue := "test-value"

		err := SetString(ctx, testKey, testValue)
		if err != nil {
			t.Fatalf("Failed to set string: %v", err)
		}

		ttl := Client.TTL(ctx, testKey).Val()
		if ttl <= 0 {
			t.Errorf("SetString TTL not set: %v", ttl)
		} else if ttl < 59*time.Minute || ttl > 60*time.Minute {
			t.Errorf("SetString TTL is not 60 minutes: got %v", ttl)
		} else {
			t.Logf("SetString TTL: %v (%.1f minutes)", ttl, ttl.Minutes())
		}

		// Clean up
		Client.Del(ctx, testKey)
	})

}

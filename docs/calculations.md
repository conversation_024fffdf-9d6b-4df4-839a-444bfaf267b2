# Comprehensive Greenhouse Environmental Control System:

# Logic, Application, and Detailed Calculations

**1. Introduction**

Modern agriculture is undergoing a significant transformation, with Controlled
Environment Agriculture (CEA) at its forefront. Greenhouses, vertical farms, and other
CEA structures represent a important shift towards producing crops with greater
precision, predictability, and resource efficiency. The imperative for such systems
stems from a confluence of global challenges: increasing population, shrinking arable
land, climate change impacts on traditional farming, and a growing consumer demand
for year-round availability of fresh, high-quality produce with a reduced
environmental footprint. This document provides an in-depth exploration of an
advanced greenhouse environmental control system, a critical enabler of successful
CEA. It meticulously details the core logic modules—Ramp Control for smooth
setpoint transitions, Proportional-Integral-Derivative (PID) Control for accurate
parameter maintenance, Wind Direction Logic for intelligent ventilation, and Vent
Program Control for precise actuator management.

The integrated operation of these modules is illustrated through a comprehensive
"start-to-finish" example of rose cultivation, a high-value crop demanding exacting
environmental conditions. A key focus throughout this document is to transparently
demonstrate the specific calculations involved in Ramp and PID control at various
stages of crop growth and in response to diurnal environmental changes. This
showcases the system's dynamic responsiveness and its capacity to adapt to both
programmed setpoints and unpredictable external influences. Beyond roses, the
principles and systems discussed are applicable to a wide array of horticultural crops,
from leafy greens and fruiting vegetables to medicinal plants and ornamentals.

The ultimate goal of such a sophisticated control system is multifaceted: to create
and maintain optimal growing conditions tailored to specific crop needs, thereby
significantly improving crop yield, consistency, and quality attributes (e.g., size, color,
flavor, shelf-life). Furthermore, by precisely managing inputs like energy for
heating/cooling, water for irrigation, and CO2 for enrichment, these systems enhance
operational efficiency and contribute to the sustainability of agricultural practices.
They also play a crucial role in minimizing pest and disease pressure by maintaining
environments less conducive to pathogens, potentially reducing reliance on chemical
interventions. As we delve into the intricacies of this technology, it becomes clear
that automated environmental control is not merely an adjunct to modern greenhouse

operations but its very backbone, enabling growers to achieve unprecedented levels
of control and productivity in the face of increasingly complex agricultural
landscapes. The discussion will also touch upon parameters beyond the primary
focus, such as light spectrum management, root zone temperature control, and
substrate moisture monitoring, which are often integrated into holistic CEA control
strategies.

**2. Core Control System Architecture (Conceptual)**

The described control system is envisioned as a modular, distributed, and software-
driven architecture, designed for robustness, flexibility, and scalability. In this
paradigm, distinct software programs, or modules, each take responsibility for a
specific aspect of environmental monitoring, decision-making, or control. These
modules interact by publishing data they generate and subscribing to data they
require from a central, shared data repository or message bus, referred to
conceptually in this document as the "Broadcast Cache." This publish-subscribe
model decouples the modules, allowing them to be developed, updated, and
maintained independently, and facilitates the addition of new functionalities or the
integration of new sensor and actuator technologies with minimal disruption to the
overall system.

Key conceptual programs forming the pillars of this architecture include:

```
● Program Manager: This module acts as the high-level orchestrator. It is
responsible for loading specific operational strategies, often termed "recipes" or
"growth plans," which are tailored for different crops, specific cultivars, or
distinct growth phases (e.g., propagation, vegetative, flowering, fruiting). These
recipes, potentially stored as structured files (e.g., XML, JSON, YAML) or in a
database, would contain detailed configurations for other programs, including
target setpoints for various parameters, ramp durations, PID tuning constants,
and rules for the Trigger Program. For instance, a "Tomato Fruiting" recipe might
specify different day/night temperature setpoints and humidity targets than a
"Lettuce Vegetative" recipe.
● Clock Program: Fundamental to any time-based control, this program provides
an accurate and reliable system-wide time base. It delivers the current time and
date, and also calculates and broadcasts critical temporal events such as
astronomical or civil dawn and dusk times, solar noon, and potentially specific
photoperiod markers. For advanced systems, it would ideally synchronize with a
Network Time Protocol (NTP) server to maintain high accuracy and automatically
```

handle complexities like leap seconds or daylight saving time adjustments if
relevant to the operational region, ensuring all scheduled events and data logs
are correctly time-stamped.
● **I/O Program (Input/Output Program):** This module serves as the critical bridge
between the digital control system and the physical greenhouse environment. It
manages bidirectional communication with a diverse array of sensors and
actuators.
○ **Sensors:** It reads data from various sensors, which could range from simple
analog sensors (e.g., thermistors, humidity sensors outputting a voltage or
current signal) and digital sensors (e.g., on/off status for rain detection) to
"smart" sensors with their own microprocessors that might provide pre-
processed data or communicate over digital buses (e.g., Modbus, SDI-12).
This includes sensors for air temperature, relative humidity, CO
concentration, photosynthetically active radiation (PAR), outdoor weather
parameters, substrate moisture, EC, pH, and potentially plant-based sensors
(e.g., leaf temperature, stem diameter). The I/O Program would also handle
aspects like sensor polling schedules, data unit conversions, and initial fault
detection (e.g., sensor out-of-range). Regular calibration routines, either
manual or semi-automated, would interface with this program to ensure data
accuracy.
○ **Actuators:** It sends control commands to various actuators, such as vent
motors (on/off, or modulating for precise positioning), heater stages (on/off
for different capacities), modulating heating valves, circulation fans (on/off,
variable speed), irrigation solenoids, CO2 enrichment valves, supplemental
lighting systems (on/off, dimmable), and motors for shade/thermal screens. It
translates the logical commands from higher-level programs (e.g., "set vent
opening to 35%") into the specific electrical signals required by the actuator
hardware.
● **Weather Monitoring Program:** This program is dedicated to processing raw
data obtained from external weather sensors. Its functions include:
○ **Data Aggregation and Averaging:** Calculating moving averages (e.g., simple
moving average over 10 minutes for temperature, exponential moving
average for rapidly changing wind speed) to smooth out short-term
fluctuations and provide more stable inputs for control decisions.
○ **Noise Filtering:** Applying digital filtering techniques to remove noise from
sensor signals.
○ **Sensor Validation:** Performing plausibility checks (e.g., outdoor temperature

cannot be 60°C), comparing readings from redundant sensors if available,
and flagging potentially faulty sensor data.
○ **Derived Parameters:** Calculating useful derived parameters, such as Vapor
Pressure Deficit (VPD) from temperature and humidity, or wind chill factor.
○ **Microclimate Awareness:** While primarily focused on external weather,
advanced versions might also incorporate data from multiple internal zones to
understand microclimate variations within a large greenhouse, which can be
influenced by external conditions like wind direction impacting different
sections of the greenhouse differently.
● **Broadcast Information Program (Cache):** This is not a program in the
traditional sense but rather the conceptual central data exchange mechanism. It
could be implemented using technologies like an MQTT (Message Queuing
Telemetry Transport) broker, a Redis in-memory data store, a shared database,
or other forms of inter-process communication. Its key characteristics are:
○ **Data Publication:** Modules publish their output data (e.g., current sensor
readings, calculated setpoints, actuator statuses) to specific "topics" or
"keys" within the cache.
○ **Data Subscription:** Modules subscribe to the topics/keys relevant to their
input needs.
○ **Decoupling:** This ensures that modules do not need direct knowledge of
each other, only of the data structure within the cache.
○ **Data Timeliness:** Data in the cache would typically be time-stamped, and
mechanisms might exist to indicate data "freshness" or quality (e.g., a flag
indicating if a sensor reading is stale or suspect).
● **Diurnal Setpoints Program:** This program is responsible for managing the
desired environmental conditions over a 24-hour cycle, according to the active
recipe from the Program Manager. It defines multiple time periods within a day
(e.g., "Early Morning," "Day," "Afternoon," "Evening," "Night") and assigns
specific target setpoints for parameters like temperature, humidity, and CO2 to
each period. Crucially, it incorporates the **Ramp Control Logic** (detailed later) to
ensure smooth transitions between the setpoints of adjacent periods. Different
parameters might have different ramp profiles; for example, temperature might
follow a linear ramp, while humidity might require a more gradual S-curve ramp to
avoid condensation issues.
● **Climate Energy Balance Program:** This module can be considered the "brain"
for climate regulation. It continuously receives the Active_Setpoint values from
the Diurnal Setpoints Program and the current Process_Variable readings from

the I/O Program (via the Cache). It then employs **PID Controllers** (detailed later)
for each controlled parameter (e.g., one PID for temperature, another for
humidity). Based on the error between setpoint and actual conditions, the PID
controllers calculate the necessary corrective effort. This program then
translates these efforts into percentage requirements for heating, cooling (via
ventilation or active cooling systems), ventilation for air exchange, humidification,
or dehumidification. It might also incorporate energy-saving strategies, such as
prioritizing passive cooling (ventilation with cool outdoor air) over energy-
intensive active cooling (chillers), or using thermal screens to reduce heating
demand at night.
● **Vent Control & Equipment Program:** This highly critical program acts as the
intelligent dispatcher of commands to physical equipment. It takes the
percentage requirements from the Climate Energy Balance Program (e.g., "30%
heating needed," "60% ventilation needed") and translates them into concrete
actions. Its responsibilities include:
○ Interpreting **Wind Direction Logic** and **Rain Override** inputs to modify vent
commands.
○ Managing **equipment staging** : If multiple heater units or fan groups exist, it
decides which ones to activate and in what sequence to meet the demand
efficiently and avoid excessive wear. For example, activating one heater stage
for a small heating demand, and multiple stages for a larger demand. Similarly
for variable speed fans.
○ Implementing **interlocks** : Ensuring safe and logical operation, e.g., preventing
CO2 enrichment if vents are open beyond a certain threshold (to avoid
wasting CO2), or ensuring circulation fans are running when fogging systems
are active.
○ Controlling supplemental lighting (on/off, dimming levels based on DLI targets
from the Diurnal Setpoints Program and PAR sensor readings).
○ Managing thermal/shade screens (deploying/retracting based on
temperature, light levels, or time of day).
● **Trigger Program:** This module provides a flexible mechanism for users to define
custom, event-based rules that operate in parallel with the main control loops.
These rules typically follow an "IF condition THEN action" structure.
○ **Conditions** can be based on sensor readings exceeding thresholds,
equipment status, time of day, or alarms from other programs.
○ **Actions** could include sending alerts to operators (e.g., email, SMS), logging
specific events, activating or deactivating certain equipment, or even

```
changing setpoints in the Diurnal Setpoints Program under specific
circumstances.
○ Examples: "IF zone1_humidity_percent > 85% AND current_period_name is
'Night' FOR 30 minutes THEN activate high_humidity_alarm AND briefly
increase minimum_vent_position by 5% if not raining." Another example: "IF
nutrient_tank_ph > 6.5 THEN send 'pH High Alert' to operator AND activate
ph_down_dosing_pump for 10 seconds."
```

This modular architecture, centered around a broadcast cache, provides a robust and
adaptable framework for implementing sophisticated greenhouse environmental
control.

**3. Detailed Control Logic Modules**

The efficacy of the greenhouse control system hinges on the precise and coordinated
operation of several key logic modules. These modules handle everything from
smooth setpoint transitions to nuanced actuator responses based on real-time
conditions.

**3.1. Ramp Control Logic 📈**

Purpose:
The Ramp Control Logic is a foundational element for ensuring plant health and system
stability. Its primary purpose is to smoothly transition an environmental setpoint (such as air
temperature, relative humidity, or CO2 concentration) from its current operational value to a
new target value over a user-defined duration. Abrupt, step-changes in setpoints can impose
significant physiological stress on plants, potentially affecting growth rates, yield, and
susceptibility to diseases. Furthermore, sudden large changes can cause the physical control
system (heaters, vents, etc.) to overshoot the target significantly, leading to oscillations and
inefficient energy use as the system struggles to stabilize. Ramping provides a predictable,
gradual adjustment, allowing the plants to acclimatize and the physical systems to respond in
a more controlled and measured manner.
**Inputs:**

```
● Initial_Setpoint (S_initial): The value of the setpoint at the precise moment the
ramp sequence is initiated. This could be the setpoint from a previous stable
period or the current value of a previously active ramp.
● Target_Setpoint (S_target): The desired final setpoint value that the system aims
to achieve upon completion of the ramp.
● Ramp_Duration (T_duration): The total allocated time, typically specified in
minutes or hours, over which the transition from S_initial to S_target must occur.
```

```
The choice of duration depends on the parameter being ramped and the
sensitivity of the crop (e.g., temperature ramps are often slower than CO
ramps).
● Ramp_Interval (T_interval): The discrete time step, usually in seconds or minutes,
at which the Active_Setpoint is recalculated and updated. This interval dictates
the "granularity" of the ramp; shorter intervals result in a smoother, more
continuous-like transition but may impose a slightly higher computational load.
This T_interval also serves as the Delta_Time (Δt) for the discrete updates within
the ramp function itself.
● Elapsed_Ramp_Time (T_elapsed): The cumulative amount of time that has passed
since the current ramp sequence began. This value is incremented by T_interval
at each update step.
```

Detailed Logic and Calculation Formulas:
While the core idea is to distribute the total change in setpoint evenly over the ramp duration,
the most direct and flexible way to calculate the instantaneous active setpoint uses the rate
of change.

1. Total Change in Setpoint (ΔS_total):
   This represents the magnitude and direction of the required adjustment.
   ΔS_total = S_target - S_initial
2. Rate of Change (Rate_change):
   This crucial value determines how much the setpoint should change per unit of
   time (consistent with T_duration and T_elapsed units, e.g., °C/minute).
   Rate_change = ΔS_total / T_duration
   = (S_target - S_initial) / T_duration
3. Current Active Setpoint (S_active) at T_elapsed:
   The Active_Setpoint is the dynamic, intermediate target value that the PID
   controller (or other control logic) will aim to achieve at any given moment during
   the ramp. It is recalculated at each T_interval.
   S_active(T_elapsed) = S_initial + (Rate_change _ T_elapsed)
   = S_initial + ( ((S_target - S_initial) / T_duration) _ T_elapsed )

```
This calculation is performed repeatedly. After each calculation, T_elapsed is
updated (e.g., T_elapsed = T_elapsed + T_interval). The ramp continues until
T_elapsed equals or slightly exceeds T_duration. At this point, S_active is set to
S_target to ensure precision, and the ramp process for that parameter
concludes.
```

Ramp Profiles:
While the formula above describes a linear ramp (constant rate of change), more advanced
systems might implement non-linear ramp profiles for even smoother transitions, especially
for sensitive parameters or large setpoint changes:
● **Sigmoidal (S-curve) Ramps:** These profiles feature a slower rate of change at
the beginning and end of the ramp, with a faster rate in the middle. This "eases
into" and "eases out of" the transition, further reducing system shock and
potential for overshoot. Implementing this requires more complex mathematical
functions (e.g., logistic functions or polynomial interpolations) to define
S_active(T_elapsed).

Considerations for Multiple Simultaneous Ramps:
Greenhouses often require simultaneous ramping of multiple parameters (e.g., increasing
temperature while decreasing humidity during the morning transition). The control system
must manage these independently, each with its own S_initial, S_target, and T_duration.
Potential interactions or physical constraints (e.g., difficulty in rapidly increasing temperature
while also rapidly decreasing humidity if relying on dehumidifying via heating and venting)
might need to be considered in the overall climate strategy, potentially by slightly staggering
ramp start times or adjusting durations.
**Output:**

```
● Active_Setpoint (S_active): This dynamically changing setpoint value is
continuously published to the Broadcast Cache, where it is picked up by the PID
control logic (typically within the Climate Energy Balance Program) as its primary
target for the respective environmental parameter.
```

Generic Example of Linear Ramp Calculation (Revisited with Interval):
Suppose greenhouse temperature needs to ramp from S_initial = 18°C to S_target = 24°C over
T_duration = 60 minutes. The T_interval is 1 minute.
● ΔS_total = 24°C - 18°C = 6°C
● Rate_change = 6°C / 60 minutes = 0.1°C per minute

```
Elapsed Time (T_elapsed) Calculation Active Setpoint (S_active)
```

```
0 min (Start) 18 + (0.1 * 0) 18.0°C
```

```
1 min 18 + (0.1 * 1) 18.1°C
```

```
... ... ...
```

```
20 min 18 + (0.1 * 20) 20.0°C
```

```
... ... ...
```

```
59 min 18 + (0.1 * 59) 23.9°C
```

```
60 min (End) 18 + (0.1 * 60) (or directly set
to S_target)
```

```
24.0°C
```

This table illustrates how the Active_Setpoint smoothly progresses, providing a
constantly updated target for the PID controller.

**3.2. PID Control Logic 📈**

Purpose:
The Proportional-Integral-Derivative (PID) controller is a cornerstone of automated feedback
control systems, widely revered for its effectiveness and versatility. Within the greenhouse
context, its purpose is to continuously and automatically maintain a measured
Process_Variable (PV) (such as actual air temperature, relative humidity, or CO2 level) as
close as possible to the desired Active_Setpoint. It achieves this by calculating the difference
(error) between the setpoint and the PV, and then computing a corrective Control_Output
(MV) based on three distinct terms: Proportional, Integral, and Derivative. This calculated MV
then directs the action of an actuator (e.g., heater, vent motor, CO2 valve). Properly tuned
PID controllers enable precise environmental regulation, minimizing deviations, reducing
oscillations, and responding efficiently to disturbances.
**Inputs:**

```
● Active_Setpoint (S_active): The current target value for the process variable. This
is typically supplied by the Ramp Control Logic during transitions or is a fixed
value during stable periods, as defined by the Diurnal Setpoints Program.
● Process_Variable (PV): The actual, real-time measured value of the parameter
being controlled, obtained from a sensor (e.g., temperature reading from a
thermistor, humidity from a capacitive sensor).
● Kp (Proportional Gain): A dimensionless tuning parameter. It scales the
contribution of the current error to the control output. A higher Kp results in a
stronger and faster response to errors.
● Ki (Integral Gain): A tuning parameter (units: 1/time). It scales the contribution of
the accumulated past errors (the integral of error over time) to the control
output.
● Kd (Derivative Gain): A tuning parameter (units: time). It scales the contribution of
the rate of change of the error to the control output.
● Delta_Time (Δt): The fixed time interval at which the PID loop executes its
```

```
calculations (e.g., every 10 seconds, every 1 minute). This should be consistent
and appropriate for the dynamics of the process being controlled.
```

**Detailed Logic and Calculation Formulas (performed at each Δt):**

1. Error Calculation (e(t)):
   The error at the current time t is the fundamental input to the PID logic.
   e(t) = S_active - PV(t)

```
A positive error means the PV is below the setpoint (e.g., too cold); a negative
error means the PV is above the setpoint (e.g., too hot).
```

2. Proportional Term (P_term):
   This term provides an immediate corrective action that is directly proportional to
   the current magnitude and sign of the error.
   P_term = Kp \* e(t)

```
Role & Impact of Kp: The P-term is the primary driver of the control action.
* If Kp is too low, the system responds sluggishly to errors and disturbances.
* If Kp is too high, the system can become overly aggressive, leading to rapid
oscillations around the setpoint or even instability.
A P-only controller often results in a steady-state error, where the PV stabilizes
slightly off the setpoint, especially if there are persistent disturbances or system
biases.
```

3. Integral Term (I_term):
   This term addresses the limitations of P-only control by accumulating the error
   over time.
   Integral_Sum(t) = Integral_Sum(t-Δt) + (e(t) _ Δt) // Numerical integration (e.g.,
   Euler method)
   I_term = Ki _ Integral_Sum(t)

```
(where Integral_Sum(t-Δt) is the accumulated sum from the previous PID cycle).
Role & Impact of Ki: The I-term continuously pushes the control output to
eliminate any persistent (steady-state) error. As long as an error exists, the
Integral_Sum changes, thereby adjusting the I_term and the overall MV.
* If Ki is too low, steady-state errors are eliminated very slowly.
* If Ki is too high, it can cause the system to "wind up" (see below) or lead to
pronounced oscillations, as the integral action overcorrects.
○ Anti-Windup Strategies (Crucial Detail): A major challenge with the
integral term is "integral windup." This occurs when the actuator (e.g., a
```

```
heater) is already operating at its maximum capacity (100% power), but a
significant error still persists (e.g., it's very cold outside). The Integral_Sum
will continue to grow very large because the error isn't being resolved. When
the condition causing the large error eventually subsides (e.g., outdoor
temperature rises), this massive accumulated Integral_Sum will keep the
heater at full power for far too long, causing a substantial overshoot of the
temperature setpoint. Common anti-windup techniques include:
■ Integral Clamping: Freezing the Integral_Sum (i.e., stop accumulating
error) when the MV(t) reaches the actuator's saturation limit (0% or
100%). Integration resumes when the MV(t) moves back into the
controllable range.
■ Back-Calculation: If MV(t) is clamped due to saturation, the
Integral_Sum is recalculated backwards such that if the P and D terms
were as they are, the I_term would produce an MV(t) at the saturation
limit. This effectively "bleeds off" excessive integral action.
■ Conditional Integration: Only enabling integration when the PV is close
to the setpoint or when the error is small.
```

4. Derivative Term (D_term):
   This term provides a predictive or anticipatory action by considering the rate at
   which the error is currently changing.
   Rate_of_Error_Change = (e(t) - e(t-Δt)) / Δt
   D_term = Kd \* Rate_of_Error_Change

```
(where e(t-Δt) is the error recorded in the previous PID calculation cycle).
Role & Impact of Kd: The D-term helps to stabilize the system and improve its
transient response. It "dampens" oscillations by counteracting rapid changes in
error.
* If the error is rapidly approaching zero, the D-term can reduce the control
effort to prevent overshoot.
* If the error is rapidly moving away from zero, the D-term can increase the
control effort to respond more quickly.
* If Kd is too low, its stabilizing effect is minimal.
* If Kd is too high, the system can become overly sensitive to noise in the PV
measurement (as noise often involves rapid changes), leading to erratic actuator
behavior.
○ Derivative Kick Mitigation: A common issue is "derivative kick," which occurs
when there's a sudden change in the setpoint (even with ramping, the PID
```

```
might see the initial error change as very rapid if the ramp just started). Since
the D-term acts on e(t) - e(t-Δt), a setpoint change causes a large spike in
e(t), leading to a large, undesirable spike in the MV. A common solution is to
calculate the derivative based on the change in the Process_Variable (PV)
only, rather than the error:
Rate_of_PV_Change = (PV(t) - PV(t-Δt)) / Δt
D_term = -Kd * Rate_of_PV_Change (note the negative sign). This makes the
D-term reactive to how the process is behaving, not how the setpoint
changes.
```

5. Control Output / Manipulated Variable (MV(t)):
   The final control output is the algebraic sum of the Proportional, Integral, and
   Derivative terms. This value is typically scaled to match the operating range of
   the actuator (e.g., 0-100% for a valve or heater).
   MV(t) = P_term + I_term + D_term

```
The MV(t) is then clamped to the actuator's physical limits (e.g., 0% and 100%).
```

**Output:**

```
● Control_Output (MV): This computed value, representing the required intensity of
actuator action (e.g., percentage heater power, vent opening percentage, chiller
load), is published to the Broadcast Cache. It is then used by downstream
modules like the Vent Control & Equipment Program to command the physical
hardware.
```

Importance of Tuning:
The performance of a PID controller is critically dependent on the selection of the gain
parameters Kp, Ki, and Kd. This process, known as "tuning," aims to achieve:
● **Fast Response:** The system should react quickly to setpoint changes or
disturbances.
● **Minimal Overshoot:** The PV should not significantly exceed the setpoint after a
change.
● **Quick Settling Time:** The PV should stabilize at the setpoint quickly without
prolonged oscillations.
● Robustness: The controller should perform well despite variations in process
dynamics or levels of disturbance.
Tuning methods range from manual trial-and-error (e.g., Ziegler-Nichols methods
as a starting point) to more sophisticated software-based auto-tuning algorithms
that can analyze the process response to specific stimuli and calculate optimal
gain values. The ideal tuning parameters are specific to each individual control

```
loop (e.g., temperature control PID will have different gains than a CO2 control
PID) and the physical characteristics of the greenhouse and its equipment.
```

Feedforward Control Enhancement:
For systems with known, measurable disturbances that significantly affect the PV, PID control
can be enhanced with "feedforward" control. For example, solar radiation is a major
disturbance for greenhouse temperature. A feedforward component could proactively adjust
the cooling/ventilation output based on measured solar radiation levels, even before the
temperature PID detects an error. This preemptive action can significantly improve
disturbance rejection. The feedforward output is typically added to the PID MV.
**Generic Example of PID Calculation (Temperature Control):**

```
● Active_Setpoint (S_active) = 22.0°C
● Process_Variable (PV) = 21.0°C (current greenhouse temperature)
● Δt = 1 minute
● Tuned Gains (illustrative for a moderately responsive air temperature loop):
Kp=5.0, Ki=0.2 (1/min), Kd=1.0 (min).
● Previous state: e(t-Δt) = 0.8°C, Integral_Sum(t-Δt) = 15.0 °C·min, PV(t-Δt) =
21.2°C. (Using derivative on PV change).
```

1. e(t) = 22.0°C - 21.0°C = 1.0°C
2. P_term = 5.0 \* 1.0°C = 5.0 (scaled to % later)
3. Integral_Sum(t) = 15.0 °C·min + (1.0°C _ 1 min) = 16.0 °C·min
   I_term = 0.2 _ 16.0 = 3.
4. (Using derivative on PV change to avoid kick)
   Rate_of_PV_Change = (PV(t) - PV(t-Δt)) / Δt = (21.0°C - 21.2°C) / 1 min = -0.
   °C/min
   D_term = -Kd _ Rate_of_PV_Change = -1.0 _ (-0.2 °C/min) = 0.
5. MV_unscaled(t) = P_term + I_term + D_term = 5.0 + 3.2 + 0.2 = 8.
   If this MV_unscaled maps to a 0-100% heater output, and assuming a scaling
   factor where, for instance, an MV of 20 might mean 100% power, then 8.4 might
   translate to (8.4 / 20) \* 100 = 42% heater power. This MV (42%) would be the
   command.

**3.3. Wind Direction Logic 📈**

Purpose:
Natural ventilation is a primary and energy-efficient method for climate control in many
greenhouses. However, its effectiveness and potential risks are heavily influenced by external
wind conditions – specifically speed and direction. The Wind Direction Logic module is
designed to intelligently adapt ventilation strategies based on these real-time wind
parameters. Its objectives are to: optimize air exchange for temperature and humidity control,

prevent structural damage to vents or the greenhouse itself from excessive wind loads,
minimize the ingress of rain through open vents, manage internal air distribution patterns to
avoid drafts on sensitive plants, and enhance the overall energy efficiency of ventilation.
**Inputs:**

```
● Wind_Direction_Sensor_Reading: Provides the current wind direction, typically as
degrees from North (0-360°) or converted to cardinal/intercardinal points (N,
NNE, NE, ENE, E, etc.). Accuracy and responsiveness of this sensor are key.
● Wind_Speed_Sensor_Reading: Provides the current wind speed, usually in m/s,
km/h, or mph. Often, an averaged value (e.g., 1-minute or 10-minute average) is
used to filter out gusts, but peak gust speed might also be monitored for safety
overrides.
● Building_Orientation: A configured parameter defining the precise orientation of
the greenhouse structure (e.g., "Main ridge runs 0° North to 180° South") and the
location and orientation of its various vent banks (e.g., "West-facing side vents
from coordinates X1,Y1 to X2,Y2", "Continuous roof vent on East slope"). This
allows the system to map wind direction to specific vent exposures.
● Vent_Group_Configurations: Defines how individual physical vents are grouped
for operational control (e.g., "All Leeward Roof Vents," "Windward Side Vents
Zone A"). This allows for differentiated control based on exposure.
```

Process:
The Wind Direction Logic typically operates based on a set of configurable rules and
strategies rather than continuous mathematical formulas like PID. The process involves
several stages:

1. Determining Windward and Leeward Sides/Vents:
   Using the Wind_Direction_Sensor_Reading and the known Building_Orientation
   and Vent_Group_Configurations, the system dynamically identifies which vent
   groups are currently on the windward side (directly facing the oncoming wind)
   and which are on the leeward side (sheltered from direct wind). Vents on sides
   parallel to the wind might also be categorized differently.
2. **Windward Vent Strategy Implementation:**
   ○ **Pressure Management:** Windward vents experience positive pressure.
   Uncontrolled opening can lead to very high influx of outside air, potentially
   causing rapid temperature drops, physical damage to plants near the vents,
   or overloading the vent mechanisms.
   ○ Rule Example 1 (Speed-Based Reduction):
   IF Wind_Speed > 5 m/s AND Wind_Speed <= 10 m/s AND Vent_Group is
   'Windward_Side_Vents'

```
THEN Max_Opening_Limit_Wind_Based for Vent_Group = (1 - (Wind_Speed -
5)/10) * Configured_Max_Opening
(This rule would linearly reduce max opening from configured max at 5 m/s
down to 50% of configured max at 10 m/s).
○ Rule Example 2 (High Wind Closure):
IF Wind_Speed > 12 m/s AND Vent_Group is 'Windward_Roof_Vents'
THEN Target_Opening for Vent_Group = Safety_Gap_Opening (e.g., 2%) OR
0%
(Prioritizes closing vulnerable roof vents in high winds).
```

3. **Leeward Vent Strategy Implementation:**
   ○ **Suction Effect:** Leeward vents often experience negative pressure (suction),
   which can be very effective for drawing air out of the greenhouse. This often
   results in more gentle and uniform air exchange than relying solely on
   windward openings.
   ○ **Prioritization:** Under moderate to strong wind conditions where windward
   vents are restricted, the system may increase the target opening of leeward
   vents to compensate and still achieve the overall ventilation demand
   calculated by the PID controller.
   ○ Rule Example (Leeward Compensation):
   IF Windward_Vents_Restricted_Factor < 0.7 AND PID_Ventilation_Demand >
   30%
   THEN Target_Opening_Leeward_Vents = PID_Ventilation_Demand / (1 - (1 -
   Windward_Vents_Restricted_Factor)/Number_of_Leeward_Banks_Available)
   (This is a conceptual example; the logic would distribute the unmet demand
   to leeward vents, up to their max limits).
4. **Cross-Ventilation Strategy:**
   ○ For optimal air flushing, especially in wider greenhouses, specific
   combinations of windward and leeward vents, or side and roof vents, can be
   used.
   ○ Rule Example (Summer Cross-Flow):
   IF Outdoor_Temp < Indoor_Temp AND Wind_Direction is
   'Perpendicular_To_Side_Vents' AND Solar_Radiation > 400 W/m²
   THEN Open 'Lower_Windward_Side_Vents' to 50% AND Open
   'Upper_Leeward_Roof_Vents' to 75%
   (This encourages a natural convection current, with cool air entering low and
   warm air exiting high).
5. **Interaction with Rain Detection:**

```
○ If the Rain_Sensor_Status (from the Vent Control Program's inputs) indicates
rain, the Wind Direction Logic provides more stringent recommendations.
○ Rule Example (Rain on Windward Side):
IF is_raining == true AND Vent_Group is 'Windward_Vents'
THEN Recommended_Opening for Vent_Group = 0%
(Overrides other wind-based opening calculations for windward vents to
prevent rain ingress). Leeward vents might still be allowed a small "rain gap"
opening if humidity control is critical.
```

6. Consideration of Historical Data and Dominant Winds:
   For more advanced systems, historical wind data (wind roses for the specific
   location) can inform the baseline ventilation strategy. If a dominant wind
   direction is known for certain seasons, default vent biases can be pre-
   configured.
7. Impact of Nearby Structures/Terrain:
   While not directly an input, the initial configuration and rule-set for the Wind
   Direction Logic should ideally consider the local topography and presence of
   nearby buildings or windbreaks, as these can significantly alter actual wind
   patterns at the greenhouse site compared to general weather station data.

Output:
The Wind Direction Logic module typically does not command vents directly but provides
crucial advisory outputs to the Vent Program Control Logic:
● Vent_Adjustment_Factor_Windward: A numerical factor (e.g., 0.0 to 1.0)
indicating the permissible opening proportion for windward vents relative to the
PID request. E.g., 0.5 means windward vents should open to at most 50% of the
PID's calculated general ventilation need.
● Vent_Adjustment_Factor_Leeward: Similar factor for leeward vents (often 1.0, or
can be >1.0 if actively compensating for windward restrictions, up to physical
limits).
● Priority_Vent_Bank: A flag or indicator suggesting which vent bank(s) (e.g.,
"Leeward_Roof," "East_Side") should be prioritized for achieving the overall
ventilation target, especially when other banks are restricted.
● Max_Opening_Limit_Wind_Based (per Vent Group): A dynamically calculated
absolute maximum opening percentage for specific vent groups based on current
wind speed and direction, overriding any higher request from the PID.
● Recommended_Strategy_Flag: Could indicate a specific mode, e.g.,
"Maximize_Leeward_Flow," "Cross_Vent_Mode_A."

Generic Example (Revisited):

Wind from West (270°) at 10 m/s. Greenhouse has West-side (windward), East-side (leeward),
and North/South-roof (cross-wind) vents.
● Max_Opening_Limit_Wind_Based for "West_Side_Vents" might be set to 30%.
● Vent_Adjustment_Factor_Windward (for West) = 0.3.
● Max_Opening_Limit_Wind_Based for "North_Roof_Vents" and
"South_Roof_Vents" might be set to 60% (as they are less directly impacted but
still experience turbulence).
● Vent_Adjustment_Factor_Leeward (for East) = 1.0.
● Priority_Vent_Bank could be "East_Side_Vents" and then "Roof_Vents".
The Vent Control Program would then use these factors to modify the PID's
overall ventilation demand when calculating final commands for each vent group.

**3.4. Vent Program Control Logic 📈**

Purpose:
The Vent Program Control Logic serves as the ultimate decision-maker and executioner for all
greenhouse vent operations. It is a critical module that synthesizes requests from various
sources—primarily the ventilation demand calculated by the PID controllers (within the
Climate Energy Balance Program), strategic advice from the Wind Direction Logic, and
overriding inputs from safety systems (like rain sensors or emergency stops). Its core
purpose is to translate these, sometimes conflicting, inputs into precise, coordinated, and
safe commands for the physical vent motors, ensuring optimal environmental control while
protecting the greenhouse structure and crop.
Inputs:
This module requires a comprehensive set of inputs to make informed decisions:
● PID_Control_Output_Ventilation (MV_vent): The primary desired overall
ventilation level (typically a percentage, 0-100%) calculated by the PID
controllers for managing temperature and/or humidity.
● From **Wind Direction Logic** :
○ Vent_Adjustment_Factor_Windward
○ Vent_Adjustment_Factor_Leeward
○ Priority_Vent_Bank
○ Max_Opening_Limit_Wind_Based (for each relevant vent group)
● Rain_Sensor_Status: A boolean or enumerated input (e.g., Not Raining, Light Rain,
Heavy Rain) indicating precipitation.
● Emergency_Override_Status: A signal indicating manual overrides (e.g., operator
forces vents closed/open) or automated emergency states (e.g., "Storm Mode
Active" triggered by extreme wind speeds from Weather Monitoring Program, or
"Fire Alarm Active").
● Vent_Minimum_Opening_Percent (per Vent Group): A configured baseline

```
minimum opening percentage (e.g., 2-5%) for each vent group. This ensures a
minimal level of air exchange for plant respiration (CO2/Oxygen balance) and to
prevent excessive humidity buildup, even if the PID calls for zero ventilation. This
minimum can itself be overridden by safety conditions like heavy rain.
● Vent_Maximum_Opening_Percent (per Vent Group): The configured maximum
physical opening percentage (e.g., 90-100%) for each vent group, preventing
over-travel of mechanisms and potential damage.
● Vent_Group_Configurations: Detailed mapping of physical vent actuators to
logical operational groups, including their characteristics (e.g., type: roof/side,
opening speed, individual or continuous).
● Current_Vent_Position_Feedback (per Vent Group): Real-time feedback from
vent position sensors (if available) indicating the actual current opening of each
vent group. This is crucial for closed-loop position control and detecting
malfunctions.
● External_Temperature_Humidity: For some advanced minimum ventilation
strategies (e.g., humidity dump at night).
```

Process:
The decision-making process within the Vent Program Control Logic follows a strict hierarchy
of priorities, combined with intelligent distribution logic:

1. **Emergency/Safety Override Check (Highest Priority):**
   ○ If Emergency_Override_Status is active (e.g., "Storm Mode," "Manual Close"),
   all other logic is bypassed, and vents are moved to a predefined safe state
   (usually fully closed or a specific storm position). This is non-negotiable.
   ○ If Rain_Sensor_Status indicates significant rain:
   ■ Windward vents are typically commanded to close fully (0%) or to a very
   small "rain gap" (e.g., 1-2%) if some air exchange is absolutely critical and
   the vent design offers some rain protection at minimal opening.
   ■ Leeward vents might be allowed a slightly larger rain gap, or also closed,
   depending on greenhouse design and rain intensity.
   ■ Roof vents are almost always closed during rain.
   These rain overrides take precedence over PID demands and most wind
   logic, unless the wind logic itself calls for even tighter closure due to
   combined high wind and rain.
2. **Apply Wind Direction Logic Modifications:**
   ○ If no overriding safety condition is met, the program takes the PID_MV_vent
   as the baseline ventilation requirement.
   ○ It then applies the Vent_Adjustment_Factor_Windward and

```
Vent_Adjustment_Factor_Leeward to this baseline to determine modified
target openings for the respective vent banks.
■ Modified_Target_Windward = PID_MV_vent *
Vent_Adjustment_Factor_Windward
■ Modified_Target_Leeward = PID_MV_vent *
Vent_Adjustment_Factor_Leeward
○ These modified targets are then capped by any
Max_Opening_Limit_Wind_Based provided for those specific vent groups.
```

3. **Distribute Remaining Ventilation Demand (if applicable):**
   ○ If the greenhouse has multiple vent banks on the windward or leeward sides,
   or if a Priority_Vent_Bank strategy is active, the program intelligently
   distributes the overall (wind-adjusted) ventilation target.
   ○ For instance, if the PID_MV_vent is 50%, and windward vents are limited to an
   effective 20% due to wind factors, the system might try to achieve the
   remaining 30% ventilation demand by further opening leeward vents or other
   available vents, up to their respective limits.
   ○ This can involve complex logic to balance airflow, prevent excessive opening
   of any single bank, and respect priorities.
4. **Enforce Minimum Opening Requirements:**
   ○ For each vent group, if its calculated target opening (after PID, wind, and
   distribution logic) is less than its configured
   Vent_Minimum_Opening_Percent, and no safety override commands full
   closure, the target opening is raised to this minimum. This ensures continuous
   minimal air exchange.
   ○ This step needs to be carefully managed; for instance, during CO
   enrichment, this minimum might be temporarily set to 0% or a very low value
   to conserve CO2.
5. **Enforce Maximum Physical Opening Limits:**
   ○ The target opening for any vent group is finally clamped so it does not
   exceed its Vent_Maximum_Opening_Percent.
6. **Deadband Implementation:**
   ○ To prevent excessive "hunting" or frequent small adjustments of vent motors
   (which causes wear and consumes energy), a deadband is often applied. If
   the newly calculated target opening for a vent is only slightly different (e.g.,
   +/- 1 - 2%) from its Current_Vent_Position_Feedback, no command is sent until
   the difference exceeds the deadband.
7. **Actuation Command Generation:**

```
○ The final, validated target opening percentage for each vent group is
translated into a specific command for the I/O Program.
○ If position feedback is available, this becomes a closed-loop control: "Move
Vent Group A to 35%." The I/O program (or dedicated motor controller) then
manages the motor until the position sensor reports 35%.
○ If no position feedback exists (open-loop), commands might be time-based:
"Run Vent Group A motor in 'open' direction for X seconds" (where X is
calibrated to achieve an approximate percentage). This is less accurate.
```

8. **Staged Operation and Interlocks:**
   ○ **Staging:** For large vent banks or to manage electrical load, opening/closing
   might be staged. E.g., to achieve 60% opening, open one section to 60%,
   then the next, etc., or open all sections to 20%, then all to 40%, then all to
   60%.
   ○ **Interlocks with other equipment:** The Vent Program might receive status
   from other equipment. E.g., if a high-pressure fogging system (for
   humidification/cooling) is active, vents might be temporarily closed or set to a
   specific pattern to maximize fog effectiveness and prevent it from being
   immediately vented out. Similarly, it might signal CO2 emitters to pause if
   vents open beyond a certain point.

**Output:**

```
● Vent_Motor_Commands: A set of precise commands (e.g., target positions, run
durations) for each configured vent group, sent to the I/O Program for execution
by the physical vent actuators.
● Current_Vent_Strategy_Status: May also publish its current operational state
(e.g., "Rain Override Active," "Windward Vents Limited," "Normal PID Control") to
the Broadcast Cache for logging and display to the operator.
```

**Generic Example (Revisited and Enhanced):**

```
● PID_MV_vent = 60%.
● Wind from North at 8 m/s. North vents (roof & side) are windward, South vents
(roof & side) are leeward.
● Wind Logic provides: Max_Opening_Limit_Wind_Based for North Roof = 40%,
North Side = 50%. Factor_Windward = 0.6. Factor_Leeward = 1.0.
● Rain_Sensor_Status = Not Raining. Emergency_Override = Off.
● Vent_Minimum_Opening_Percent for all groups = 2%.
Vent_Maximum_Opening_Percent = 90%.
```

1. No emergency/rain override.

2. PID demand is 60%.
   ○ Target for North (windward) groups before absolute limit: 60% _ 0.6 = 36%.
   ■ North Roof: Capped by Max_Opening_Limit_Wind_Based at 40%. So,
   target is 36% (as it's < 40%).
   ■ North Side: Capped by Max_Opening_Limit_Wind_Based at 50%. So,
   target is 36% (as it's < 50%).
   ○ Target for South (leeward) groups: 60% _ 1.0 = 60%.
3. Minimums (2%) are met. Maximums (90%) are not exceeded by these targets.
4. Final commands (simplified, assuming no further distribution logic for this
   example):
   ○ North Roof Vents: Open to 36%.
   ○ North Side Vents: Open to 36%.
   ○ South Roof Vents: Open to 60%.
   ○ South Side Vents: Open to 60%.

This detailed process ensures that vent operations are responsive, safe, and
optimized for the current conditions and control objectives.

**3.5. Integration of Control Logic Components**

The true power and sophistication of an advanced greenhouse environmental control
system emerge not from the isolated operation of individual logic modules, but from
their seamless, dynamic, and hierarchical integration. This integration creates a
cohesive system capable of nuanced responses to a multitude of internal and
external variables. The Broadcast Cache serves as the central nervous system for this
integration, facilitating the flow of data and commands.

**The Hierarchical Flow of Control Decisions:**

1. **Strategic Imperative (Program Manager & Diurnal Setpoints):**
   ○ At the highest level, the **Program Manager** loads the overarching cultivation
   strategy (the "recipe") for the specific crop and its current growth phase.
   This recipe populates the **Diurnal Setpoints Program** with target values for
   temperature, humidity, CO2, light (DLI), etc., for different periods of the day
   (e.g., morning, midday, night).
   ○ The **Diurnal Setpoints Program** then takes charge of generating the
   instantaneous Active_Setpoint for each parameter. During transitions
   between periods (e.g., from night to day conditions), its **Ramp Control Logic**
   calculates a smoothly changing Active_Setpoint, publishing this dynamic
   target to the Broadcast Cache. During stable periods, it publishes the fixed

```
setpoint for that period.
```

2. **Core Regulation (Climate Energy Balance Program & PID Logic):**
   ○ The **Climate Energy Balance Program** continuously subscribes to these
   Active*Setpoint values and to the real-time Process_Variable readings (actual
   sensor data for temperature, humidity, CO2) from the I/O Program (via the
   Cache).
   ○ For each controlled parameter, a dedicated **PID Controller** within this
   program calculates the error (S_active - PV). Based on this error and its
   tuned Kp, Ki, and Kd gains, the PID computes a Control_Output (MV). This MV
   represents the \_required effort* or _demand_ for corrective action – e.g., "X%
   heating power needed," "Y% ventilation rate required," "Z% CO2 enrichment
   flow." These demand values are published to the Cache.
3. **Environmental Intelligence & Adaptation (Weather Monitoring & Wind**
   **Direction Logic):**
   ○ In parallel, the **Weather Monitoring Program** processes data from external
   sensors, providing reliable information about outdoor temperature, humidity,
   wind speed/direction, solar radiation, and rain status to the Cache.
   ○ The **Wind Direction Logic** subscribes to this weather data, particularly wind
   speed and direction. It analyzes this in conjunction with the configured
   greenhouse orientation and vent layout to determine current
   windward/leeward exposures. It then publishes its strategic advice—such as
   Vent_Adjustment_Factors, Max_Opening_Limits_Wind_Based, and
   Priority_Vent_Banks—to the Cache. This advice is tailored to optimize
   ventilation under current wind conditions while ensuring safety.
4. **Execution & Actuator Management (Vent Control & Equipment Program):**
   ○ The **Vent Control & Equipment Program** is the primary consumer of the
   PID-generated MV for ventilation and the advisory outputs from the Wind
   Direction Logic. It also subscribes to Rain_Sensor_Status and any
   Emergency_Override_Status.
   ○ It follows its hierarchical logic:
   ■ First, it checks for highest-priority overrides (Emergency, then Rain). If an
   override is active, it commands vents to a predefined safe state, largely
   disregarding other inputs for vent positioning.
   ■ If no safety overrides are active, it takes the PID's ventilation demand
   (MV_vent) and modifies it using the factors and limits provided by the
   Wind Direction Logic to calculate target positions for each vent group.
   ■ It then ensures these target positions respect configured

```
Vent_Minimum_Opening_Percent (for air quality) and
Vent_Maximum_Opening_Percent (for equipment protection), and applies
deadbands to prevent excessive motor activity.
■ Finally, it dispatches the precise, validated commands to the I/O Program
to physically move the vents.
○ Similar logic applies to other equipment: heater commands are based on the
heating MV from its PID, CO2 valve commands on the CO2 MV, etc., with
appropriate interlocks (e.g., CO2 off if vents are wide open).
```

Feedback Loops and Continuous Adjustment:
This entire process operates in a continuous closed loop. Changes in actuator positions (e.g.,
vents opening) affect the internal greenhouse environment. Sensors pick up these changes in
the Process_Variables. This new PV data feeds back into the PID controllers, which then
recalculate errors and adjust their MVs. External weather changes also feed into the system,
potentially altering PID demands and Wind/Rain logic outputs, leading to further adjustments.
This constant cycle of measurement, calculation, and actuation allows the system to
dynamically track setpoints and respond to disturbances.
Conflict Resolution:
Potential conflicts between control objectives (e.g., the need to ventilate for cooling might
conflict with the desire to maintain high CO2 levels, or the need to dehumidify might conflict
with the need to conserve heat) are typically resolved through:
● **Predefined Priorities:** The system can be configured with priorities (e.g.,
temperature control usually takes precedence over CO2 concentration if both
cannot be simultaneously optimized).
● **Weighted Objectives:** More advanced Climate Energy Balance programs might
use algorithms that try to satisfy multiple objectives by finding a compromise
based on user-defined weightings for each parameter.
● **Mode-Based Logic:** The Program Manager might switch the entire system into
different operational modes (e.g., "Energy Saving Mode," "Max Production
Mode") which alter these priorities and weightings.

This intricate web of interactions, governed by clear logic and priorities, is what
enables the greenhouse control system to function as an intelligent, autonomous
environmental manager.

**4. Comprehensive Application Example: Rose Cultivation in a Controlled
Greenhouse 📈📈**

This section provides a practical illustration of how the described integrated control
system dynamically manages the greenhouse environment throughout the typical
lifecycle of rose cultivation. We will pay particular attention to the "Flowering &

Harvest" phase for a detailed, step-by-step daily data flow, showcasing the Ramp
and PID calculations in action.

**4.1. Overall Rose Cultivation Lifecycle & System Adaptation**

Commercial rose cultivation is a sophisticated process involving several distinct
physiological phases. Each phase has unique environmental requirements to optimize
growth, flower development, and overall plant health. The greenhouse control system
adapts to these changing needs primarily by the **Program Manager** loading different
"recipes" or configurations. These recipes dictate the specific settings for the
**Diurnal Setpoints Program** (defining temperature, humidity, CO2 targets, and ramp
profiles for various times of day) and may also adjust PID tuning parameters within
the **Climate Energy Balance Program** or specific rules in the **Trigger Program**.

```
● Phase 1: Propagation / Rooting (Duration: e.g., 3-5 weeks if starting from
cuttings)
○ Environment Goal: To encourage rapid and robust root development on
newly struck cuttings and prevent desiccation before an effective root
system is established. Successful rooting is foundational for future plant
vigor.
○ Typical Diurnal Setpoints:
■ Temperature: Moderate and stable, e.g., Day 20-22°C (68-72°F), Night
18 - 20°C (64-68°F). Avoid large fluctuations.
■ Humidity: Very high relative humidity (RH), typically 85-95%, often
maintained using misting or fogging systems. This minimizes transpiration
from leaves that lack roots to replenish water.
■ Light: Lower light levels (e.g., PAR 100-200 μmol/m²/s) are preferred to
reduce stress and water loss. Shading screens are often employed.
■ CO2: Enrichment is generally not a priority as the primary focus is on root
initiation, not extensive photosynthesis. Ambient levels are usually
sufficient.
■ Air Movement: Gentle air circulation is needed to prevent stagnant,
overly saturated conditions that could promote fungal pathogens, but
strong drafts must be avoided.
○ System Action: The Program Manager loads a "Rose Propagation" recipe.
The Diurnal Setpoints Program implements high humidity targets. The Vent
Control & Equipment Program manages misting/fogging systems
frequently, keeps vents mostly closed or at minimal settings to maintain
humidity, and controls shade screens based on light sensor readings or a
```

time schedule. PID loops for temperature are active but operate within a
narrow band.
● **Phase 2: Vegetative Growth (Duration: e.g., 4-8 weeks after rooting, until
bud initiation)**
○ **Environment Goal:** To promote vigorous development of leaves, stems, and
overall plant biomass, building a strong framework for future flower
production.
○ **Typical Diurnal Setpoints:**
■ **Temperature:** Slightly warmer to encourage active growth, e.g., Day 22-
25°C (72-77°F), Night 18-20°C (64-68°F). A positive DIF (Day Temp > Night
Temp) often promotes stem elongation.
■ **Humidity:** Moderate to high RH (70-80%) to support turgor in rapidly
expanding tissues.
■ **Light:** Gradually increasing light levels, targeting an optimal Daily Light
Integral (DLI) for roses (e.g., 15-25 mol/m²/day) through natural light
supplemented by artificial lighting if needed.
■ **CO2:** Enrichment to 600-800 ppm (or higher, depending on light
availability and venting) during daylight hours significantly boosts
photosynthesis and growth rates.
■ **Air Movement:** Good air circulation is important to ensure uniform CO2
distribution and manage leaf boundary layer humidity.
○ **System Action: Program Manager** loads "Rose Vegetative" recipe. The
**Diurnal Setpoints Program** implements the new temperature, humidity, and
CO2 targets, with carefully managed ramps between day/night periods to
avoid stressing the actively growing plants. The **Vent Control & Equipment
Program** manages supplemental lighting, CO2 enrichment (interlocked with
ventilation status), and ensures adequate air circulation. PID controllers work
actively to maintain these dynamic setpoints.
● **Phase 3: Flower Bud Initiation & Development (Duration: e.g., 2-4 weeks
until buds are visible/maturing)**
○ **Environment Goal:** To encourage the plants to transition from purely
vegetative growth to initiating and developing flower buds. This is a critical
physiological shift.
○ **Typical Diurnal Setpoints:** Environmental cues can be variety-specific.
■ **Temperature:** Sometimes, a slight drop in average daily temperature or a
specific DIF strategy (e.g., a cool night period followed by a warm
morning) is used to promote bud initiation. Typical ranges might be Day

20 - 24°C (68-75°F), Night 16-18°C (61-64°F).
■ **Photoperiod:** For some rose varieties, photoperiod (day length) can
influence flowering. Supplemental lighting might be used to extend the
day length or provide night interruption lighting if required by the cultivar.
■ **Humidity:** Moderate RH (65-75%) is usually maintained.
■ **Light & CO2:** Remain important for providing the energy and carbon
building blocks for developing buds. DLI targets might be maintained or
slightly increased.
○ **System Action: Program Manager** loads "Rose Bud Initiation" recipe. The
**Diurnal Setpoints Program** implements the specific temperature regime. If
photoperiod control is critical, the **Vent Control & Equipment Program**
manages lighting schedules precisely (potentially using input from the
**Trigger Program** for complex sequences like specific durations of night
interruption).
● **Phase 4: Flowering & Harvest (Duration: e.g., 4-6 weeks from visible bud to
harvest, cyclical)**
○ **Environment Goal:** To maximize flower quality (size, color intensity, petal
count, stem length and strength, vase life) and yield, while actively preventing
common floral diseases like Botrytis cinerea (grey mold).
○ **Typical Diurnal Setpoints:**
■ **Temperature:** Optimal temperatures for flower development and color
expression, e.g., Day 21-24°C (70-75°F), Night 16-18°C (61-64°F). Cooler
night temperatures can enhance color and prolong bud development.
■ **Humidity:** Moderate RH (60-70%). This is a careful balance: too low can
stress flowers, too high significantly increases the risk of Botrytis,
especially as flowers open. Active dehumidification might be needed.
■ **Light:** High light levels (DLI often 20-30+ mol/m²/day) are crucial for
producing large, high-quality blooms.
■ **CO2:** Continued enrichment (600-800 ppm) supports photosynthesis
needed for flower growth.
■ **Air Movement:** Excellent air circulation is vital to manage humidity within
the plant canopy, reduce condensation on petals, and ensure uniform
temperature.
○ **System Action: Program Manager** loads "Rose Flowering" recipe. Precise
control of humidity via ventilation and possibly dehumidifiers (managed by
the **Vent Control & Equipment Program** ) becomes paramount. PID loops for
temperature and humidity are tightly controlled. Air circulation fans run

```
consistently. This phase is chosen for the detailed daily example below.
● Phase 5: Post-Harvest / Pruning / Rest (Cyclical, duration varies based on
production strategy)
○ Environment Goal: To allow plants to recover after a significant flowering
flush, to facilitate pruning operations, and sometimes to induce a brief rest
period before stimulating the next flowering cycle.
○ Typical Diurnal Setpoints:
■ Temperature: Often cooler to reduce plant metabolism, e.g., Day 18-
20°C (64-68°F), Night 15-17°C (59-63°F).
■ Humidity: Reduced RH may be targeted to harden plants or reduce
disease risk during periods of dense canopy post-pruning.
■ Light & CO2: May be reduced as active growth is not the primary goal.
○ System Action: Program Manager loads "Rose Rest Period" or "Rose
Pruning Recovery" recipe. The system maintains these less demanding
conditions.
```

**4.2. Detailed Daily Data Flow Example: Rose Flowering Phase (Phase 4)**

We will now walk through key moments of a typical day during the "Rose Flowering"
phase, illustrating the interaction of the control modules and detailing the Ramp and
PID calculations.

**Target Rose Flowering Conditions for this Example Day (as set in the "Rose
Flowering" recipe):**

```
● Day Temperature Target: 23.0°C
● Night Temperature Target: 17.0°C
● Day Humidity Target: 65.0%
● Night Humidity Target: 70.0%
● CO2 Target (Day, when photosynthetically active): 700 ppm
● Ramp Duration (for Temp & Humidity transitions between Day/Night periods): 60
minutes
● Ramp/PID Calculation Interval (T_interval for ramps, Δt for PID): 1 minute
● Greenhouse Configuration: North-South main axis, primary ventilation vents on
East and West facades/roof sections.
```

**Key Programs Involved:** Program Manager, Clock, I/O Program, Weather Monitoring
Program, Broadcast Information Program (Cache), Diurnal Setpoints Program,
Climate Energy Balance Program (housing PID controllers), Vent Control & Equipment
Program.

Scenario 1: 05:45 AM - Pre-Dawn, End of Night Period
The greenhouse environment has been stabilized at night-time setpoints. The system is
anticipating the transition to day conditions.
● **Clock Program** (publishes to Cache): current_time_hhmmss: "05:45:00",
dawn_time_hhmm: "06:00" (this is a pre-configured or astronomically calculated
value for the current date and location).
● **I/O Program** (Sensor data published to Cache):
○ zone1_temperature_c (PV_temp): 17.1°C (actual measured air temperature in
the zone)
○ zone1_humidity_percent (PV_hum): 69.5% (actual measured RH)
○ zone1_co2_ppm (PV_co2): 450 ppm (CO2 level, likely elevated slightly due to
plant respiration overnight in a relatively closed environment)
○ outdoor_temperature_c_raw: 15.0°C (raw reading from external temperature
sensor)
● **Weather Monitoring Program** (processes raw data and publishes to Cache):
○ outdoor_temperature_c_avg10m: 15.1°C (10-minute average outdoor
temperature)
○ wind_speed_mps_avg1m: 0.5 m/s (1-minute average wind speed, indicating
calm conditions)
○ is_raining: false (binary status from rain sensor)
● **Diurnal Setpoints Program** (currently in "Night_Roses" period as defined in the
"Rose Flowering" recipe):
○ Active_Setpoint_Temperature (S_active_temp): 17.0°C (No ramp is active for
temperature at this moment).
○ Active_Setpoint_Humidity (S_active_hum): 70.0% (No ramp is active for
humidity).
○ Active_Setpoint_CO2 (S_active_co2): 400 ppm (Target for night, essentially
allowing it to float or ensuring it doesn't drop too low if some minimal venting
occurs).
○ Publishes to Cache: active_setpoints: { "temperature_target_c": 17.0,
"humidity_target_percent": 70.0, "co2_target_ppm": 400 }, is_ramping: false,
current_period_name: "Night_Roses".
● **Climate Energy Balance Program (PID Calculation for Temperature):**
○ **Inputs for Temperature PID:**
■ S_active_temp = 17.0°C
■ PV_temp = 17.1°C
■ Δt = 1 minute
○ Previous error e_temp(t-Δt) and Integral_Sum_temp(t-Δt) are values from the

ongoing stable night control loop at 05:44 AM. Let's assume e_temp(t-Δt) = -
0.05°C and Integral_Sum_temp(t-Δt) is a small value, say 2.0 °C·min,
reflecting minor adjustments around the setpoint.
○ PID Gains (illustrative for night-time temperature stability): Kp_temp = 5.0,
Ki_temp = 0.1, Kd_temp = 2.0 (derivative on PV change).

1. **Error (e_temp(t)):**
   e_temp(t) = S_active_temp - PV_temp = 17.0°C - 17.1°C = -0.1°C

```
(The zone is slightly warmer than the target).
```

2. **Proportional Term (P_term):**
   P_term = Kp_temp _ e_temp(t) = 5.0 _ (-0.1°C) = -0.5
3. **Integral Term (I_term):**
   Integral_Sum_temp(t) = Integral_Sum_temp(t-Δt) + (e_temp(t) _ Δt)
   = 2.0 °C·min + (-0.1°C _ 1 min) = 1.9 °C·min
   I_term = Ki_temp _ Integral_Sum_temp(t) = 0.1 _ 1.9 = 0.19
4. **Derivative Term (D_term - on PV change):** Assume PV_temp(t-Δt) at 05:44
   AM was 17.15°C.
   Rate_of_PV_Change = (PV_temp(t) - PV_temp(t-Δt)) / Δt
   = (17.1°C - 17.15°C) / 1 min = -0.05 °C/min
   D_term = -Kd_temp _ Rate_of_PV_Change = -2.0 _ (-0.05 °C/min) = 0.1
5. **Control Output (MV_temp_unscaled):**
   MV_temp_unscaled(t) = P_term + I_term + D_term = -0.5 + 0.19 + 0.1 = -0.21

This small negative value indicates a very slight need for cooling or, more
likely, cessation of any minimal heating that might have been active. If scaled,
this would translate to:
■ heating_requirement_percent: 0.0%
■ cooling_requirement_percent: 0.0% (as the deviation is tiny and natural
heat loss might handle it)
■ ventilation_requirement_percent: 2.0% (This is likely a pre-configured
minimum ventilation rate for air exchange, independent of minor PID
adjustments when close to setpoint, unless a specific "energy band" or
deadband for PID output is used).
○ The Climate Energy Balance Program publishes these requirement
percentages to the Cache.

```
● Vent Control & Equipment Program: Reads the requirements from the Cache.
○ Commands vents to maintain their 2.0% minimum opening.
○ Commands heater to remain OFF.
○ CO2 emitters are OFF.
```

Scenario 2: 06:15 AM - Morning Ramp Period (Dawn was at 06:00 AM)
The system initiated the transition from night to day setpoints at 06:00 AM. It is now 15
minutes into the 60-minute ramp.
● **Clock Program** to Cache: current_time_hhmmss: "06:15:00".
● **I/O Program** (Sensor data to Cache):
○ zone1_temperature_c (PV_temp): 17.2°C (Temperature might have slightly
drifted or stayed low due to minimal night heating and cool outdoor
conditions).
○ zone1_humidity_percent (PV_hum): 69.8%
○ zone1_co2_ppm (PV_co2): 440 ppm
○ outdoor_temperature_c_raw: 15.5°C (Outdoor temperature is beginning to
rise).
○ solar_radiation_wm2_avg5m: 50 W/m² (Sun is up, providing some initial solar
gain).
● **Weather Monitoring Program** to Cache: outdoor_temperature_c_avg10m:
15.6°C, wind_speed_mps_avg1m: 0.8 m/s (still calm).
● **Diurnal Setpoints Program (Ramp Calculations are in full swing):**
○ Current period: "Morning_Ramp_Roses" (Defined in recipe to last from 06:00
to 07:00).
○ T_elapsed = 15 minutes for all ramping parameters.
○ **Temperature Ramp Calculation:**
■ S_initial_temp = 17.0°C (Night Target from "Night_Roses" period)
■ S_target_temp = 23.0°C (Day Target from upcoming "Day_Roses" period)
■ T_duration_temp = 60 minutes
S_active_temp(15min) = S_initial_temp + (((S_target_temp - S_initial_temp) /
T_duration_temp) _ T_elapsed)
= 17.0°C + (((23.0°C - 17.0°C) / 60 min) _ 15 min)
= 17.0°C + ((6.0°C / 60 min) _ 15 min)
= 17.0°C + (0.1°C/min _ 15 min)
= 17.0°C + 1.5°C
= 18.5°C

```
○ Humidity Ramp Calculation:
■ S_initial_hum = 70.0% (Night Target)
```

```
■ S_target_hum = 65.0% (Day Target)
■ T_duration_hum = 60 minutes
S_active_hum(15min) = 70.0% + (((65.0% - 70.0%) / 60 min) * 15 min)
= 70.0% + ((-5.0% / 60 min) * 15 min)
= 70.0% + (-0.08333%/min * 15 min)
= 70.0% - 1.25%
= 68.75%
```

```
○ CO2 Ramp Calculation:
■ S_initial_co2 = 400 ppm (Night Target)
■ S_target_co2 = 700 ppm (Day Target)
■ T_duration_co2 = 60 minutes (assuming CO2 ramp aligns with
temp/humidity for simplicity here, though it might be linked to light levels
more directly in some strategies)
S_active_co2(15min) = 400ppm + (((700ppm - 400ppm) / 60 min) * 15 min)
= 400ppm + ((300ppm / 60 min) * 15 min)
= 400ppm + (5ppm/min * 15 min)
= 400ppm + 75ppm
= 475ppm
```

○ Publishes to Cache: active_setpoints: { "temperature_target_c": 18.5,
"humidity_target_percent": 68.75, "co2_target_ppm": 475 }, is_ramping: true,
current_period_name: "Morning_Ramp_Roses".
● **Climate Energy Balance Program (PID Calculation for Temperature):**
○ **Inputs for Temperature PID:**
■ S_active_temp = 18.5°C
■ PV_temp = 17.2°C
■ Δt = 1 minute
○ PID Gains (may use slightly more aggressive gains for ramp tracking, or gains
robust enough for both ramp and steady state): Kp_temp = 10.0, Ki_temp =
0.5, Kd_temp = 3.0.
○ e_temp(t-Δt) (at 06:14) was likely smaller but positive as ramp started.
Assume e_temp(t-Δt) = 0.8°C, Integral_Sum_temp(t-Δt) = 5.0 °C·min,
PV_temp(t-Δt) = 17.1°C.

1. **Error (e_temp(t)):**
   e_temp(t) = S_active_temp - PV_temp = 18.5°C - 17.2°C = 1.3°C

```
(The system is significantly cooler than the current ramp target, requiring
```

```
heating).
```

2. **Proportional Term (P_term):**
   P_term = Kp_temp _ e_temp(t) = 10.0 _ 1.3°C = 13.0
3. **Integral Term (I_term):**
   Integral_Sum_temp(t) = Integral_Sum_temp(t-Δt) + (e_temp(t) _ Δt)
   = 5.0 °C·min + (1.3°C _ 1 min) = 6.3 °C·min
   I_term = Ki_temp _ Integral_Sum_temp(t) = 0.5 _ 6.3 = 3.15
4. **Derivative Term (D_term - on PV change):**
   Rate_of_PV_Change = (PV_temp(t) - PV_temp(t-Δt)) / Δt
   = (17.2°C - 17.1°C) / 1 min = 0.1 °C/min
   D_term = -Kd_temp _ Rate_of_PV_Change = -3.0 _ (0.1 °C/min) = -0.3

```
(The PV is rising slowly, so D-term slightly dampens the heating request to
prevent overshoot if the rise accelerates).
```

5. **Control Output (MV_temp_unscaled):**
   MV_temp_unscaled(t) = P_term + I_term + D_term = 13.0 + 3.15 - 0.3 = 15.85

```
This positive value indicates a strong heating requirement. If scaled (e.g., 0-
20 range for MV maps to 0-100% power), this could translate to:
■ heating_requirement_percent: (15.85 / 20) * 100 = 79.25% (Let's say
system rounds/stages this to 75-80% heating capacity).
■ cooling_requirement_percent: 0.0%.
■ ventilation_requirement_percent: 5.0% (Still maintaining a low ventilation
rate, as the priority is to increase temperature and CO2, and solar gain is
still minimal. Excessive ventilation would lose heat and CO2).
○ Publishes these requirements to Cache.
● Vent Control & Equipment Program: Reads the heating requirement.
○ Commands Heater (e.g., activates necessary stages or modulates heating
valve to deliver ~75-80% capacity).
○ Vents remain at their configured minimum (e.g., 5.0%) to conserve heat and
allow CO2 to build up if enrichment starts.
○ CO2 Emitters: If PV_co2 (440 ppm) is significantly below S_active_co2 (475
ppm) and vent positions are minimal, the program may command CO2
emitters to start dosing.
```

Scenario 3: 11:00 AM - Daytime Period ("Day_Roses") - Ramp Finished
The morning ramp successfully completed at 07:00 AM. The system is now in the

"Day_Roses" period, actively maintaining daytime setpoints under significant solar load.
● **Clock Program** to Cache: current_time_hhmmss: "11:00:00".
● **I/O Program** (Sensor data to Cache):
○ zone1_temperature_c (PV_temp): 23.2°C
○ zone1_humidity_percent (PV_hum): 64.0%
○ zone1_co2_ppm (PV_co2): 710 ppm (CO2 slightly above target, perhaps due
to effective enrichment and controlled venting)
○ outdoor_temperature_c_raw: 26.0°C
○ solar_radiation_wm2_avg5m: 600 W/m² (Strong mid-morning sun, significant
heat gain).
● **Weather Monitoring Program** to Cache:
○ outdoor_temperature_c_avg10m: 26.0°C
○ wind_speed_mps_avg1m: 3.0 m/s from West (270°)
○ is_raining: false
● **Broadcast Information Program** (derived data published to Cache by a utility
module or the Weather Program): Given greenhouse orientation,
windward_vent_side: "WestVents", leeward_vent_side: "EastVents".
● **Diurnal Setpoints Program** ("Day_Roses" period is active):
○ Active_Setpoint_Temperature (S_active_temp): 23.0°C.
○ Active_Setpoint_Humidity (S_active_hum): 65.0%.
○ Active_Setpoint_CO2 (S_active_co2): 700 ppm.
○ Publishes to Cache: active_setpoints: { "temperature_target_c": 23.0,
"humidity_target_percent": 65.0, "co2_target_ppm": 700 }, is_ramping: false,
current_period_name: "Day_Roses".
● **Climate Energy Balance Program (PID Calculation for Temperature):**
○ **Inputs for Temperature PID:** S_active_temp = 23.0°C, PV_temp = 23.2°C. PID
gains might be Kp_temp = 8.0, Ki_temp = 0.3, Kd_temp = 2.5.
○ e_temp(t-Δt) (at 10:59) was, say, -0.15°C. Integral_Sum_temp(t-Δt) might be -
10.0 °C·min (accumulating negative error due to solar gain). PV_temp(t-Δt) =
23.15°C.

1. **Error (e_temp(t)):**
   e_temp(t) = S_active_temp - PV_temp = 23.0°C - 23.2°C = -0.2°C

```
(Greenhouse is slightly too warm due to solar gain).
```

2. **P_term:** 8.0 \* (-0.2°C) = -1.6
3. **I_term:** Integral_Sum_temp(t) = -10.0 + (-0.2 \* 1) = -10.2 °C·min. I_term = 0.3
    - (-10.2) = -3.06.
4. **D_term (on PV):** Rate_PV = (23.2 - 23.15)/1 = 0.05 °C/min. D_term = -2.5 \*

## 0.05 = -0.125.

5. MV_temp_unscaled: -1.6 - 3.06 - 0.125 = -4.785. This negative value indicates
   a cooling requirement.
   This translates to:
   ■ heating_requirement_percent: 0.0%.
   ■ cooling_requirement_percent: 0.0% (assuming active cooling is off, and
   ventilation is primary).
   ■ ventilation_requirement_percent: 23.9% (e.g., if -20 MV maps to 100%
   vent, then -4.785 maps to approx 23.9% ventilation demand for cooling).
   Let's round to 24.0%.
   ● **Climate Energy Balance Program (PID Calculation for Humidity):**
   ○ **Inputs for Humidity PID:** S_active_hum = 65.0%, PV_hum = 64.0%. Gains:
   Kp_hum = 2.0, Ki_hum = 0.05, Kd_hum = 1.0.
   ○ e_hum(t-Δt) was, say, 0.8%. Integral_Sum_hum(t-Δt) = 20.0 %·min.
   PV_hum(t-Δt) = 64.2%.
1. **Error (e_hum(t)):**
   e_hum(t) = S_active_hum - PV_hum = 65.0% - 64.0% = 1.0%

```
(Air is 1% drier than the target).
```

2. **P_term:** 2.0 \* 1.0% = 2.0.
3. **I_term:** Integral_Sum_hum(t) = 20.0 + (1.0 _ 1) = 21.0 %·min. I_term = 0.05 _
   21.0 = 1.05.
4. **D_term (on PV):** Rate_PV_hum = (64.0 - 64.2)/1 = -0.2 %/min. D_term = -1.0 \*
   (-0.2) = 0.2.
5. MV_hum_unscaled: 2.0 + 1.05 + 0.2 = 3.25. This positive value indicates a
   need for humidification.
   This might translate to:
   ■ humidification_requirement_percent: 16.3% (if MV range for humidifier is
   0 - 20).
   ■ The system needs to balance this with the ventilation demand. Often,
   temperature control (requiring ventilation) will take precedence over
   humidification if outdoor air is dry. If outdoor air is humid, ventilation
   might help. If dedicated humidifiers exist, they would be activated. If not,
   the ventilation might proceed, and the humidity might remain slightly
   below target.
   ● **Vent Control & Equipment Program:**
   ○ Primary driver: ventilation_requirement_percent: 24.0% (from temperature

## PID).

○ Wind conditions: 3.0 m/s from West. **Wind Direction Logic** might advise:
■ Vent*Adjustment_Factor_Windward (WestVents) = 0.8 (reduce West vent
opening by 20% due to direct wind).
■ Vent_Adjustment_Factor_Leeward (EastVents) = 1.0 (no restriction).
○ The program aims to achieve an \_effective* 24% ventilation.
■ If West and East vents have equal capacity, a naive split is 12% West, 12%
East.
■ Applying wind factors:
■ West target: 12% / 0.8 = 15% (i.e., to get 12% effective, need to open
to 15% if factor was a reduction of effectiveness, or if factor is direct
multiplier: 12% _ 0.8 = 9.6%). The logic is usually that the factor limits
the PID output. So, if PID asks for 24%, windward vents can only
contribute 24% _ 0.8 = 19.2% at most from their share.
■ A more sophisticated distribution: If total ventilation needed is 24 units:
■ Max from West vents (assuming they could do the full 24% alone if no
wind): 24% _ 0.8 = 19.2% effective opening.
■ Remaining needed: 24% - 19.2% = 4.8% (if West vents alone were
considered).
■ This is complex; simpler is to say: West vents open to X%, East to Y%.
The *effective* total ventilation should be 24%.
■ Let's assume it sets west_vents_position_command_percent = 20%
(which is 20% _ 0.8 = 16% "effective" due to wind reduction, or simply
limited to 24%_0.8 = 19.2%).
■ And east_vents_position_command_percent = 28% to achieve the
total.
■ For this example, let's use the simpler interpretation: PID wants 24%.
West vents are limited to 24% _ 0.8 = 19.2%. East vents can do 24% \*
1.0 = 24%. The program might open East vents to 24% and West vents
to a lesser value like 19% or so, or average them.
■ A common strategy: Target_West = Min(PID_Demand,
Wind_Limit_West), Target_East = PID_Demand.
■ So, west_vents_position_command_percent = 19.2%,
east_vents_position_command_percent = 24.0%.
○ CO2 Control: PV_co2 (710 ppm) is slightly above S_active_co2 (700 ppm). The
**Vent Control & Equipment Program** commands
co2_emitter_command_onoff: "OFF".

```
○ Humidification: If humidifiers are present and the
humidification_requirement_percent > 0, they would be activated, provided
this doesn't conflict strongly with ventilation goals (e.g., not humidifying
heavily if vents are wide open with dry outdoor air).
○ Publishes all actuator commands to Cache.
```

Scenario 4: 02:00 PM - Daytime, Sudden Rain Shower
The weather changes abruptly.
● **I/O Program** (Sensor data): Rain sensor detects moisture. Publishes
is*raining_raw: true.
● **Weather Monitoring Program** processes this and publishes to Cache: is_raining:
true.
● **Vent Control & Equipment Program (High-Priority Override Logic):**
○ Detects is_raining: true. This immediately triggers its "Rain Override" routine,
which has higher precedence than standard PID-driven ventilation or normal
wind logic.
○ **Crucially, the Diurnal Setpoints Program and the Climate Energy
Balance Program (with its PIDs) continue to function in the background.**
They are unaware of the rain override at their level. The PID for temperature,
for example, will sense the internal temperature rising (as vents close and
solar gain continues) and humidity potentially increasing. It will calculate an
even higher ventilation_requirement_percent and
cooling_requirement_percent.
○ However, the **Vent Control & Equipment Program** will \_not* act on this
increased ventilation demand from the PID. Instead, it executes its rain safety
protocol:
■ Command to Cache: west_vents_position_command_percent: 5.0% (a
pre-configured "rain gap" to allow minimal air exchange while preventing
significant water ingress, especially if these are roof vents or wind-facing
side vents).
■ Command to Cache: east_vents_position_command_percent: 5.0%
(similar rain gap for leeward vents).
■ Some systems might close windward vents to 0% and only allow a rain
gap on leeward vents.
○ **Consequences & Secondary Effects:**
■ Internal temperature and humidity will likely rise.
■ The PID in the Climate Energy Balance program, seeing the temperature
climb far above setpoint and its ventilation commands having no effect

```
(because they are overridden), will likely push its
cooling_requirement_percent very high.
■ If the greenhouse is equipped with active cooling systems (e.g., chillers
and fan coils, evaporative cooling pads) or active dehumidifiers, the Vent
Control & Equipment Program might then activate these systems based
on the high cooling_requirement_percent or a separate high
dehumidification_requirement_percent from a humidity PID, if its logic is
configured to use these as backups when ventilation is compromised by
rain.
● I/O Program: Receives commands to close vents to 5% and relays them to the
vent motors.
```

Scenario 5: 06:15 PM - Evening Ramp Period (Dusk is at 06:30 PM)
The rain has stopped. The system is now 15 minutes into the 60-minute ramp from Day
setpoints down to Night setpoints.
● **Clock Program** to Cache: current_time_hhmmss: "06:15:00", dusk_time_hhmm:
"06:30".
● **I/O Program** (Sensor data to Cache):
○ zone1_temperature_c (PV_temp): 22.5°C (Temperature is still relatively high
from daytime solar gain and possibly the earlier rain event that trapped heat).
○ zone1_humidity_percent (PV_hum): 68.0%
○ outdoor_temperature_c_raw: 20.0°C (Outdoor temperature is dropping as
evening approaches).
● **Weather Monitoring Program** to Cache: outdoor_temperature_c_avg10m:
19.8°C, is_raining: false.
● **Diurnal Setpoints Program (Ramp Calculations):**
○ Current period: "Evening_Ramp_Roses". T_elapsed = 15 minutes (assuming
ramp starts at 06:00 PM).
○ **Temperature Ramp Calculation:**
■ S_initial_temp = 23.0°C (Previous Day Target)
■ S_target_temp = 17.0°C (Upcoming Night Target)
■ T_duration_temp = 60 minutes
S_active_temp(15min) = S_initial_temp + (((S_target_temp - S_initial_temp) /
T_duration_temp) _ T_elapsed)
= 23.0°C + (((17.0°C - 23.0°C) / 60 min) _ 15 min)
= 23.0°C + ((-6.0°C / 60 min) _ 15 min)
= 23.0°C + (-0.1°C/min _ 15 min)
= 23.0°C - 1.5°C
= 21.5°C

```
○ Humidity and CO2 would also be ramping towards their respective night
targets (e.g., humidity target increasing, CO2 target decreasing).
○ Publishes to Cache: active_setpoints: { "temperature_target_c": 21.5,
"humidity_target_percent": 66.25 /*example*/, "co2_target_ppm": 625
/*example*/ }, is_ramping: true, current_period_name:
"Evening_Ramp_Roses".
● Climate Energy Balance Program (PID Calculation for Temperature):
○ Inputs for Temperature PID: S_active_temp = 21.5°C, PV_temp = 22.5°C.
```

1. **Error (e_temp(t)):**
   e_temp(t) = S_active_temp - PV_temp = 21.5°C - 22.5°C = -1.0°C

```
(The greenhouse is still warmer than the current ramp target; system needs
to cool down further).
```

2. **P,I,D terms:** P_term will be negative, driving a cooling action. I_term will
   accumulate this negative error. D_term will react to the rate of temperature
   change.
3. **MV_temp (Cooling/Ventilation Requirement):** The PID output indicates a
   cooling need. Since outdoor_temperature_c_avg10m (~19.8°C) is now
   significantly lower than PV_temp (22.5°C), natural ventilation will be very
   effective for "free cooling."
   ■ heating_requirement_percent: 0.0%.
   ■ cooling_requirement_percent: 0.0% (assuming active mechanical cooling
   is not needed as natural ventilation is viable).
   ■ ventilation_requirement_percent: 30.0% (or a value dynamically
   calculated by the PID to track the cooling ramp effectively, considering
   the temperature differential to outside).
   ○ Publishes these requirements to Cache.
   ● **Vent Control & Equipment Program:**
   ○ Reads ventilation_requirement_percent: 30.0%.
   ○ Checks wind conditions (assume calm now for simplicity).
   ○ Commands vents to open to achieve 30% effective ventilation to facilitate
   cooling towards the night setpoint.
   ○ CO2 emitters would be commanded OFF as photosynthesis declines with
   diminishing light at dusk.

Scenario 6: 10:00 PM - Night Period ("Night_Roses") - Ramp Finished
The evening ramp concluded at 07:00 PM. The system is now in the stable "Night_Roses"

period.
● **Clock Program** to Cache: current_time_hhmmss: "22:00:00".
● **I/O Program** (Sensor data to Cache):
○ zone1_temperature_c (PV_temp): 16.8°C (Temperature has settled, slightly
below target perhaps due to continued heat loss).
○ zone1_humidity_percent (PV_hum): 70.5% (Humidity close to target).
○ outdoor_temperature_c_raw: 12.0°C (Cool night).
● **Diurnal Setpoints Program** ("Night_Roses" period active):
○ Active_Setpoint_Temperature (S_active_temp): 17.0°C.
○ Active_Setpoint_Humidity (S_active_hum): 70.0%.
○ Active_Setpoint_CO2 (S_active_co2): 400 ppm.
○ Publishes to Cache: active_setpoints: { "temperature_target_c": 17.0, ... },
is_ramping: false, current_period_name: "Night_Roses".
● **Climate Energy Balance Program (PID Calculation for Temperature):**
○ **Inputs for Temperature PID:** S_active_temp = 17.0°C, PV_temp = 16.8°C.

1. **Error (e_temp(t)):**
   e_temp(t) = S_active_temp - PV_temp = 17.0°C - 16.8°C = 0.2°C

```
(The zone is slightly cooler than the target).
```

2. **P,I,D terms:** P_term will be small and positive. I_term will start accumulating
   positive error (or reducing a previous negative sum). D_term depends on
   temperature stability.
3. **MV_temp (Heating Requirement):** The PID output indicates a minor heating
   need to bring the temperature up by 0.2°C and maintain it against night-time
   heat loss to the cooler exterior.
   ■ heating_requirement_percent: 10.0% (This might command a low stage of
   heating or cycle the primary heater gently).
   ■ cooling_requirement_percent: 0.0%.
   ■ ventilation_requirement_percent: 2.0% (Maintaining the configured
   minimum air exchange rate).
   ○ Publishes these requirements to Cache.
   ● **Vent Control & Equipment Program:**
   ○ Commands Heater ON (to deliver approx. 10% heating capacity).
   ○ Commands Vents to maintain their 2.0% minimum opening.

This detailed daily flow, with its embedded Ramp and PID calculations, exemplifies the
responsive and adaptive nature of the integrated control system in managing a
dynamic greenhouse environment for high-value rose cultivation.

**4.3. Trigger Program Example (Reiteration and Expansion)**

Beyond the continuous feedback loops of the PID controllers and the scheduled
changes of the Diurnal Setpoints Program, the **Trigger Program** provides an
essential layer of customizable, event-driven logic. This allows growers to implement
specific rules for situations that fall outside standard diurnal control or require unique
responses. These triggers are typically defined by "IF-THEN-ELSE" constructs.

```
● Example Trigger 1 (Revisited & Enhanced):
"Night_High_Humidity_Alert_and_Corrective_Action"
○ Purpose: To prevent prolonged periods of excessively high humidity at night,
which can promote fungal diseases like Botrytis, especially during the
flowering phase.
○ Condition (IF):
(
zone1_humidity_percent > 75%
AND
current_period_name CONTAINS "Night"
AND
trigger_active_duration("Night_High_Humidity_Alert") >= 30 minutes //
Condition must persist for 30 mins
)
○ Action (THEN):
```

1. Set night_humidity_alarm_active = true (Publish to Cache for logging and
   potential operator notification via a separate alarm management module).
2. IF is_raining == false AND outdoor_humidity_percent <
   (zone1_humidity_percent - 10%) AND outdoor_temperature_c >
   (S_active_temp - 5°C) THEN // Check if outdoor air is suitable for
   dehumidification and not too cold
   ■ Temporarily increase ventilation_requirement_override_percent by an
   additional 5% for the next 15 minutes. (This override would be a
   special input to the Vent Control Program, which would add it to the
   current minimum ventilation, ensuring it doesn't conflict with major
   safety overrides like storm closure but can augment normal night
   venting). This action is often called a "humidity dump" or "purging."
   ■ Log "Night humidity dump initiated."
3. ELSE
   ■ Log "Night high humidity persists, but conditions unsuitable for vent-
   based dump."

```
■ IF active_dehumidifier_available == true THEN
■ Set dehumidifier_command_onoff = "ON" for 30 minutes or until
humidity drops below 72%.
○ Reset Condition: The trigger night_humidity_alarm_active could be reset
automatically if zone1_humidity_percent drops below 72% for 10 minutes.
● Example Trigger 2 (New): "Equipment_Failure_Detection_Heater"
○ Purpose: To detect a potential malfunction in the heating system if it's
commanded ON but no corresponding temperature rise (or even a drop) is
observed.
○ Condition (IF):
(
heating_requirement_percent > 50% // System is calling for significant
heating
AND
heater_stage1_command_onoff == "ON" // And the heater is commanded on
AND
PV_temp_rate_of_change_10min <= 0.0 °C/hour // And actual temperature is
not rising (or falling) over last 10 mins
AND
S_active_temp > PV_temp + 1.0°C // And there's still a significant error
AND
trigger_active_duration("Heater_Fault_Check") >= 20 minutes // Conditions
persist
)
○ Action (THEN):
```

1. Set heater_fault_alarm_active = true (Publish to Cache).
2. Send high-priority alert to operator: "Potential Heater Malfunction
   Detected in Zone 1! System calling for heat, but temperature is not
   responding."
3. Log all relevant parameters: heating_requirement_percent,
   heater_command_status, PV_temp, S_active_temp,
   outdoor_temperature_c_avg10m.
4. Optionally, if redundant heating is available: IF backup_heater_available
   == true THEN command_backup_heater_on = true.
   ○ **Reset Condition:** Manual reset by operator after checking the system, or if
   temperature starts rising appropriately once the issue is (externally) resolved.

These trigger examples illustrate the flexibility to add layers of intelligent oversight

and automated response to specific operational concerns, enhancing both crop
protection and system reliability. The Trigger Program relies on subscribing to a wide
range of data from the Cache and can publish its own flags or command overrides
back into the Cache for other modules to act upon.

**5. Conclusion**

This comprehensive overview has meticulously detailed the architecture, logic, and
application of an advanced greenhouse environmental control system. It underscores
that such a system is far more than a collection of thermostats and timers; it is a
complex, integrated ecosystem of software modules, sensors, and actuators working
in concert. The precise and adaptive application of **Ramp Control Logic** ensures that
transitions between different environmental regimes are smooth and physiologically
sound for the plants, minimizing stress and promoting stable growth. At the heart of
continuous regulation, **PID Control Logic** provides the core intelligence for
accurately tracking desired setpoints for critical parameters like temperature,
humidity, and CO2, dynamically adjusting outputs to an array of climate control
equipment.

The intelligence of the system is further augmented by modules like the **Wind
Direction Logic** , which allows for strategic and safe utilization of natural ventilation
by adapting to real-time wind conditions, and the robust **Vent Program Control
Logic** , which serves as the final gateway for all vent commands, incorporating crucial
safety overrides for conditions such as rain or extreme weather. The integration of
these modules, facilitated by a central **Broadcast Cache** , creates a responsive and
resilient control environment.

Through the detailed "day-in-the-life" scenarios of a rose cultivation cycle,
particularly during the demanding flowering phase, we have observed the tangible
application of these principles. The step-by-step breakdown of Ramp and PID
calculations at different times of day—from pre-dawn stability, through dynamic
morning ramps, active daytime maintenance with adaptation to solar gain and
weather events like rain, to controlled evening ramps and stable night-time
conditions—highlights the system's capability to continuously monitor, compute, and
actuate. This high level of detailed, automated, and adaptive control is indispensable
for modern CEA. It is fundamental to optimizing crop production cycles, ensuring
consistent high quality and yield, managing energy and water resources efficiently,
and ultimately enhancing the economic viability and environmental sustainability of
greenhouse operations.

Looking ahead, the modular architecture described provides a robust foundation for
future enhancements and the incorporation of even more advanced control
paradigms. These may include:

```
● Predictive Control: Utilizing weather forecasts and thermal models of the
greenhouse to anticipate environmental changes and proactively adjust controls,
rather than just reacting to current deviations.
● AI/Machine Learning: Employing ML algorithms to learn optimal control
strategies from historical data, further refine PID tuning dynamically, or predict
pest/disease outbreaks based on subtle environmental cues.
● Integration with Energy Markets: Optimizing energy consumption by aligning
heating/cooling/lighting schedules with variable electricity pricing or on-site
renewable energy availability.
● Advanced Sensor Integration: Incorporating more sophisticated plant-based
sensors (e.g., chlorophyll fluorescence, sap flow, leaf turgor pressure) to directly
measure plant responses and fine-tune the environment for actual plant needs
rather than just air parameters.
● Digital Twins: Creating virtual replicas of the greenhouse and crop, allowing for
simulation of different control strategies and "what-if" analyses before
implementation in the real world.
```

In conclusion, the type of comprehensive environmental control system detailed
herein is no longer a luxury but a necessity for competitive and sustainable CEA. It
empowers growers with unprecedented precision and adaptability, paving the way for
more resilient, productive, and resource-efficient agriculture.

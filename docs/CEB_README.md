# Climate Energy Balance (CEB) System

## Overview

The Climate Energy Balance (CEB) system is a sophisticated climate control module that manages heating and ventilation equipment based on temperature and humidity demands. It implements advanced control algorithms including dual PID controllers, feed-forward adjustments, intelligent equipment selection, and energy-saving interlocks as specified in the official Argus Control Systems operator manual.

**Program Name**: Climate Energy Balance
**Compliance**: Fully compliant with Argus Control Systems CEB Program specification (Rev. January 2009)

## Architecture

### Core Components

1. **CEB Controller** (`ceb/ceb.go`) - Main control logic
2. **PID Controllers** (`ceb/pid.go`) - Temperature and humidity control loops
3. **Sensor Integration** - Integrated sensor readings with validation
4. **Feed-Forward Logic** - Predictive adjustments based on external factors

### Key Features

-   **Complete Energy Balance Program**: Produces all 9 official outputs as specified in PDF
-   **Dual PID Control**: Separate control loops for temperature and humidity with optimized tuning
-   **Rate Management**: Controls the rate of change, not just states
-   **Feed-Forward Prediction**: Proactive adjustments for outdoor temperature and light with curtain factors
-   **Equipment Selection**: Always uses the highest demand value (highest vs sum logic)
-   **Sensor Integration**: Smoothed, validated sensor data with noise filtering
-   **Safety Limits**: Protection against excessive changes and emergency stop
-   **Energy-Saving Interlocks**:
    -   **Dehumidification Interlock**: "Less 'P' Heat Component" prevents heating/ventilation conflicts
    -   **Cooling/Heating Interlock**: Integral reset prevents oscillation when heating is active
-   **Hardware Abstraction**: Temperature mapping (30-80°C) for real heating equipment
-   **Aligned Output Display**: Professional formatted logging for easy monitoring

### Internal Variable Names

The CEB system uses descriptive variable names that match the Energy Balance Program specification:

| **Variable Name**              | **Description**                               | **Output #** |
| ------------------------------ | --------------------------------------------- | ------------ |
| `VentReqForTemperatureControl` | Ventilation Required for Temperature Control  | 1            |
| `VentReqForHumidityControl`    | Ventilation Required for Humidity Control     | 2            |
| `HighestVentRequest`           | Highest Ventilation Request                   | 3            |
| `SumOfVentRequests`            | Sum of Ventilation Requests                   | 4            |
| `HeatReqForTemperatureControl` | Heating Required for Temperature Control      | 5            |
| `HeatReqForHumidityControl`    | Heating Required for Humidity Control         | 6            |
| `HighestHeatRequest`           | Highest Heating Request                       | 7            |
| `SumOfHeatRequests`            | Sum of Heating Requests                       | 8            |
| `HeatingSystemTempRequest`     | Current Temperature Request to Heating System | 9            |

## Configuration

### CEB Configuration File (`config/cebConfig.json`)

```json
{
    "programName": "Climate Energy Balance",
    "enabled": true,
    "temperaturePID": {
        "kp": 25.0,
        "ki": 0.02,
        "kd": 0.05,
        "outputMin": 0.0,
        "outputMax": 100.0,
        "integralMax": 50.0
    },
    "humidityPID": {
        "kp": 20.0,
        "ki": 0.02,
        "kd": 0.02,
        "outputMin": 0.0,
        "outputMax": 100.0,
        "integralMax": 30.0
    },
    "outdoorTempEffect": {
        "enabled": true,
        "minInput": -10.0,
        "maxInput": 35.0,
        "minOutput": 0.0,
        "maxOutput": 50.0,
        "curtainFactor": 0.7
    },
    "lightEffect": {
        "enabled": true,
        "minInput": 0.0,
        "maxInput": 1000.0,
        "minOutput": 0.0,
        "maxOutput": 30.0,
        "curtainFactor": 1.0
    },
    "heatingSystem": {
        "minRequest": 30.0,
        "maxRequest": 80.0,
        "rampRate": 5.0
    },
    "ventilationSystem": {
        "minRequest": 0.0,
        "maxRequest": 100.0,
        "rampRate": 10.0
    },
    "sensorIntegration": {
        "integrationTime": 10,
        "validationEnabled": true,
        "minValidTemp": -20.0,
        "maxValidTemp": 60.0,
        "minValidHumidity": 0.0,
        "maxValidHumidity": 100.0
    },
    "safetyLimits": {
        "maxTempChange": 2.0,
        "maxHumidityChange": 10.0,
        "emergencyStop": false
    }
}
```

## Data Flow

1. **Setpoint Ingestion**: Gets targets from diurnal setpoint program
2. **Sensor Integration**: Processes and validates sensor readings
3. **Demand Calculation**: Runs parallel PID loops for temperature and humidity
4. **Feed-Forward Adjustments**: Applies predictive corrections
5. **Equipment Selection**: Chooses highest demand for each equipment type
6. **Safety Validation**: Applies safety limits and constraints
7. **Output Storage**: Stores results in Redis for equipment control

### Data flow architecture

```mermaid
graph TD
    A[Diurnal Setpoints] --> B[CEB Controller]
    C[Weather Data] --> B
    D[Sensor Readings] --> E[Sensor Integration]
    E --> B
    B --> F[Temperature PID]
    B --> G[Humidity PID]
    F --> H[Feed-Forward Adjustments]
    G --> H
    H --> I[Equipment Selection Logic]
    I --> J[Heating System]
    I --> K[Ventilation System]
    L[Redis Cache] --> B
    B --> L
```

```
Diurnal Setpoints → CEB Controller ← Weather Data
                         ↓
                  Sensor Integration
                         ↓
              Temperature PID ← → Humidity PID
                         ↓
               Feed-Forward Adjustments
                         ↓
              Equipment Selection Logic
                         ↓
           Heating System ← → Ventilation System
                         ↓
                   Redis Storage
```

## Redis Keys

### Input Keys (from other systems)

-   `hub:1:zone:z1:instance:i1:setpoint:heatingTarget` - Target temperature for heating
-   `hub:1:zone:z1:instance:i1:setpoint:coolingTarget` - Target temperature for cooling
-   `hub:1:zone:z1:instance:i1:setpoint:dehumidifyTarget` - Target humidity for dehumidification
-   `hub:1:zone:z1:instance:i1:io:sensorTemperature` - Current temperature reading
-   `hub:1:zone:z1:instance:i1:io:sensorHumidity` - Current humidity reading
-   `hub:1:zone:z1:instance:i1:weather:outdoorTemperature` - Outdoor temperature
-   `hub:1:zone:z1:instance:i1:weather:lightLevel` - Light intensity

### Output Keys (CEB results)

#### The 9 Official Energy Balance Program Outputs

-   `ceb:ventTempControl` - 1. Ventilation Required for Temperature Control (0-100%)
-   `ceb:ventHumidityControl` - 2. Ventilation Required for Humidity Control (0-100%)
-   `ceb:highestVentRequest` - 3. Highest Ventilation Request (0-100%)
-   `ceb:sumVentRequests` - 4. Sum of Ventilation Requests (0-100%)
-   `ceb:heatTempControl` - 5. Heating Required for Temperature Control (0-100%)
-   `ceb:heatHumidityControl` - 6. Heating Required for Humidity Control (0-100%)
-   `ceb:highestHeatRequest` - 7. Highest Heating Request (0-100%)
-   `ceb:sumHeatRequests` - 8. Sum of Heating Requests (0-100%)
-   `ceb:heatingSystemTempRequest` - 9. Current Temperature Request to Heating System (°C)

#### Additional Monitoring Keys

-   `ceb:integratedTemp` - Integrated temperature value
-   `ceb:integratedHumidity` - Integrated humidity value

#### Legacy Compatibility Keys

-   `ceb:heatRequest` - Final heating system request (same as highestHeatRequest)
-   `ceb:ventRequest` - Final ventilation system request (same as highestVentRequest)

## Usage

### Enabling CEB

1. Run the main program: `./program-manager`
2. Select option `3` (climateEnergyBalance) when prompted
3. The system will automatically create default configuration if none exists
4. CEB will start processing every 5 seconds

### Testing

Use the test script to populate sample data:

```bash
go run test/test_ceb.go
```

### Example Output

The CEB system displays beautifully formatted output every 5 seconds:

```
CEB: Starting processing cycle...
CEB: Setpoints - Heat: 22.0°C, Cool: 26.0°C, Dehumid: 70.0%
CEB: Sensors - Temp: 21.5°C, Humidity: 75.0%, Outdoor: 15.0°C, Light: 500
CEB: Demands - HeatForTempControl: 1.5%, VentForTempControl: 0.0%, HeatForHumidityControl: 3.3%, VentForHumidityControl: 4.4%

CEB: All 9 Energy Balance Outputs:
  1. Vent for Temp:       15.0%
  2. Vent for Humidity:   14.9%
  3. Highest Vent:        15.0%
  4. Sum Vent:            29.9%
  5. Heat for Temp:       0.0%
  6. Heat for Humidity:   0.0%
  7. Highest Heat:        30.0%
  8. Sum Heat:            0.0%
  9. Heating System Temp: 30.0°C
CEB: Processing cycle completed successfully
```

### Monitoring

Monitor CEB operation through:

-   Console logs showing processing cycles with aligned output formatting
-   Redis keys containing all 9 Energy Balance Program outputs
-   Equipment request values for heating and ventilation systems
-   Real-time sensor integration and PID calculations

## Usage Examples

### Accessing Energy Balance Outputs in Other Programs

```go
// Example: Get highest ventilation request
ventRequest, _ := redis.GetFloat(ctx, "ceb:highestVentRequest")

// Example: Get heating system temperature
heatTemp, _ := redis.GetFloat(ctx, "ceb:heatingSystemTempRequest")

// Example: Get sum of all heating requests for load analysis
sumHeat, _ := redis.GetFloat(ctx, "ceb:sumHeatRequests")

// Example: Get individual control demands using configuration
cebConfig := config.CreateDefaultCEBConfig()
topics := cebConfig.RedisTopics
tempHeat, _ := redis.GetFloat(ctx, topics.HeatTempControl)
humidVent, _ := redis.GetFloat(ctx, topics.VentHumidityControl)

// Example: Get integrated sensor values
integratedTemp, _ := redis.GetFloat(ctx, topics.IntegratedTemp)
integratedHumidity, _ := redis.GetFloat(ctx, topics.IntegratedHumidity)
```

### Running the System

1. **Start the program**: `./program-manager`
2. **Select CEB**: Choose option `3` when prompted
3. **Populate test data**: `go run test/test_ceb.go` (optional)
4. **Monitor output**: Watch console for real-time calculations

## Integration

The CEB system integrates with:

-   **Diurnal Setpoint Program**: Receives target values
-   **Weather Monitoring**: Gets outdoor conditions
-   **Equipment Control**: Sends heating/ventilation requests
-   **Redis**: Stores all data and results

## Safety Features

-   Temperature and humidity validation ranges
-   Rate limiting to prevent equipment damage
-   Emergency stop capability
-   Integral windup protection in PID controllers
-   Sensor failure detection and fallback modes

## Tuning Guidelines

### PID Tuning

-   **Kp**: Proportional gain (responsiveness)
-   **Ki**: Integral gain (steady-state accuracy)
-   **Kd**: Derivative gain (stability)

### Feed-Forward Tuning

-   Adjust input/output ranges based on local climate
-   Set curtain factors based on thermal screen properties
-   Enable/disable effects based on equipment capabilities

### Safety Limits

-   Set maximum change rates based on crop sensitivity
-   Configure validation ranges for local sensor types
-   Adjust integration time based on sensor noise levels

### Heating System Temperature Mapping

The 9th output "Current Temperature Request to Heating System" maps the heating percentage to actual temperature:

-   **minRequest**: Minimum heating system temperature (e.g., 30°C for pipe temperature)
-   **maxRequest**: Maximum heating system temperature (e.g., 80°C for pipe temperature)
-   **Calculation**: `Temperature = minRequest + (HeatingPercent/100) * (maxRequest - minRequest)`

Example: With 50% heating demand and range 30-80°C:
`Temperature = 30 + (50/100) * (80-30) = 55°C`

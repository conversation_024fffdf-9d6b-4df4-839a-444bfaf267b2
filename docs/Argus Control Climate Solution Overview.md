# Argus Control Climate Solution Overview

Argus Control provides a climate control solution that manages climates and equipment through a specialized control program. Below is an explanation of its key components and how they work together.

## 1. Program Manager

The Program Manager oversees and orchestrates the configuration and execution of all programs within the Argus system. Its responsibilities include:

-   Managing program configuration.
-   Ensuring proper execution of programs in sequence (e.g., Clock, Diurnal Setpoints, Weather Monitoring).
-   Handling configuration management to maintain system integrity via JSON files or a web dashboard.

## 2. Clock Program

The Clock Program calculates dawn and dusk times based on the provided latitude and longitude. Key features include:

-   Uses Network Time Protocol (NTP) to obtain accurate real-time data.
-   Provides time-based inputs for other programs to align operations with daily cycles.
-   Sends time and dawn/dusk data to the Broadcast Program (Section 5) for distribution.

## 3. Diurnal Set Point Program

The Diurnal Set Point Program establishes target set points for other programs, acting as the system’s scheduling engine. It is structured as follows:

-   Consists of **8 periods**, each with **8 set points**:
    -   Heating Target (e.g., 20°C for temperature control).
    -   Cooling Target (e.g., 24°C to define temperature dead band).
    -   Dehumidify Target (e.g., 70% RH to prevent fungal growth).
    -   Humidify Target (e.g., 50% RH to avoid plant stress).
    -   Max Dehumid Vent (e.g., 30% max vent opening for dehumidification).
    -   Max Dehumid Heat (e.g., 15% max heating for dehumidification).
    -   CO2 Target (e.g., 800 PPM to enhance photosynthesis).
    -   SPARE (flexible for user-defined parameters, e.g., irrigation EC or shade screen %).
-   Users assign names and target values to set points, supporting climate and other system parameters.
-   Calculates ramped values for smooth transitions (e.g., interpolating from 22°C to 18°C over 3 hours between periods) to prevent crop stress and equipment wear.
-   Includes ramp rate alarms to flag excessive setpoint changes and period overlap alarms to prevent scheduling conflicts.
-   Sends set points to the Broadcast Program (Section 5) for distribution to other programs, such as the Climate Energy Balance Program (Section 6).

## 4. Weather Service

The Weather Service provides smooth, reliable data from external weather sources, typically an on-site weather station. Its role includes:

-   Collecting data from io service on external conditions (e.g., temperature, humidity, wind speed, solar radiation, rain status).
-   Processing data to compute min/max/avg values (e.g., wind speed avg 7.5 m/s) and sending them to the Broadcast Program (Section 5).
-   Enabling precise climate control adjustments based on internal and external environmental differences.

## 5. Broadcast Program

The Broadcast Program serves as a communication hub for the Argus system. Its functions include:

-   Facilitates message passing between all programs using a **Redis Pub/Sub** architecture, ensuring real-time data distribution with a maximum retrieval latency of 100 ms.
-   Stores and manages data from sensors, set points, weather services (including min/max/avg values, e.g., wind speed avg 7.5 m/s), and program outputs in a local cache.
-   Provides an API with secure access (via API keys) to retrieve:
    -   Sensor data
    -   Set point values
    -   Weather data (min/max/avg)
    -   Program output values and statuses

## 6. Climate Energy Balance Program

The Climate Energy Balance Program uses set points from the Diurnal Set Point Program (Section 3) and weather data from the Weather Service (Section 4) via the Broadcast Program (Section 5) to calculate energy balance and control equipment, emphasizing rate management to prevent crop stress and optimize energy efficiency. The program produces exactly **9 standardized outputs** that serve as the foundation for all climate control decisions. Its key functions include:

-   Performs **PID (Proportional-Integral-Derivative) calculations** with parallel loops for:
    -   **Temperature Control**: Calculates heating (HeatReqForTemperatureControl) or ventilation (VentReqForTemperatureControl) to meet temperature targets.
    -   **Dehumidification Control**: Calculates heating (HeatReqForHumidityControl) or ventilation (VentReqForHumidityControl) to meet humidity targets.
-   Integrates weather data, including:
    -   **Outdoor temperature** (e.g., 12°C) to adjust heating/cooling demands via feed-forward logic.
    -   **Wind speed and direction** (e.g., 35 km/h) to inform ventilation strategies.
    -   **Solar radiation** (e.g., 500 W/m²) to account for heat gain, reducing heating or increasing ventilation.
    -   **Rain status** to trigger overrides (e.g., close vents if raining).
-   Calculates control values by comparing indoor conditions (e.g., temperature 25°C, humidity 75%) against set points (e.g., 22°C, 70%), producing demands like ventilation (e.g., 60%) or heating (e.g., 10%).
-   Applies **Highest Request Logic** to synthesize demands:
    -   HighestHeatRequest = MAX(Adjusted_HeatReqForTemperatureControl, Adjusted_HeatReqForHumidityControl).
    -   HighestVentRequest = MAX(Adjusted_VentReqForTemperatureControl, Adjusted_VentReqForHumidityControl).
-   Implements energy-saving interlocks:
    -   **Heat/Vent Dehumidification Interlock** (Less ‘P’ Heat Component): Reduces ventilation dehumidification when heating is used to prevent energy waste.
    -   **Cooling/Heating Integral Interlock**: Zeros the cooling integral when heating is active (HighestHeatRequest > 0%) to prevent oscillations.
-   **Produces 9 Energy Balance Program Outputs:**
    1. **VentReqForTemperatureControl** - Ventilation Required for Temperature Control (0-100%)
    2. **VentReqForHumidityControl** - Ventilation Required for Humidity Control (0-100%)
    3. **HighestVentRequest** - Highest Ventilation Request (MAX of outputs 1 & 2)
    4. **SumOfVentRequests** - Sum of Ventilation Requests (SUM of outputs 1 & 2)
    5. **HeatReqForTemperatureControl** - Heating Required for Temperature Control (0-100%)
    6. **HeatReqForHumidityControl** - Heating Required for Humidity Control (0-100%)
    7. **HighestHeatRequest** - Highest Heating Request (MAX of outputs 5 & 6)
    8. **SumOfHeatRequests** - Sum of Heating Requests (SUM of outputs 5 & 6)
    9. **HeatingSystemTempRequest** - Current Temperature Request to Heating System (°C)
-   Supports two main control modes, with configurations impacting operational costs:
    -   **Heating Tuning**: Offers 5 control options:
        1. **Heat Required for Temperature Control**: Configures the primary PID loop for temperature, adjusting heating based on proportional span (e.g., 4.0°C, gain = 25) and integral time.
        2. **Heat Required for Dehumidification**: Configures the secondary PID loop for humidity, with a Max Dehumidify Heat limiter (e.g., 15–20%) and proportional span (e.g., 1–2% RH offset).
        3. **Heating Light Effect**: Reduces heating demand based on solar radiation (e.g., 100–500 W/m² reduces heating by 0–50%).
        4. **Heating Outdoor Temperature Effect**: Increases heating demand based on cold outdoor temperatures, with settings for retracted (e.g., up to 80% effect) or extended (e.g., up to 50% effect) thermal screens.
        5. **Heating System Request Temperature**: Maps the final heating demand (HighestHeatRequest) to hardware-specific parameters, producing HeatingSystemTempRequest (e.g., hydronic pipe temperature from 30°C at 0% to 80°C at 100%).
    -   **Vent Tuning**: Offers 4 control options:
        1. **Ventilation for Temperature Control**: Configures the primary PID loop for cooling via ventilation, using a wider proportional span and a Cooling Band (e.g., 2°C dead band) to prevent minor activations.
        2. **Ventilation Required for Dehumidification**: Configures the PID loop for humidity, with the Less ‘P’ Heat Component interlock and a temperature interlock to disable venting if the room is too cold.
        3. **Ventilation Light Effect**: Increases ventilation based on solar radiation (e.g., 60% multiplier on base demand).
        4. **Ventilation Outdoor Temperature Effect**: Adjusts ventilation aggressiveness based on the difference between the cooling target and outdoor temperature (e.g., tempers vent opening on hot days).
-   Each option uses individual PID calculations with feed-forward adjustments based on weather data.
-   Includes settings to prevent excessive energy consumption (e.g., Max Dehumidify Heat limiter).
-   Sends calculated values (e.g., 60% ventilation, 10% heating) to the Vent Program (Section 7) or Mixing Valve Program via the Broadcast Program (Section 5).
-   Handles missing weather data by using last valid values from the Broadcast cache.
-   Periodically re-evaluates indoor conditions to adjust demands dynamically, using integrated sensor readings (e.g., moving average) to dampen noise.
-   **Displays real-time output** with professionally aligned formatting showing all 9 Energy Balance Program outputs every 5 seconds for monitoring and debugging.

## 7. Vent Program

The Vent Program specializes in controlling ventilation equipment, such as roof or side vents. Its workflow includes:

-   Receives commands from the Climate Energy Balance Program (Section 6) (e.g., 60% ventilation) via the Broadcast Program (Section 5).
-   Calculates windward/leeward vent adjustments based on wind speed and direction (e.g., limit windward vents to 50% if wind speed > 7 m/s) (TBD).
-   Applies overrides based on weather data, such as:
    -   Closing vents if rain is detected.
    -   Restricting windward vents to 0% if wind speed exceeds 30 km/h.
-   Adjusts vent positions (e.g., windward 0%, leeward 60%) based on calculated adjustments and overrides.
-   Sends control signals to the I/O Program (Section 8) via the Broadcast Program (Section 5), which relays commands to physical actuators for vent operation.
-   Provides position feedback via sensors (e.g., potentiometer) to the Broadcast Program (Section 5) for system monitoring.

## 8. I/O Program

The I/O Program interfaces with the I/O Hub firmware via MQTT to manage sensor data and actuator commands for physical equipment, such as roof and side vents. Its key functions include:

-   Subscribes to MQTT for real-time sensor data (e.g., temperature, vent position feedback) every second.
-   Converts raw sensor signals (e.g., 2.4V to 24°C) and stores them in a time-series database (e.g., InfluxDB).
-   Sends processed sensor data to the Broadcast Program (Section 5) for distribution to other programs.
-   Receives actuator commands (e.g., roof vent 0%, side vent 60%) from the Broadcast Program (Section 5) and publishes them to the I/O Hub.
-   Secures MQTT communication with TLS and handles connection failures with automatic reconnects (e.g., retry every 5 seconds).
-   Ensures reliable communication between the control system and physical hardware.

## 9. Summary

The Argus Control system, guided by the **Plant Empowerment** philosophy, integrates modular components to deliver precise climate control tailored to plant biological needs. The Program Manager (Section 1) ensures seamless operation, the modular Clock (Section 2) and Diurnal Set Point (Section 3) Programs provide critical time-based inputs with smooth transitions, the Weather Service (Section 4) supplies external data, the Broadcast Program (Section 5) enables secure, modular communication and data access, the Climate Energy Balance (Section 6) calculates heating and ventilation demands using PID and feed-forward logic with energy-saving interlocks, the Vent Program (Section 7) executes vent control with windward/leeward adjustments, and the I/O Program (Section 8) interfaces with physical hardware. The system dynamically adjusts heating, ventilation, and equipment based on indoor-outdoor conditions and feedback loops, managing the rate of change to ensure efficient and flexible climate control while minimizing crop stress and energy costs.

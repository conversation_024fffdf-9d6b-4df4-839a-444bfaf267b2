# Argus multi zone

## 🏗️ Multi-Zone Architecture

### **Zone Hierarchy**

```
Hub Level (Global Services)
├── Manager Service          # System orchestration
├── Broadcast Service        # Redis communication hub
├── Clock Service           # Time synchronization
├── Weather Service         # Environmental data
├── IO Service             # Hardware interface
├── Registry Service       # Service discovery
└── Storage Service        # Data persistence

Zone Level (Per Zone)
├── Zone Manager           # Zone lifecycle management
├── Climate Service        # PID control + CEB per zone
├── Diurnal Service       # 8×8 setpoint matrix per zone
├── Vent Service          # Ventilation control per zone
└── Zone Coordination     # Resource sharing and priorities
```

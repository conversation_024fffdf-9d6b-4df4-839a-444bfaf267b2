# Clock Program

## Overview

The Clock Program is a fundamental component of the Argus Control system that provides accurate time-based functionality for all other programs. It calculates astronomical events, maintains system time synchronization, and provides essential temporal data for scheduling and control operations.

**Program Name**: Clock Program
**Compliance**: Provides time base for Argus Control Systems programs

## Architecture

### Core Components

1. **Time Management**: System time synchronization with NTP servers
2. **Astronomical Calculations**: Dawn, dusk, and solar position calculations
3. **Geographic Positioning**: Location-based solar event calculations
4. **Data Broadcasting**: Time and astronomical data distribution via Redis

## Features

### Time Synchronization

-   Network Time Protocol (NTP) integration
-   Configurable NTP server selection
-   Automatic time zone handling
-   Sync status monitoring

### Astronomical Calculations

-   **Dawn/Dusk Times**: Sunrise/sunset calculations (sun at horizon, 0°)
-   **Solar Noon**: Precise solar noon calculation
-   **Day Length**: Accurate daylight duration
-   **Solar Position**: Real-time sun elevation and azimuth
-   **Daytime Detection**: Boolean daytime/nighttime status

### Geographic Support

-   Latitude/longitude-based calculations
-   Timezone-aware time display
-   Seasonal variation handling
-   Polar day/night detection

## Configuration

The Clock Program uses `config/clockConfig.json`:

```json
{
    "programName": "Clock Program",
    "enabled": true,
    "latitude": 17.407104033722273,
    "longitude": 78.38716849147556,
    "timezone": "Asia/Kolkata",
    "ntpServer": "pool.ntp.org",
    "updateInterval": 60
}
```

### Configuration Parameters

-   **`programName`**: Display name for the program
-   **`enabled`**: Enable/disable the Clock Program
-   **`latitude`**: Geographic latitude in decimal degrees (-90 to +90)
-   **`longitude`**: Geographic longitude in decimal degrees (-180 to +180)
-   **`timezone`**: IANA timezone identifier (e.g., "America/New_York")
-   **`ntpServer`**: NTP server hostname for time synchronization
-   **`updateInterval`**: Update frequency in seconds (default: 60)

## Data Outputs

The Clock Program stores the following data in Redis:

### Time Data

-   `clock:current_time` - Current time (HH:MM:SS format)
-   `clock:date` - Current date (YYYY-MM-DD format)
-   `clock:day_of_week` - Day of week name
-   `clock:day_of_year` - Day number in year (1-366)

### Astronomical Data

-   `clock:dawn` - Dawn time (HH:MM:SS format)
-   `clock:dusk` - Dusk time (HH:MM:SS format)
-   `clock:solar_noon` - Solar noon time (HH:MM:SS format)
-   `clock:day_length` - Day length in hours
-   `clock:sun_elevation` - Sun elevation angle in degrees
-   `clock:sun_azimuth` - Sun azimuth angle in degrees
-   `clock:is_daytime` - Boolean daytime status (1.0 = day, 0.0 = night)

### System Status

-   `clock:last_ntp_sync` - Last NTP synchronization timestamp
-   `clock:ntp_sync_status` - NTP synchronization status

## Usage

### Enabling Clock Program

1. Run the main program: `./program-manager`
2. Select option `1` (clock) when prompted
3. The system will automatically create default configuration if none exists
4. Clock will start processing every 60 seconds (configurable)

### Testing

Use the test script to check Clock Program data:

```bash
go run test/test_clock.go
```

### Example Output

The Clock Program displays formatted output every update cycle:

```
Clock: Current Time:      23:30:25
Clock: Date:              2025-06-22
Clock: Day of Week:       Sunday
Clock: Dawn:              04:52:57
Clock: Dusk:              18:23:59
Clock: Is Daytime:        No
```

## Integration

The Clock Program integrates with:

-   **Diurnal Setpoint Program**: Provides dawn/dusk times for relative scheduling
-   **Climate Energy Balance**: Time-based control logic
-   **Weather Monitoring**: Timestamp synchronization
-   **Broadcast Program**: Time data distribution

### API Usage

```go
// Example: Get current time
currentTime, _ := redis.GetString(ctx, "clock:current_time")

// Example: Get dawn/dusk times
dawn, _ := redis.GetString(ctx, "clock:dawn")
dusk, _ := redis.GetString(ctx, "clock:dusk")

// Example: Check if it's daytime
isDaytime, _ := redis.GetFloat(ctx, "clock:is_daytime")
if isDaytime == 1.0 {
    // It's daytime
}
```

## Testing

### Unit Tests

```bash
go test ./clock -v
```

### Integration Tests

```bash
go run test/test_clock.go
```

### Test Coverage

-   ✅ Controller initialization
-   ✅ Time update functionality
-   ✅ Astronomical calculations
-   ✅ Julian day calculations
-   ✅ Redis integration
-   ✅ Disabled controller handling

## Technical Details

### Astronomical Algorithms

The Clock Program uses standard astronomical algorithms:

1. **Julian Day Calculation**: Converts calendar dates to Julian day numbers
2. **Solar Position**: Calculates sun elevation and azimuth using orbital mechanics
3. **Sunrise/Sunset Calculation**: Uses astronomical sunrise/sunset (sun at horizon, 0°)
4. **Equation of Time**: Accounts for Earth's orbital eccentricity

### Accuracy

-   **Time Accuracy**: Depends on NTP server synchronization (typically ±1 second)
-   **Astronomical Accuracy**: ±2 minutes for dawn/dusk times
-   **Solar Position**: ±0.1° for elevation and azimuth

### Performance

-   **Update Frequency**: Configurable (default 60 seconds)
-   **CPU Usage**: Minimal (astronomical calculations are lightweight)
-   **Memory Usage**: Low (stateless calculations)

## Troubleshooting

### Common Issues

1. **NTP Sync Failures**: Check network connectivity and NTP server availability
2. **Incorrect Times**: Verify timezone configuration
3. **Invalid Coordinates**: Ensure latitude/longitude are within valid ranges

### Debugging

Enable verbose logging to see detailed calculation steps and Redis operations.

## Future Enhancements

Potential improvements:

1. **Multiple NTP Servers**: Fallback server support
2. **Custom Twilight Angles**: Configurable twilight definitions
3. **Moon Phase Calculations**: Lunar position and phase data
4. **Seasonal Events**: Equinox and solstice calculations
5. **Time Zone Database**: Automatic timezone detection

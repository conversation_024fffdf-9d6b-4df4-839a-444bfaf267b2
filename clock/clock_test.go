package clock

import (
	"context"
	"testing"
	"time"

	"program-manager/redis"
	"program-manager/types"
)

// TestNewClockController tests Clock controller initialization
func TestNewClockController(t *testing.T) {
	config := types.ClockConfig{
		ProgramName:    "Test Clock",
		Enabled:        true,
		Latitude:       17.407104033722273,
		Longitude:      78.38716849147556,
		Timezone:       "Asia/Kolkata",
		NTPServer:      "pool.ntp.org",
		UpdateInterval: 60,
		RedisTopics: types.ClockRedisTopics{
			CurrentTime: "test:clock:currentTime",
			Date:        "test:clock:date",
			DayOfWeek:   "test:clock:dayOfWeek",
			Dawn:        "test:clock:dawn",
			Dusk:        "test:clock:dusk",
			IsDaytime:   "test:clock:isDaytime",
		},
	}

	controller := NewClockController(config)

	if controller == nil {
		t.Fatal("NewClockController returned nil")
	}

	if !controller.IsEnabled() {
		t.<PERSON>rror("Controller should be enabled")
	}

	if controller.config.ProgramName != "Test Clock" {
		t.<PERSON><PERSON><PERSON>("Expected program name 'Test Clock', got '%s'", controller.config.ProgramName)
	}
}

// TestUpdateCurrentTime tests time update functionality
func TestUpdateCurrentTime(t *testing.T) {
	config := types.ClockConfig{
		ProgramName:    "Test Clock",
		Enabled:        true,
		Latitude:       17.407104033722273,
		Longitude:      78.38716849147556,
		Timezone:       "Asia/Kolkata",
		NTPServer:      "pool.ntp.org",
		UpdateInterval: 60,
		RedisTopics: types.ClockRedisTopics{
			CurrentTime: "test:clock:currentTime",
			Date:        "test:clock:date",
			DayOfWeek:   "test:clock:dayOfWeek",
			Dawn:        "test:clock:dawn",
			Dusk:        "test:clock:dusk",
			IsDaytime:   "test:clock:isDaytime",
		},
	}

	controller := NewClockController(config)
	controller.updateCurrentTime()

	// Check that time fields are populated
	if controller.state.CurrentTime == "" {
		t.Error("Current time should not be empty")
	}

	if controller.state.Date == "" {
		t.Error("Date should not be empty")
	}

	if controller.state.DayOfWeek == "" {
		t.Error("Day of week should not be empty")
	}

	// Validate time format (HH:MM:SS)
	if _, err := time.Parse("15:04:05", controller.state.CurrentTime); err != nil {
		t.Errorf("Invalid time format: %s", controller.state.CurrentTime)
	}

	// Validate date format (YYYY-MM-DD)
	if _, err := time.Parse("2006-01-02", controller.state.Date); err != nil {
		t.Errorf("Invalid date format: %s", controller.state.Date)
	}
}

// TestCalculateAstronomicalData tests astronomical calculations
func TestCalculateAstronomicalData(t *testing.T) {
	config := types.ClockConfig{
		ProgramName:    "Test Clock",
		Enabled:        true,
		Latitude:       17.407104033722273,
		Longitude:      78.38716849147556,
		Timezone:       "Asia/Kolkata",
		NTPServer:      "pool.ntp.org",
		UpdateInterval: 60,
		RedisTopics: types.ClockRedisTopics{
			CurrentTime: "test:clock:currentTime",
			Date:        "test:clock:date",
			DayOfWeek:   "test:clock:dayOfWeek",
			Dawn:        "test:clock:dawn",
			Dusk:        "test:clock:dusk",
			IsDaytime:   "test:clock:isDaytime",
		},
	}

	controller := NewClockController(config)
	controller.calculateAstronomicalData()

	// Check that astronomical data is calculated
	if controller.state.Dawn == "" {
		t.Error("Dawn time should not be empty")
	}

	if controller.state.Dusk == "" {
		t.Error("Dusk time should not be empty")
	}

	// Validate time formats
	if _, err := time.Parse("15:04:05", controller.state.Dawn); err != nil {
		t.Errorf("Invalid dawn time format: %s", controller.state.Dawn)
	}

	if _, err := time.Parse("15:04:05", controller.state.Dusk); err != nil {
		t.Errorf("Invalid dusk time format: %s", controller.state.Dusk)
	}
}

// TestAstronomicalLibraryIntegration tests integration with go-sunrise library
func TestAstronomicalLibraryIntegration(t *testing.T) {
	config := types.ClockConfig{
		ProgramName:    "Test Clock",
		Enabled:        true,
		Latitude:       17.407104033722273,
		Longitude:      78.38716849147556,
		Timezone:       "Asia/Kolkata",
		NTPServer:      "pool.ntp.org",
		UpdateInterval: 60,
		RedisTopics: types.ClockRedisTopics{
			CurrentTime: "test:clock:currentTime",
			Date:        "test:clock:date",
			DayOfWeek:   "test:clock:dayOfWeek",
			Dawn:        "test:clock:dawn",
			Dusk:        "test:clock:dusk",
			IsDaytime:   "test:clock:isDaytime",
		},
	}

	controller := NewClockController(config)
	controller.calculateAstronomicalData()

	// Test that dawn/dusk times are calculated using go-sunrise library
	if controller.state.Dawn == "" {
		t.Error("Dawn time should not be empty when using go-sunrise library")
	}

	if controller.state.Dusk == "" {
		t.Error("Dusk time should not be empty when using go-sunrise library")
	}

	// Validate time formats
	if _, err := time.Parse("15:04:05", controller.state.Dawn); err != nil {
		t.Errorf("Invalid dawn time format from go-sunrise: %s", controller.state.Dawn)
	}

	if _, err := time.Parse("15:04:05", controller.state.Dusk); err != nil {
		t.Errorf("Invalid dusk time format from go-sunrise: %s", controller.state.Dusk)
	}
}

// TestProcessCycleIntegration tests full processing cycle with Redis
func TestProcessCycleIntegration(t *testing.T) {
	// Initialize Redis for testing
	redis.Initialize()
	ctx := context.Background()

	config := types.ClockConfig{
		ProgramName:    "Test Clock",
		Enabled:        true,
		Latitude:       17.407104033722273,
		Longitude:      78.38716849147556,
		Timezone:       "Asia/Kolkata",
		NTPServer:      "pool.ntp.org",
		UpdateInterval: 60,
		RedisTopics: types.ClockRedisTopics{
			CurrentTime: "test:clock:currentTime",
			Date:        "test:clock:date",
			DayOfWeek:   "test:clock:dayOfWeek",
			Dawn:        "test:clock:dawn",
			Dusk:        "test:clock:dusk",
			IsDaytime:   "test:clock:isDaytime",
		},
	}

	controller := NewClockController(config)

	// Run one processing cycle
	err := controller.ProcessCycle(ctx)
	if err != nil {
		t.Fatalf("ProcessCycle failed: %v", err)
	}

	// Verify data is stored in Redis using configured topics
	if val, err := redis.GetString(ctx, config.RedisTopics.CurrentTime); err != nil {
		t.Errorf("Failed to get current time from Redis: %v", err)
	} else if val == "" {
		t.Error("Current time should not be empty in Redis")
	}

	if val, err := redis.GetString(ctx, config.RedisTopics.Dawn); err != nil {
		t.Errorf("Failed to get dawn time from Redis: %v", err)
	} else if val == "" {
		t.Error("Dawn time should not be empty in Redis")
	}

	if val, err := redis.GetFloat(ctx, config.RedisTopics.IsDaytime); err != nil {
		t.Errorf("Failed to get daytime status from Redis: %v", err)
	} else if val < 0 || val > 1 {
		t.Error("Daytime status should be 0 or 1 in Redis")
	}
}

// TestDisabledController tests that disabled controller doesn't process
func TestDisabledController(t *testing.T) {
	config := types.ClockConfig{
		ProgramName:    "Test Clock",
		Enabled:        false, // Disabled
		Latitude:       17.407104033722273,
		Longitude:      78.38716849147556,
		Timezone:       "Asia/Kolkata",
		NTPServer:      "pool.ntp.org",
		UpdateInterval: 60,
		RedisTopics: types.ClockRedisTopics{
			CurrentTime: "test:clock:currentTime",
			Date:        "test:clock:date",
			DayOfWeek:   "test:clock:dayOfWeek",
			Dawn:        "test:clock:dawn",
			Dusk:        "test:clock:dusk",
			IsDaytime:   "test:clock:isDaytime",
		},
	}

	controller := NewClockController(config)

	if controller.IsEnabled() {
		t.Error("Controller should be disabled")
	}

	// ProcessCycle should return nil for disabled controller
	ctx := context.Background()
	err := controller.ProcessCycle(ctx)
	if err != nil {
		t.Errorf("ProcessCycle should return nil for disabled controller, got: %v", err)
	}
}

package clock

import (
	"context"
	"fmt"
	"log"
	"time"

	"program-manager/redis"
	"program-manager/types"

	"github.com/nathan-osman/go-sunrise"
)

// ClockController manages the Clock Program functionality
type ClockController struct {
	config types.ClockConfig
	state  types.ClockState
}

// NewClockController creates a new Clock controller
func NewClockController(config types.ClockConfig) *ClockController {
	return &ClockController{
		config: config,
		state:  types.ClockState{},
	}
}

// IsEnabled returns whether the clock program is enabled
func (c *ClockController) IsEnabled() bool {
	return c.config.Enabled
}

// ProcessCycle executes one complete Clock calculation cycle
func (c *ClockController) ProcessCycle(ctx context.Context) error {
	if !c.config.Enabled {
		return nil
	}

	log.Printf("\nClock: Starting processing cycle...")

	// Update current time
	c.updateCurrentTime()

	// Calculate astronomical data
	c.calculateAstronomicalData()

	// Store all clock data in Redis
	if err := c.storeClockData(ctx); err != nil {
		log.Printf("Clock: Error storing data to Redis: %v", err)
		return err
	}

	// Display current status
	c.displayStatus()

	return nil
}

// updateCurrentTime updates the current time and date information
func (c *ClockController) updateCurrentTime() {
	now := time.Now()

	// Load timezone if specified
	if c.config.Timezone != "" {
		if loc, err := time.LoadLocation(c.config.Timezone); err == nil {
			now = now.In(loc)
		} else {
			log.Printf("Clock: Warning - Invalid timezone '%s', using local time", c.config.Timezone)
		}
	}

	c.state.CurrentTime = now.Format("15:04:05")
	c.state.Date = now.Format("2006-01-02")
	c.state.DayOfWeek = now.Weekday().String()
	log.Printf("updated current time %s", c.state.CurrentTime)
}

// calculateAstronomicalData calculates sunrise/sunset (dawn/dusk) and daytime status using go-sunrise library
func (c *ClockController) calculateAstronomicalData() {
	now := time.Now()

	// Load timezone if specified
	if c.config.Timezone != "" {
		if loc, err := time.LoadLocation(c.config.Timezone); err == nil {
			now = now.In(loc)
		}
	}

	// Calculate sunrise and sunset (dawn and dusk) - sun at horizon (0°)
	dawn, dusk := c.calculateSunriseSunset(
		c.config.Latitude, c.config.Longitude,
		now.Year(), now.Month(), now.Day(),
	)

	// Convert to local timezone if specified
	if c.config.Timezone != "" {
		if loc, err := time.LoadLocation(c.config.Timezone); err == nil {
			dawn = dawn.In(loc)
			dusk = dusk.In(loc)
		}
	}

	c.state.Dawn = dawn.Format("15:04:05")
	c.state.Dusk = dusk.Format("15:04:05")

	// Determine if it's daytime using sunrise/sunset times (dawn to dusk)
	currentHour := now.Hour()
	currentMinute := now.Minute()
	currentSecond := now.Second()
	currentTimeSeconds := currentHour*3600 + currentMinute*60 + currentSecond

	dawnTime, _ := time.Parse("15:04:05", c.state.Dawn)
	duskTime, _ := time.Parse("15:04:05", c.state.Dusk)

	dawnSeconds := dawnTime.Hour()*3600 + dawnTime.Minute()*60 + dawnTime.Second()
	duskSeconds := duskTime.Hour()*3600 + duskTime.Minute()*60 + duskTime.Second()

	c.state.IsDaytime = currentTimeSeconds >= dawnSeconds && currentTimeSeconds <= duskSeconds
}

// calculateSunriseSunset calculates sunrise and sunset times (dawn and dusk) when the sun is at horizon (0°)
func (c *ClockController) calculateSunriseSunset(latitude, longitude float64, year int, month time.Month, day int) (time.Time, time.Time) {
	// Use the go-sunrise library's built-in SunriseSunset function for accurate sunrise/sunset calculation
	sunrise, sunset := sunrise.SunriseSunset(latitude, longitude, year, month, day)
	return sunrise, sunset
}

// storeClockData stores essential clock data in Redis using configured topics
func (c *ClockController) storeClockData(ctx context.Context) error {
	// Store essential clock values using configured Redis topics
	clockData := map[string]interface{}{
		c.config.RedisTopics.CurrentTime: c.state.CurrentTime,
		c.config.RedisTopics.Date:        c.state.Date,
		c.config.RedisTopics.DayOfWeek:   c.state.DayOfWeek,
		c.config.RedisTopics.Dawn:        c.state.Dawn,
		c.config.RedisTopics.Dusk:        c.state.Dusk,
		c.config.RedisTopics.IsDaytime:   c.state.IsDaytime,
	}

	for key, value := range clockData {
		switch v := value.(type) {
		case string:
			if err := redis.SetString(ctx, key, v); err != nil {
				return fmt.Errorf("failed to store %s: %v", key, err)
			}
		case bool:
			var boolVal float64
			if v {
				boolVal = 1.0
			}
			if err := redis.SetFloat(ctx, key, boolVal); err != nil {
				return fmt.Errorf("failed to store %s: %v", key, err)
			}
		}
	}

	return nil
}

// GetCurrentState returns the current clock state
func (c *ClockController) GetCurrentState() *types.ClockState {
	return &c.state
}

// displayStatus displays the current clock status
func (c *ClockController) displayStatus() {
	log.Printf("Clock: %-20s %s", "Current Time:", c.state.CurrentTime)
	log.Printf("Clock: %-20s %s", "Date:", c.state.Date)
	log.Printf("Clock: %-20s %s", "Day of Week:", c.state.DayOfWeek)
	log.Printf("Clock: %-20s %s", "Dawn:", c.state.Dawn)
	log.Printf("Clock: %-20s %s", "Dusk:", c.state.Dusk)

	daytimeStatus := "No"
	if c.state.IsDaytime {
		daytimeStatus = "Yes"
	}
	log.Printf("Clock: %-20s %s", "Is Daytime:", daytimeStatus)
}

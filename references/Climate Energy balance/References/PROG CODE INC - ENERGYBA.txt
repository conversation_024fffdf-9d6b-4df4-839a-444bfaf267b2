;	NOLIST

X1:	EQU	0
Y1:	EQU	2
X2:	EQU	4
Y2:	EQU	6


	;SETTINGS FOR HEATING,COOLING & DEHUMIDIFICATION
	;
	;TEMPERATURE VENTILATION
	off .set 0		;INITIALIZE THE OFFSET VALUE 'OFF=0'
 o	PSPN_Pointer,4	;P SPAN
 o	IKFC,2	;I FACTOR
 o	DDBD,2	;COOLING DEADBAND
 o	CLFXL,0
	;
	;TEMPERATURE HEATING
	off .set 0		;INITIALIZE THE OFFSET VALUE 'OFF=0'
 o	HPSPN_Pointer,4	;P SPAN
 o	HIKFC,2	;I FACTOR
 o	SHKC,8	;SHADE CLOSED OUTSIDE EFFECT
 o	SHKO,8	;SHADE OPEN OUTSIDE EFFECT
 o	LTKF,8	;LIGHT EFFECT
 o	LTPM,2	;LIGHT PREDICTION MULTIPLIER
 o	HTFXL,0 
	;
	;DE-HUMIDIFY VENTILATION
	off .set 0		;INITIALIZE THE OFFSET VALUE 'OFF=0'
 o	DVPSPN_Pointer,4	;P SPAN
 o	DVIKFC,2	;I FACTOR
 o	DVVFLG,1	;FLAG BITS
 o	DVSPAR,1	;SPARE
 o	DVFXL,0
	;
	;DE-HUMIDIFY HEAT
	off .set 0		;INITIALIZE THE OFFSET VALUE 'OFF=0'
 o	DHPSPN_Pointer,4	;P SPAN
 o	DHIKFC,2	;I FACTOR
 o	DHOFFSET,2 ;DEHUMIDIFY HEATING OFFSET
 o	DHFXL,0


	;THE 'DVVFLG' DEHUMIDIFY VENTILATION FLAG BITS
	;ARE AS FOLLOWS:
HTREDU:	EQU	0	;REDUCE ON 'P' HEAT

	;VARIABLES FOR HEATING,COOLING & DEHUMIDIFICATION COOLING
	;
	;TEMPERATURE VENTILATION
	off .set 0		;INITIALIZE THE OFFSET VALUE 'OFF=0'
 o	ISUM,4	;I COMPONENT
 o	PSPN,3	;P SPAN + watchdog
 o	_spareV,1
 o	PDIF,2	;P DIFFERENCE
 o	PCMP,2	;P COMPONENT
 o	FLGS,1	;FLAGS
 o	ITMR,1	;I TIMER
 o	COMP,2	;LIGHT ADJUSTED COMPOSITE
 o	CMPS,2	;TOTAL COMPOSITE
 o	CMP2,2	;DEADBANDED COMPOSITE
 o	CMP3,2	;OUTSIDE TEMPERATURE ADJUSTED
 o	CLVRL,0
	;
	;TEMPERATURE HEATING
	off .set 0		;INITIALIZE THE OFFSET VALUE 'OFF=0'
 o	HISUM,4	;I COMPONENT
 o	HPSPN,3	;P SPAN + watchdog
 o	_spareVH,1
 o	HPDIF,2	;P DIFFERENCE
 o	HPCMP,2	;P COMPONENT
 o	HFLGS,1	;FLAGS
 o	HITMR,1	;I TIMER
 o	HCOMP,2	;spare
 o	HCMPS,2	;TOTAL COMPOSITE
 o	OTEF,2	;OUTSIDE TEMP. EFFECT
 o	LTEF,2	;LIGHT EFFECT after considering Outside Temp. Effect
 o	cLTEF,2	;Calculated LIGHT EFFECT
 o	HTVRL,0
	;
	;DEHUMIDIFY VENTILATION
	off .set 0		;INITIALIZE THE OFFSET VALUE 'OFF=0'
 o	DVISUM,4	;I COMPONENT
 o	DVPSPN,3	;P SPAN + watchdog
 o	_spareVDV,1
 o	DVPDIF,2	;P DIFFERENCE
 o	DVPCMP,2	;P COMPONENT
 o	DVFLGS,1	;FLAGS
 o	DVITMR,1	;I TIMER
 o	DV_PI,2	;P+I
 o	DV_OTC,2	;OUTSIDE TEMP ADJUSTED COMPOSITE
 o	DVCOMP,2	;TOTAL COMPOSITE
 o	DVVRL,0
	;
	;DEHUMIDIFY HEAT
	off .set 0		;INITIALIZE THE OFFSET VALUE 'OFF=0'
 o	DHISUM,4	;I COMPONENT
 o	DHPSPN,3	;P SPAN + watchdog
 o	_spareVDH,1
 o	DHPDIF,2	;P DIFFERENCE
 o	DHPCMP,2	;P COMPONENT
 o	DHFLGS,1	;FLAGS
 o	DHITMR,1	;I TIMER
 o	DHCOMP,2	;COMPOSITE
 o	TARGET,2	;TARGET AFTER OFFSET APPLIED
 o	DHVRL,0

	;FLAG BITS ARE AS FOLLOWS:
BELOWB:	EQU	0	;CURRENTLY BELOW COOLING BAND
INBAND:	EQU	1	;IN COOLING BAND
ABOVEB:	EQU	2	;ABOVE COOLING BAND
WASATB:	EQU	3	;WAS AT BOTTOM OF COOL BAND
WASATP:	EQU	4	;WAS AT TOP OF COOL BAND
	;MASKS
BELOWB_MASK: EQU	%00000001	;CURRENTLY BELOW COOLING BAND
INBAND_MASK: EQU	%00000010	;IN COOLING BAND
ABOVEB_MASK: EQU	%00000100	;ABOVE COOLING BAND
WASATB_MASK: EQU	%00001000	;WAS AT BOTTOM OF COOL BAND
WASATP_MASK: EQU	%00010000	;WAS AT TOP OF COOL BAND



	;SETTINGS FOR HEATING,COOLING & DEHUMIDIFICATION
	;TEMPERATURE VENTILATION
	off .set 0		;INITIALIZE THE OFFSET VALUE 'OFF=0'
;************
 o	_DEVTYP,1	;'DEVTYP' DEVICE PROGRAM ASSIGNED TYPE
 o	_ORDER,1	;'ORDER' DEVICE EXECUTION ORDERING
 o	_PWFXFL,1	;'PWFXFL' POWER STAGING FLAGS
 o	_STGTIM,1	;'STGTIM' POWER STAGING DELAY TIME
 o	_PWRTHR,4	;'PWRTHR' POWER FAIL THRESHOLD (32 BITS)
 o	_PWRPTR,4	;'PWRPTR' POWER FAIL INPUT POINTER
 o	_REQTIM,NMPTRS ;'REQTIM' FOR 'N_PTRS' POINTERS
 offalign 4	;FORCE LONG ALIGNMENT
	;SETTINGS
 o	AIRTPT,4	;AIR TEMPERATURE SENSOR POINTER
 o	BAIRTP,4	;BACKUP AIR TEMP. POINTER
 o	HUMSPT,4	;HUMIDITY SENSOR POINTER
 o	SHDPPT,4	;SHADE POSITION POINTER
 o	COLTPT,4	;COOLING TARGET POINTER
 o	HEATPT,4	;HEATING TARGET POINTER
 o	DHVTPT,4	;DEHUMIDIFY VENT TARGET POINTER
 o	DHHTPT,4	;DEHUMIDIFY HEAT TARGET	POINTER
 o	MXDHVP,4	;MAX. DEHUMIDIFY VENT POINTER
 o	MXDHHP,4	;MAX. DEHUMIDIFY HEAT POINTER
 o	XTOT_P,4	;WEATHER: OUTDOOR TEMPERATURE
 o	LITPTR,4	;LIGHT
 o	ILTPTR,4	;INTEGRATED LIGHT
	;TABLES
 o	CLPIFX,CLFXL	;TEMPERATURE COOLING
 o	HTPIFX,HTFXL	;TEMPERATURE HEATING
 o	DHPIFX,DVFXL	;DE-HUMIDIFY VENTILATION
 o	DHHIFX,DHFXL	;DE-HUMIDIFY HEAT
	;WORDS
 o	COOLING_LIGHT_EFFECT,4*2	;4-POINT TABLE X1,Y1 & X2,Y2
 o	COOLING_OUTDOOR_EFFECT,4*2	;4-POINT TABLE X1,Y1 & X2,Y2

 o	HSTMPS,2	;REQUEST START TEMPERATURE
 o	HSTMPE,2	;REQUEST MAX. TEMPERATURE
 o	REQFLG,1	;REQUEST FLAGS
 o	HTGSYS,1	;HEATING SYSTEM TO MAKE REQUESTS TO
	;
 offalign 4	;FORCE LONG ALIGNMENT
	;
	;VARIABLES FOR HEATING,COOLING & DEHUMIDIFICATION COOLING
 o	DEVVST,0
 o	PWREAD,4	;'PWREAD' POWER FAIL INPUT
 o	_WDG,1	;'WDG'
 o	DEVFLT,1	;DEVICE FAULT FLAGS
 o	DEVVFL,1	;VARIABLES FLAGS
 o	STGTMR,1	;STAGING DELAY TIMER
 o	REQTMR,NMPTRS ;FOR 'N_PTRS' POINTERS
 offalign 4	;FORCE LONG ALIGNMENT
 o	AIRTRD,2	;AIR TEMP.
 o	_WDG1,1	;WDG
 o	_SPAR1,1	;SPARE
 o	BAIRDG,2	;BACKUP AIR TEMP.
 o	_WDG2,1	;WDG
 o	_SPAR2,1	;SPARE
 o	COLTGT,2	;COOLING POINT
 o	_WDG3,1	;WDG
 o	_SPAR3,1	;SPARE
 o	HEATGT,2	;HEATING POINT
 o	_WDG4,1	;WDG
 o	_SPAR4,1	;SPARE
 o	DHVTGT,2	;DEHUMIDIFY VENT TARGET
 o	_WDG5,1	;WDG
 o	_SPAR5,1	;SPARE
 o	DHHTGT,2	;DEHUMIDIFY HEAT TARGET
 o	_WDG6,1	;WDG
 o	_SPAR6,1	;SPARE
 o	ITGLIT,2	;INTEGRATED LIGHT READING
 o	_WDG7,1	;WDG
 o	_SPAR7,1	;SPARE
 o	XTLITE,2	;LIGHT READING
 o	_WDG8,1	;WDG
 o	_SPAR8,1	;SPARE
 o	XTOTMP,2	;OUTSIDE TEMPERATURE
 o	_WDG9,1	;WDG
 o	_SPAR9,1	;SPARE
 o	HUMRDG,2	;HUMIDITY READING
 o	_WDG10,1	;WDG
 o	_SPAR10,1	;SPARE
 o	MXDHV,2	;MAX. DEHUMIDIFY VENT
 o	_WDG11,1	;WDG
 o	_SPAR11,1	;SPARE
 o	MXDHH,2	;MAX. DEHUMIDIFY HEAT
 o	_WDG12,1	;WDG
 o	_SPAR12,1	;SPARE
 o	SHDP,2	;CURRENT SHADE POSITION
 o	_WDG13,1	;WDG
 o	_SPAR13,1	;SPARE
	;WORDS
 o	HUMLSS,16*2	;16 WORD INTEGRATE LIST
 o	AIRLSS,16*2	;16 WORD INTEGRATE LIST
 o	ITAIRS,2	;SHORT TERM INTEGRATED AIR
 o	ITGHMS,2	;INTEGRATED HUMIDITY
 o	HTGREQ,2	;HEATING REQUEST TEMPERATURE
 o	COOLING_LIGHT_MULTIPLIER,2 ;CALCULATED VALUE FOR LIGHT EFFECT
 o	OUTSIDE_EFFECT_MULTIPLIER,2
 o	COOLING_DELTA_T,2 ;COOLING TARGET - OUTDOOR TEMPERATURE
 o	HEATING_DELTA_T,2 ;HEATING TARGET - OUTDOOR TEMPERATURE
 o	HEATING_PREDICTED_LIGHT,2
 o	HIGHEST_HEAT,2	;HIGHER OF THE TWO HEAT REQUESTS
 o	SUM_OF_HEAT,2	;SUM OF THE TWO HEAT REQUESTS
 o	HIGHEST_VENT,2	;HIGHER OF THE TWO VENTILATION REQUESTS
 o	SUM_OF_VENT,2	;SUM OF THE TWO VENTILATION REQUESTS
	;TABLES
 o	CLPIVR,CLVRL	;COOLING
 o	HTPIVR,HTVRL	;HEATING
 o	DHPIVR,DVVRL	;DEHUMIDIFY VENT
 o	DHHIVR,DHVRL	;DEHUMIDIFY HEAT
	;
 offalign 4	;FORCE LONG ALIGNMENT
 o	LOCAL_ALARMS_BUILDING,4
 o	LOCAL_ALARMS_BUILT,4
 offalign 4	;FORCE LONG ALIGNMENT
 o	DEVVED,0	;END OF DEVICE VARIABLES
DVLENG:	EQU	DEVVED-DEVVST	;TABLE LENGTH

	;

 	;THE 'REQFLG' BITS ARE:
USESUM:	EQU	0	;USE THE SUM OF HEATING REQUESTS


	LIST


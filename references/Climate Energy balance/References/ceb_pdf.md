# Climate Energy Balance Program

## Operator Manual

```
Rev. January 2009
```

# P R O G R A M

Climate Energy Balance Control Program Operator Manual

© 2009 Argus Control Systems Limited. All Rights Reserved.

```
This publication may not be duplicated in whole or in part by any means without the prior written permission of
Argus Control Systems Limited.
```

```
Limits of Liability and Disclaimer of Warranty:
The information in this manual is furnished for informational use only and is subject to change without notice.
Although our best efforts were used in preparing this book, Argus Control Systems Limited assumes no
responsibility or liability for any errors or inaccuracies that may appear.
```

```
Trademarks:
Argus Controls, Argus Graph, Argus Control Systems, Argus Titan, and the Argus logo are trademarks of
Argus Control Systems Limited.
```

```
Argus Control Systems Ltd.
```

```
Telephone: (************* or (604) 536- 9100
Toll Free Sales: (************* (North America)
Toll Free Service: (************* (North America)
Fax: (604) 538- 4728
E-mail: <EMAIL>
Web: http://www.arguscontrols.com
```

```
Written and designed by Argus Control Systems Limited.
Printed in Canada.
```

```
Revision Date: January 2009
```

## P R O G R A M

-   ABOUT THIS MANUAL................................................................................................................................. Table of Contents
    -   When to use this Program
    -   Alternatives
-   INTRODUCTION
-   NOTES ON CLIMATE TUNING
    -   Process Lag in Controlled Systems
        -   Is process lag a problem?
    -   Proportional Response in Controlled Systems
    -   Integral Response in Controlled Systems
    -   Air/Water Relationships
        -   Dehumidification Strategies
        -   Using Heating to Reduce Humidity
        -   Using Ventilation to Reduce Humidity
        -   Managing Dehumidification Costs
-   USING THE CLIMATE ENERGY BALANCE PROGRAM FOR CONTROL
-   PARAMETER TYPES USED IN THIS PROGRAM
    -   Settings
    -   Readings
    -   External Pointers
-   INSTALLING A CLIMATE ENERGY BALANCE PROGRAM
    -   Setting up the Program Inputs
-   USING THE CLIMATE - ENERGY BALANCE PROGRAM
    -   Energy Balance Program Summary
    -   Heating Tuning
        -   How it Works
    -   Heating Required for Temperature Control
        -   Temperature Control Parameters
        -   Heating Current Proportional
    -   Heating Required for Humidity Control
        -   How it Works
    -   Heating Outdoor Temperature Effect
        -   How it works
    -   Heating Light Effect
        -   How it Works
    -   Heating System Request Temperature
        -   How it Works
    -   Ventilation Tuning
        -   How it Works
    -   Ventilation Required for Temperature Control
        -   How it Works
        -   Integrated Temperature Reading
        -   Cooling Target
        -   Cooling Band
        -   Cooling 'P' Difference
        -   Cooling Proportional Span
        -   Current Proportional
        -   Cooling Integral Time
        -   Cooling Integral Timer
        -   Cooling Current Integral
        -   Proportional plus Integral
        -   Ventilation Light Effect Multiplier...................................................................................................
        -   Light Adjusted Cooling Total
        -   Ventilation Outdoor Temperature Effect Multiplier
        -   Outdoor Temperature Adjusted Ventilation Total P R O G R A M
    -   Ventilation Required for Dehumidification
        -   Maximum Ventilation for Dehumidification Limit
        -   Integrated Humidity Reading
        -   Ventilation Dehumidify Target
        -   Dehumidify Ventilation Difference between Reading and Target...............................................
        -   Dehumidify Ventilation Proportional Span
        -   Dehumidify Ventilation Current Proportional
        -   Dehumidify Ventilation Integral Time
        -   Dehumidify Ventilation Integral Timer
        -   Dehumidify Ventilation Current Integral
        -   Dehumidify Ventilation Proportional plus Integral
        -   Ventilation Outdoor Temperature Effect Multiplier
        -   Dehumidify Ventilation Outdoor Temperature Adjusted Total
        -   Dehumidify Ventilation Heat Reduction Setting
        -   Heating Current Proportional
        -   Total Ventilation required for Dehumidification
    -   Ventilation Light Effect
        -   How it Works
    -   Ventilation Outdoor Temperature Effect
        -   How it Works
-   INSTALLATION AND SERVICE SCREENS

# P R O G R A M

-   1 -

## ABOUT THIS MANUAL

This manual describes the operation of the Climate Energy Balance program and its typical uses. It is
intended as a manual for configuring and using the program. Setup information is also provided,
although in most cases this program will arrive fully preconfigured by Argus. If at any time you
encounter problems, or are unsure how to proceed, please call Argus for assistance.

```
Warning! The settings in this program are intended for advanced users and Argus Factory
service personnel. They do not require changing for day-to-day operation. Small
adjustments may have a very large effect on your system performance! We strongly
advise you to contact Argus before adjusting these settings.
```

### When to use this Program

Use this program when you need to coordinate heating and ventilation. It is particularly useful in
applications where the amount of heating and ventilation can be proportioned or modulated (as
opposed to ON/OFF type control). It is also useful for tuning systems with considerable process lags
and high thermal mass such as hydronic (hot water) heating systems.

### Alternatives

Other control programs, such as the Equations program, and PID equations can also be configured for
heating, ventilating, and cooling control. If you have a non-standard heating or cooling application,
please call Argus for assistance.

# P R O G R A M

-   2 -

## INTRODUCTION

The Climate Energy Balance program decides how much heat or ventilation is required. The objective
is to provide an output equipment response that just meets or balances the current energy flux between
the indoor and outdoor climates, while avoiding excessive control oscillation that can result in over or
undershooting of the targets. Equipment control for heating, cooling, and humidity management often
involves multiple equipment systems that must work in harmony to achieve the climate targets
efficiently. The program uses information from outdoor weather and climate zone sensors to analyze
the current indoor and outdoor conditions. It then compares this information to the current climate
targets and calculates a desired output response for heating and ventilation equipment.

Heating and ventilation can be used for both temperature and humidity control. The program calculates
separate heating and ventilation values for temperature and humidity control and produces separate
and combined desired heating and ventilation results. These results are expressed in generalized
percentage values from 0 % - 100 % where 0 % means that no effect is currently required, and 100 %
means that the maximum heating or ventilation available is currently required.

The Climate Energy Balance program contains many tuning settings to adjust for the dynamics of
individual climates and locations, and the natural process lags associated with heating and cooling
equipment. Tuning parameters are also used to adjust the relative response to changes in outdoor air
temperature and light energy levels. These settings generally do not require changing for day-to-day
operation.

The energy balance program produces 9 results for use in other programs:

1. Ventilation Required for Temperature Control
2. Ventilation Required for Humidity Control
3. Highest Ventilation Request
4. Sum of Ventilation Requests
5. Heating Required for Temperature Control
6. Heating Required for Humidity Control
7. Highest Heating Request
8. Sum of Heating Requests
9. Current Temperature Request to the Heating System.

These results are then used in equipment control programs such as Vent, Mixing Valve, and
Equations programs that are configured to convert these desired effect values into equipment control
responses appropriate for each style of equipment used. Finally, these equipment control results are
used in the I/O Module programs that are used to position the physical equipment.

# P R O G R A M

-   3 -

## NOTES ON CLIMATE TUNING

### Process Lag in Controlled Systems

Process lag is a general term that describes the delay between the measurement, output response,
and delivered effects in a controlled system. It is caused by several factors including:

-   Capacitance - the ability of the system to store some of the applied energy. For example, in a
    hot water heating system, a considerable amount of heat energy must be applied to (stored in)
    the water and the conduit before it can be transferred to the target area.
-   Resistance - any opposition to the flow of energy or materials in the process. For example, in
    hot water heating systems, practical limitations on the exposed surface area of the heating
    pipes or radiating fins limits the rate that heat energy can be transferred to the surrounding
    environment.
-   Dead Time or Transport Time – this is the time it takes to physically move the energy or
    controlled variable from one place to another. In a typical hot water heating system, it can take
    several minutes from the time that a mixing valve introduces additional hot water into an
    environment, and a commensurate rise in the measured temperature.
-   Sensor Lag – this is the time it takes for a change in the measured variable to be registered
    by the sensing apparatus. For climate heating applications this is usually negligible. However,
    poor air circulation in a controlled zone can result in unnecessary measurement errors and
    delay.
-   Controller Lag - the time required for the controller to process input data and produce a
    change in it's output. This is sometimes called controller latency. For climate control
    applications this is usually negligible. In practice, it is a common strategy to intentionally tune or
    ‘detune’ the controller responsiveness to match the cyclical nature of each process lag. This
    helps avoid oscillation effects.

For most applications concerned with climate control, capacitance, resistance, and transport time, are
the main contributors to process lag.

#### Is process lag a problem?

Generally, as long as your equipment is capable of producing a sufficient amount of the desired effect
(such as heating and ventilation), your control system can be tuned to compensate for process lag.
This does not make these lags go away, but it can sometimes smooth out the undesirable effects. This
is accomplished by controlling the aggressiveness with which the control system responds to a given
deviation from the setpoint. For example, during cold weather you might want the system to respond
more aggressively to a given drop in climate temperature, since it will take relatively more energy to
achieve the setpoint than in warmer weather.

For heating and ventilation control, the energy balance program addresses the problem of process lag
through the tuning settings. This is done by balancing the Proportional and Integral settings to match
the dynamics of the climate, and the capabilities of the heating and ventilation equipment. In addition,
the program provides for self adjusting modifier values to offset for the effects of outdoor temperature
and light levels. Predictive or ‘feed forward‘ settings are also provided to ramp up or down the current
control response to compensate for the predicted external effects of outdoor temperature and light
levels.

# P R O G R A M

-   4 -

### Proportional Response in Controlled Systems

A proportional response varies directly with the amount of measured deviation from the target. This
type of calculation is commonly used in feedback controlled systems. With proportional control, an
instantaneous output value is calculated in response to the degree which a measured variable has
deviated from the target. Typically, a small deviation will produce a small output response and a large
deviation will produce a larger output response.

In the above example, a proportional output response (the blue line) has been applied over the Output
Span in relation to a temperature deviation. As the Measured Temperature (the red line) drops below
the Target Temperature (the green line) the control system applies a proportioned output response
(the blue line). In this example, the 'P' Band over which the output is proportioned is 4.0 °C. Therefore,
the output will be increased evenly from 0 to 100% as the deviation increases from zero to 4.0 °C.

Notice also that at the end of the displayed time span the temperature is still slightly deviated from the
target and the output is producing a steady response (around 20%). This is a common result when
using a proportional control strategy by itself. The system has become 'balanced' just below the target
with a steady output. Since the measured deviation is neither increasing or decreasing, the output
remains steady. However, we want the temperature to be 'driven' back to the target. This happens
because proportional control alone has no mechanism for increasing its output over time other than the
measured deviation itself. As long as the deviated value is not changing, there is no way to increase the
proportional output to drive the measured value back to the target. To correct for this situation, an
Integral control strategy is often applied together with proportional control. This is often referred to as
PI control.

# P R O G R A M

-   5 -

### Integral Response in Controlled Systems

An Integral control strategy is often used in conjunction with a proportional control strategy. Integral
control considers the length of time that a deviation from the target has occurred and produces a
control response in proportion to the amount of time that has elapsed. An integral calculation is not
**concerned with the size of a deviation, only with it’s duration**. It doesn’t care about the amount of
the actual deviation (beyond some trigger threshold setting that is usually linked to the proportional
band setting), only the amount of time that the deviation has been measured.

In the above example, the integral controller notices the measured deviation (the red line) from the
target (green line) and it begins to ramp up the Heating Required desired output (blue line) over time.
Notice that it continues to increase the output value even when the measured deviation begins to
decline. This is because it is counting the time from the start of the deviation, it doesn't care about the
deviation amount. When the deviation returns to zero the integral timer begins to reset itself by reducing
its output value at the same rate that it increased it. However, while it is reducing the integral output
value, the measured deviation occurs again, so it begins timing and incrementing the output
accordingly. Notice that the desired output value has climbed even though the amount of deviation is
less than the first interval. Eventually, if the deviation is not resolved in a specified time, the output will
reach it's maximum output value.

While integral control can be used by itself in some circumstances, it is most often used as a means of
correcting for the offset phenomenon that commonly occurs when using proportional control. Should
an offset or steady state condition occur with proportional control, the integral timer will increase the
output over time to correct it. Whenever Integral control used in conjunction with a Proportional control
strategy it is referred to as PI control (Proportional/Integral). When properly tuned, a PI strategy can
produce very accurate correction of target deviations with a minimum of overshoot and oscillation.

### Air/Water Relationships

The Energy Balance program can be used to operate heating and ventilating equipment for humidity
management purposes as well as temperature management. With carefully coordinated simultaneous
heating and venting, it is possible to control humidity levels while maintaining the climate temperature
setpoint. To accomplish this, it is often necessary to be heating and venting at the same time. This must
be managed very carefully to avoid wasting energy.

# P R O G R A M

-   6 -

#### Dehumidification Strategies

Since a given volume of air can hold progressively more water vapor as its temperature increases,
heating and air exchange are the most common methods used to reduce humidity levels relative to the
dewpoint. Besides ventilation and heating, there are other strategies for dehumidification.

Removing water vapor from the air by temporarily chilling the air to below the dewpoint, thus
condensing some of the water vapor, and then reheating the resulting drier air is a common method
employed in insulated buildings equipped with mechanical refrigeration systems (air conditioning).

There are other methods as well, such as the use of chemical desiccants or brine solutions. These are
used less often in commercial situations.

Dehumidification strategies generally involve changing the psychrometric properties of the air, and in
particular, the temperature of the air, at least temporarily. The Argus Energy Balance program offers 2
strategies for dehumidification control:

1. Apply heat to increase the air temperature, thereby reducing the relative humidity
2. Ventilate – to exchange moist air with drier outdoor air

In practice, these strategies must often be used together, particularly when you are trying to hold a
steady temperature. It is also important to understand that whenever heating and ventilating are
employed for humidity management purposes, they cannot help but affect temperatures levels, since
air temperature and humidity levels are so interdependent.

Similarly, heating and venting for temperature control will affect humidity levels. In the end, whenever
we need to use the same equipment to control both conditions we can't adjust one without the other.

# P R O G R A M

-   7 -

The diagram below illustrates some of the relationships between air temperature and humidity.

The top row of arrows shows the effects on air temperature, relative humidity, vapor pressure deficit,
crop irrigation demand, and evapotranspiration (evaporation and plant transpiration) rates when
supplemental heat or the heat from light energy is applied.

The middle row shows what happens as humidity increases due to the evaporation of water in the
climate.

The third row shows the effects of ventilation: exchanging the climate air with cooler outside air.

Since humidity levels and air temperature are so interrelated, it is seldom possible to adjust one without
affecting the other.

If humidity and air temperature are so interdependent, why should we bother having separate control
calculations? Separating the two problems allows us to independently calculate the degree and nature
of the control response that is required for both humidity and temperature management, and to act
accordingly. It also allows us to set limits on the amount of heating resources we are willing to use for
humidity correction.

# P R O G R A M

-   8 -

For example, suppose we have a climate where:

-   Heating Target is 20 °C
-   Cooling Target is 25 °C
-   Dehumidify Target is 80 %
-   Current Temperature is 21 °C
-   Current Humidity is 85% Rh
-   Outdoor Temperature is 10 °C
-   Outdoor Humidity is 85% Rh

From the above information we can see that the current temperature does not require adjustment since
it is in the Deadband (non controlled area) between the Heating and the Cooling targets. Therefore,
there is currently no demand for heating or ventilation for temperature management purposes.
However, the humidity has risen above the control target and something needs to be done about it. The
Energy Balance program offers two strategies that can be used separately or together: We can apply
Heat, or we can apply Ventilation, or we can apply both.

#### Using Heating to Reduce Humidity

Let's first assume that we are going to apply Heating for Dehumidification without applying any
Ventilation for Dehumidification, here's what may happen:

1. First, the air temperature will begin to rise (because we are actively applying more heat) and
   the relative humidity will be reduced, at least temporarily. If it is reduced to below the target
   threshold, the control system will stop applying the additional heat. If we are lucky, the new
   temperature may now be 24 °C, still within the heating/cooling deadband.
2. Due to continued evaporation of free water in the climate, and the moisture released by
   animals, plants, or human activities, this drier, warmer air may well accumulate more water
   vapor, again causing the system to apply additional heating, until eventually the Heating
   Ventilation target is exceeded.
3. Even though we haven’t configured any ventilation for dehumidification, ventilation control will
   now be applied, not for dehumidification purposes, but to mange the increased temperature.
   This will result in an exchange of warm moist air climate air for cool (and apparently just as
   moist) outdoor air.
4. In the above example the outdoor air and the indoor air are at the same relative humidity
   (85%) but different temperatures. It may appear at first that bringing in air that is the same
   relative humidity won't help solve our climate humidity problems. However, the outdoor air is
   much cooler, and when we bring it in, it is going to be warmed up to at least our heating
   setpoint (20 °C). If we consult a psychometric chart (a chart used for calculating air properties
   at various temperature and humidity levels), we would see that when you take air at 10 °C , 85
   % Rh and warm it up to 20 °C the relative humidity will actually fall to 45 % Rh. Therefore
   when this cool air is reheated, the net effect will be that we have mixed in a volume of air at
   45% Rh with our 85% Rh climate air. The resulting humidity will be somewhere in between the
   two and hopefully lower than our maximum humidity setpoint.

To accomplish the dehumidification above, we caused the control system to do something that we don't
normally want to see happen; we operated our heating equipment and our cooling equipment at the
same time. However, this is often the only practical way that dehumidification can be accomplished.

# P R O G R A M

-   9 -

#### Using Ventilation to Reduce Humidity

To avoid raising the temperature all the way to the Cooling Target, we can simultaneously employ the
Ventilation for Dehumidification options in the Energy Balance program, which, if carefully
configured, will caused the ventilation machinery to act before the greenhouse temperatures rise above
the controlled levels. In this way, it may be possible to control excess humidity without significantly
affecting the climate temperature, since it is possible to exhaust warm moist air and reheat the
incoming cool air at a rate that maintains a stable climate temperature. The objective is to achieve a
steady ventilation/reheating state that maintains the climate temperature within the temperature
setpoints, while at the same time keeping the humidity levels just under the target threshold.

Ventilation and heating systems often do not produce their results at the same rate. For example,
ventilation systems can often lower the temperature faster than heating systems can raise it due to
differing process lags in the two systems. Therefore, there are offset settings in the Energy Balance
program that can help you to adjust for this. If the heating system takes longer to produce a result, you
may want to have the Heating for Dehumidification control start before the Ventilation for
Dehumidification controls. With the Proportional/Integral tuning settings for each methods, you can
control how aggressively each method will tackle the problem and provide tuning for the respective
process lags in each system. You can also control the maximum amount of each resource (Heating or
Venting) that you wish to use for dehumidification purposes.

#### Managing Dehumidification Costs

Dehumidification can be expensive, and you might not want to turn all of your heating and cooling
resources over to the control system to manage tight humidity targets. To mange this, you can set
maximum limits for both the dehumidification heating and ventilation controls to limit the amount of
resources applied by each. However, keep in mind that whenever you limit the Heating or Ventilation
resources that can be applied, you may not be able to achieve the dehumidification targets you have
established.

You should make sure that your dehumidification goals are realistic for the intended application. Don't
do any more dehumidifying than is necessary to accomplish your objectives. Also, make sure that the
sensors used to monitor humidity levels are working properly, accurate, and representative of the
average humidity conditions.

# P R O G R A M

-   10 -

## USING THE CLIMATE ENERGY BALANCE PROGRAM FOR CONTROL

The diagram below shows the typical program linkages for using the Climate Energy Balance program
in a climate control application.

# P R O G R A M

-   11 -

## PARAMETER TYPES USED IN THIS PROGRAM

### Settings

By default, operator settings are displayed in blue type:

These values are constants. They remain in use until they are changed by an operator. You can
change these values as required. When you left-click on a setting entry, a dialog appears to assist with
changing the entry:

### Readings

By default, readings are displayed in red type:

Readings display numeric and text information about control variables. They cannot be changed by the
operator.

### External Pointers

By default, external pointers are displayed in red type on a pink background:

The red Reading displays the last value of the external variable defined by the blue address pointer.
External pointers are used to assign control system values that are needed by the control program.
They are usually displayed along with their Address information to show where the assigned parameter
originates. In addition, a Cross Module Request time setting and a delay timer reading are also
displayed. These cross module parameters are only used if the reading originates on another
controller. They are used to manage the flow of information between controllers (cross module
communications traffic) on the system networks.

```
External pointers are generally configured as part of the initial setup of the program and
they should not need to be changed by the operator. Changing a pointer assignment can
lead to serious malfunctions of the control linkages, and the controlled equipment.
```

# P R O G R A M

-   12 -

## INSTALLING A CLIMATE ENERGY BALANCE PROGRAM

```
Note: Normally, your system will arrive fully preconfigured and you should not need to adjust
the setup information. If you are adding a new program, then you may need to follow these
steps with assistance from an Argus service representative.
```

1. To add a new Climate Energy Balance program, make sure you a logged on with an
   appropriate password, and then navigate to the Group Window where you want to place the
   menu entry for the program. Right-click on an empty space on the group window screen and
   select Add Program:

```
Note: You can only install a program in a Group-type window - otherwise the selection will
be grayed out:
```

```
Group Windows are used to add identifying prefixes to the parameter labels of any programs
that are contained within them. For example, if a group window is named Zone 1:Z1, then the
individual parameter labels in each control program that is displayed in the Group window, will
all start with the prefix Z1. This makes it easy to distinguish between identical parameters
when they are used in different control groups or zones.
```

# P R O G R A M

-   13 -

2. After selecting Add Program the following dialog appears:
3. As displayed in the above example, select the Controller where the program will be installed
   and the Program Type from the list. Select Climate **–** Energy Balance. A second dialog is
   displayed:

```
Select Default Settings to install a program with Factory default values, or a previously
configured and saved copy of program settings (if any exist) from the selection list.
```

# P R O G R A M

-   14 -

```
Your new program is displayed:
```

4. If desired, select the Program Name parameter at the top of the screen and enter a name to
   describe this program:

You are now ready to begin setting up and using the Climate Energy Balance program.

# P R O G R A M

-   15 -

### Setting up the Program Inputs

At the bottom of the main screen there is a menu entry for Climate Energy Balance Input Values.
From this screen you can assign all of the inputs needed to operate the program. These assigned
inputs also appear in the program sections where they are used. The example below shows the typical
assignments for these inputs.

Each of the above inputs is described later in this document in the context of the program sections
where they are used.

# P R O G R A M

-   16 -

## USING THE CLIMATE - ENERGY BALANCE PROGRAM

### Energy Balance Program Summary

This section on the main screen shows the current readings and output values for the program. The
Zone Temperature, Current heating Target, and Current Cooling Target values are the readings
from the assigned inputs for these sources.

In the above example, since the Zone Temperature is currently between the heating and cooling
targets, the Climate Energy Balance program does not want any heating or venting and it is outputting
[0%] for all calculated output values. At [0%], the program wants no heat, and at [100%], the program
wants the maximum amount of heating possible. The logic for producing these values is configured in
the screens Heating Tuning.

Separate heating and venting calculations are performed for both temperature control and humidity
management. For standard heating control applications, the Highest Heat Request reading is
normally used to drive the heating equipment. If needed, the Sum of Heat Requests is also available.
For standard ventilation control applications, the Highest Ventilation Request is normally used to
drive the venting equipment. If needed, the Sum of Ventilation Requests is also available.

The Heating System Request Temperature is produced from the Heating System Request
Temperature Setting section.

### Heating Tuning

This section includes all of the parameters that are used to calculate the current heating demand for
temperature and humidity control. It consists of the following subsections:

-   Heating Required for Temperature Control
-   Heating Required for Dehumidification
-   Heating System Request Temperature
-   Heating Outdoor Temperature Effect
-   Heating Light Effect

# P R O G R A M

-   17 -

#### How it Works

One way to understand how this section works is to imagine what would happen if we didn’t use any
climate tuning at all, and could not calculate a proportioned response.

Heating systems are normally designed for the worst case scenario. The maximum amount of heat
energy they can supply has been calculated to maintain a defined climate temperature on the coldest
day of the year. Another way of expressing this is that they have been engineered to maintain a certain
rise in temperature above the outside temperature when running flat out.

Without climate tuning and proportioned control, as soon as the temperature in the climate drops by
just a fraction below the setpoint, the full resources of the heating system (100%) will be requested.
This will cause the heating equipment to supply all the heat it can, as soon as it can. This might take a
while, depending on the process lag your heating equipment, during which time, the climate
temperature might continue to drop. Eventually, the heat is delivered. It takes more time for the heat
energy to be transferred to the air, and for the temperature sensors to register a rise in temperature. By
the time the measured climate temperature rises above the setpoint, the heating system may have
delivered far more heat than is necessary due to these process lags. Even if we shut off the heat
supply as soon as the target temperature is reached, the residual heat energy in the system may cause
the climate temperature to rise well above the setpoint. This is called ‘overshoot’.

The above scenario could also result in ‘undershoot’ problems as well, since it will tend to operate the
heating system in an ‘all-or-nothing’ fashion. The following example illustrates the classic temperature
oscillation behavior typical with this type of unturned feedback control:

The Heating Tuning section of this program attempts to correct both overshoot and undershoot
problems by producing proportioned heating request values that will result in more ‘steady state’
conditions, where the correct amount of heat is added for the current conditions, with compensation for
process lags. Instead of requesting 100% heat as soon as the climate temperature drops below the
setpoint, the program begins asking for heat in proportion to the amount of deviation, and how long the
deviation has occurred. Therefore, while a temperature deviation is relatively minor and recent, the
system will ask for a correspondingly small heating response. If the deviation persists for a longer time,
or increases, the program asks for more heat. Similarly, as the measured temperature begins to rise
back towards the setpoint, the program begins to taper off the heating demand. The illustration below
shows a properly tuned heating response:

# P R O G R A M

-   18 -

Even if you use on/off heating equipment such as unit heaters, the Climate - Energy Balance program
will calculate variable on and off times to match the output of the equipment to the current conditions.

The primary method for tuning this type of measured response is to use the Heating Proportional
Span and Heating Integral Timer settings in the Heat Required for Temperature Control section.

The Heating tuning section also contains built-in Heating Modifiers to compensate for the effects of
outdoor temperature and outdoor light levels.

If you require additional, or different heating modifiers, these can be configured in other control
programs by using the Heating Required for Temperature Control result from this program as an
input in a control program that is configured to modify this value based on other measured conditions
(for example, heat loss due to wind).

### Heating Required for Temperature Control

As the name implies this section calculates the amount of heating that is required to maintain the
current target temperature. It considers the current zone temperature conditions in relation to the target
temperature and develops a proportioned result from 0 - 100%. At zero percent, no heat is required for
temperature management. At 100%, the maximum heating resources are required to meet the current
demand.

# P R O G R A M

-   19 -

At any time, an enclosed climate may be either:

-   Losing heat energy to the surrounding environment
-   Gaining heat energy from the surrounding environment
-   In balance with the surrounding environment (neither gaining nor losing energy).
    At times when the controlled climate is losing heat energy, supplemental heating is used to
    maintain temperature. Since the rate of heat loss or gain is constantly changing due to fluctuating
    external factors such as wind, solar radiation, rain, snow, and relative humidity levels, the heating
    system must be able to deliver an amount of heat that correctly matches the current rate of loss if it
    is to maintain a steady temperature. This is normally accomplished by either varying the ON/OFF
    ratio of devices such as unit heaters, or by controlling the amount of heat delivered by proportional
    systems such as hot water heating systems.
    Whenever the heating system is providing the correct amount of heat needed to maintain the target
    temperature setpoint, the amount of supplemental heat that is being applied, will match the amount
    of heat that is being lost. To accomplish this, it is necessary for the control system to measure the
    current conditions and compare them to the current target temperature values for the climate.
    Since most heating systems cannot deliver heat instantly in response to a control system request,
    there is usually a Process Lag that must be taken into account when tuning the climate response.
    This portion of the Energy Balance program calculates the current amount of heat that is required
    for temperature control. It uses the following steps to determine the current ventilation required:

1. The program first calculates the difference between the Heating Target and the Integrated
   Temperature Reading (the measured zone temperature). The result is expressed as the
   Heating Target minus Integrated Temperature reading.
2. The difference between the heating target and the measured zone temperature value is then
   proportioned over the operator defined Heating Proportional Span setting and a desired
   Heating Current Proportional value is calculated. It is expressed as percent of ventilation
   required.
3. If Integral control is used, the Heating Integral Timer will also accumulate whenever there is
   a deviation greater than the deadband. The rate of Integral accumulation is determined by the
   Heating Integral Time setting. This results in the Heating Current Integral value which is
   also expressed as a percent of ventilation required.
4. The above proportional and integral values are then added together..
5. If a Heating Outdoor Temperature Effect is used, then the Heating Outdoor Temperature
   Effect amount will be added.
6. If a Heating Light Effect modifier is used then the current Heating Light Effect amount will be
   added.
7. The final result is the Total Heating required for Temperature Control reading. This value
   can then used by other control programs to operate the heating equipment accordingly.

#### Temperature Control Parameters

## Heating Target

This is the temperature that the program is trying to achieve. This reading displays the Heating Target
value from the assigned Heating Target source value. Normally, a Scheduled Heating setpoint value
from a Diurnal Setpoints schedule or an Multi Step Setpoint schedule is assigned as the Heating
Target.

# P R O G R A M

-   20 -

## Integrated Temperature Reading

This reading displays the current integrated zone temperature. If a backup Zone Temperature reading
sensor is assigned this reading will display the backup value if the primary Zone Temperature Reading
sensor fails.

## Heating Target minus Integrated Temperature Reading

This reading value displays the current calculated difference between the Integrated Temperature
reading and the current Heating Target. This difference is compared to the Heating Proportional
Span of “P” value to calculate a proportional response.

## Heating Proportional Span

Enter a temperature span value over which to proportion the output response. This setting is entered as
the number of degrees below the Heating Target at which the Proportional value will be 100 %.

Proportional settings are used to scale or proportion a control response over a specified range.
Proportional heating is based on the difference between the measured Integrated Temperature
Reading and the Heating Target. The difference (whether positive or negative) is used with this setting
to calculate the Heating Current Proportional value.

Example:

```
Heating Target 20.0 °C
```

-   Integrated Temperature Reading - 18.0 °C
    = Heating Target minus Integrated Temperature Reading = 2.0 °C
    / Heating Proportional Span / 4.0 °C
    = Heating Current Proportional = 50 %

In the above example, the Heating Proportional Span has been set to 4.0 °C. When the Heating
Target minus Integrated Temperature Reading reaches this threshold, the Heating Current
Proportional will be at 100%. Since the current difference is only 2.0 °C, (which is half of 4.0 °C), the
heating current proportional is currently 50%.

For most systems, a reasonable Heating Proportional Span value is around 4.0 °C (7.2°F). Smaller
values will tend to result in 'overshoot', with the controller requesting more heat than is required. Larger
values can result in an unresponsive system. These reactions will vary from zone to zone depending
upon the responsiveness of the heating equipment, the rate of heat loss, and the associated process
lags.

For tuning, start with the suggested default values and data record all of the parameters. Observe the
responsiveness and accuracy of temperature control over time, and adjust as required.

# P R O G R A M

-   21 -

#### Heating Current Proportional

This reading displays the current Heating Proportional value for this climate. It indicates how much
heating response is currently required due to deviation of the Integrated Temperature Reading from the
Heating Target. It is calculated as follows:

Example:

```
Heating Target 20.0 °C
```

-   Integrated Temperature Reading - 18.0 °C
    = Heating Target minus Integrated Temperature Reading = 2.0 °C
    / Heating Proportional Span / 4.0 °C
    = Heating Current Proportional = 50 %

## Heating Integral Time

Enter an integral value between 0 and 255 seconds.

The Factory Default setting is 50 seconds.

Set to zero to disable entirely.

Integral settings are used in a variety of control functions to deliver an output response based upon the
length of time a current value has deviated from its target value. The Argus system calculates an
integral value based upon the current deviation of the Integrated Temperature Reading from the
Heating Target and the length of time the deviation has been in effect.

Each time the Heating Integral Timer counts up to the Heating Integral Time value, the Heating
Current Integral value is incremented. Therefore small Heating Integral Time setting value will result in
more aggressive integral response (the calculated integral response will change rapidly). A larger
integral setting value will result in less aggressive integral response (the calculated integral response
will change slowly).

Overly aggressive settings (where the integral setting value is too small) can cause excessive
equipment cycling and poor control. Settings that are too passive (where the integral setting value is to
large) will also produce poor control due to lack of equipment responsiveness.

Setting values of 30 to 100 seconds are typical. Zero will disable Integral Control. Try a larger
number first and only make it smaller if it is clearly taking too long (more than one hour) to correct a
deviation.

For more information on Integral control, see the Notes on Climate Tuning section of this document.

## Heating Integral Timer

This second timer counts up to the Heating Integral Time and resets. Each time that it reaches the
Heating Integral Time, the Heating Current Integral value is incremented or decremented depending
on the state of the Heating Target minus Integrated Temperature Reading. If the Heating Target
minus Integrated Temperature Reading is a positive value, the Heating Current Integral will be
incremented. If it is a negative value or Zero, it will be decremented until it reaches zero.

# P R O G R A M

-   22 -

## Heating Current Integral

The Heating Current Integral value is a time weighted summation of the differences between the
Integrated Temperature Reading and the Heating Target. It is calculated from the Heating Integral
Time Setting. This value increases whenever the reading value is below the target, and decreases
when the reading value is above the target.

## Heating Outdoor Temperature Effect Amount

This modifier value is the final result of the Heating Outdoor Temperature Effect section. It is added to
the sum of the Heating Current Proportional and Heating Current Integral values. The Outdoor
Effect modifier is used to request additional heating resources as the difference between the controlled
climate temperature and the outdoor temperature increases.

## Heating Light Effect Amount

This modifier value is the final result of the Heating Light Effect section. It is added to the sum of the
Heating Current Proportional and Heating Current Integral and Heating Outdoor Light Effect
values. The Heating Light Amount modifier is used to reduce the Outdoor Effect Modifier to
compensate for the solar gain effects associated with increasing light levels on buildings such as
greenhouses.

## Total Heating required for Temperature Control

This is the final result of the Heating Tuning calculations. It is calculated as follows:

Example:

```
Heating Current Proportional 11.25 %
+ Heating Current Integral + 14.00 %
+ Heating Outdoor Effect + 6.12 %
```

-   Heating Light Effect - 5.57%
    = Total Heating Required for Temperature Control = 25.80 %

# P R O G R A M

-   23 -

### Heating Required for Humidity Control

This portion of the Energy Balance program calculates the current amount of heating that is required for
dehumidification. It is particularly suited to applications that use ventilation (the exchange of inside air
with outside air) in conjunction with heating to manage humidity levels. For more information on
humidity management, see the Air/Water Relationships section of this document.

In the example above, the Current Humidity is 12% above the Dehumidify Target threshold. The
Proportional and Integral calculations have determined that the heating resources should be applied at
a total of 36.87 % (P +I). However, since the Current Maximum Heating for Dehumidification Limit is set
to 10%, only 10% of the total heating resources will be applied for humidity control.

#### How it Works

Dehumidification strategies often use air exchange to exhaust warm moist air and introduce cooler air,
which, when reheated, will be drier than the air it has replaced. Using a combination of heating and
venting in response to rising humidity levels can produce corrective humidity control while maintaining
stable climate temperature setpoints. To accomplish this, it is often useful to begin heating in
conjunction with air exchange before the measured temperatures drop below the heating setpoint. This
advance reheating can help provide stable climate temperatures while the ventilation system is
operating for dehumidification purposes.

This section of the program calculates the current dehumidification heating response using the
following steps:

1. The current Integrated Humidity Reading (the measured humidity) for the climate is
   compared to the Heating Dehumidify Target and an optional Dehumidify Heat Offset
   setting to determine the Dehumidify Heating 'P' Difference.
2. This difference is then applied to the Dehumidify Heat Proportional Span setting to calculate
   the Dehumidify Heat Current Proportional, which is expressed as a percentage of heating
   resources required.

# P R O G R A M

-   24 -

3. If Integral control is used, the Dehumidify Heat Integral Timer will increment whenever
   there is a positive deviation from the dehumidify target (it will decrement when there is a
   negative deviation from the target or when the deviation is zero.) The rate of Integral
   accumulation is determined by the Dehumidify Heat Integral Time setting. The result is the
   Dehumidify Heat Current Integral which is expressed as a percentage of heating resources
   required.
4. The sum of the Proportional and Integral calculations is expressed as the Total Heating
   Required for Dehumidification.

```
Note: The total heating required cannot exceed the Maximum Heating for Dehumidification
Limit. This value is usually defined in the Diurnal Setpoints program and is used to set an upper
limit for the amount heating resources that will be applied for controlling. It is generally used to
help reduce heating costs.
```

The Dehumidify Heat Offset value can be used to prevent the program from generating an output
response until the deviation has exceeded a certain threshold. It can be used to prevent 'nuisance'
control actions for minor deviations. In addition, it can be used to provide staging between heating for
dehumidification and ventilation for dehumidification strategies, so that you can have one follow the
other, based on the amount of deviation from the Target.

The offset value is used to define a threshold at which the control program will begin to calculate the
Dehumidify Heat Current Proportional value required for dehumidification. For example, when set to
zero, the program will start calculating a value whenever the Integrated Humidity Reading minus the
Heating Dehumidify Target is greater than zero. If an offset value of 6 % Rh is used, the program will
not start calculating a desired output until there is at least a 6 % Rh deviation between the Reading and
the Target.

In the following example, the operator has set a Dehumidify Heat Offset of 6.0 % Rh. Currently the
Integrated Humidity Reading is 10% higher than the Heating Dehumidify Target (Reading - Target).
However, since there is a 6 % Rh Dehumidify Heat Offset in effect, the net deviation (the Dehumidify
Heating 'P' Difference) is only 4 %.

```
Integrated Humidity Reading 80.0 % Rh
```

```
Heating Dehumidify Target - 70.0% Rh
```

```
Reading - Target = 10.0 % Rh
```

```
Dehumidify Heat Offset - 6.0% Rh
```

```
Dehumidify Heating 'P' Difference = 4.0 %
```

```
Dehumidify Heat Proportional Span / 40.0%
```

```
Dehumidify Heat Current Proportional = 10.0 %
```

The Dehumidify Heat Proportional Span setting is used to scale or proportion a control response
over a specified range, based on the amount of deviation of the measured control condition from the
target. The Dehumidify Proportional is based on the difference between the Climate Humidity and
the Dehumidify Heating Target. This difference (positive or negative) is used with the Proportional
setting to calculate the current value.

# P R O G R A M

-   25 -

The setting is entered as the number of percent (%Rh) above the Dehumidify Heating Target at
which the current Proportional value will be +100%.

In the above example, a Proportional Span setting of 40% Rh means that the proportional output
response (the Dehumidify Current Proportional) will be proportioned over a span of 40% Rh. This
means that the program will start applying a small amount of output as soon as the Dehumidify
Heating 'P' Difference is greater than zero and it gradually increase the its output over a span of 40%
Rh.

Since the current Dehumidify Heating 'P' Difference in the above example is only 4% a Dehumidify
Heating Current Proportional value of 10% will be generated (4% divided by 40%). When the
Dehumidify Heating 'P' Difference reaches 20%, the Dehumidify Heating Current Proportional
value will be 50% and so on. Typically, values of 30% Rh to 40% Rh will yield good results when used
in conjunction with Integral humidity control.

Notice that there is a 6% Dehumidify Heat 'P' Offset. This value can be used to prevent the program
from generating an output response until the deviation has exceeded a certain threshold (in this case,
until the reading has deviated from the target by 6% or more). It will effectively prevent 'nuisance'
control actions for minor deviations.

## Dehumidify Heat Integral Time

Integral settings are used in a variety of control functions to deliver an output response based upon the
length of time a current value has deviated from its target value. The Argus system calculates an
integral value based upon the current deviation from the dehumidify target and the length of time the
deviation has been in effect. For more information on Integral control see the Integral Response in
Controlled Systems topic in this document.

## Set to Zero to Disable Entirely

An integral setting value can be entered between 0 and 255 seconds. A low integral setting value will
result in a more aggressive integral response (the calculated integral response will change rapidly). A
high integral setting value will result in a less aggressive integral response (the calculated integral
response will change slowly).

Overly aggressive settings (where integral setting value is too low) can cause excessive equipment
cycling and poor control. Settings that are too passive (where the integral setting value is to large) will
also produce poor control due to lack of equipment responsiveness.

Setting values of 30 to 100 are typical. Try a larger number first and only make it smaller if it is clearly
taking too long to correct a deviation.

```
Integrated Humidity Reading 80.0 % Rh
```

```
Heating Dehumidify Target - 70.0% Rh
```

```
Reading - Target = 10.0 % Rh
```

```
Dehumidify Heat Offset - 6.0% Rh
```

```
Dehumidify Heating 'P' Difference = 4.0 %
```

```
Dehumidify Heat Proportional Span / 40.0%
```

```
Dehumidify Heat Current Proportional = 10.0 %
```

# P R O G R A M

-   26 -

## Dehumidify Heat Integral Timer

This second timer counts up to the Dehumidify Heat Integral Time value and resets. Each time that it
reaches the Dehumidify Heating Integral Time, the Dehumidify Heat Current Integral value is
incremented or decremented depending on the state of the Integrated Humidity Reading minus the
Heating Dehumidify Target plus the Dehumidify Heat Offset. The result is called the Dehumidify
Heating 'P' Difference.

If the Dehumidify Heating 'P' Difference is a positive value, the Heating Current Integral will be
incremented. If it is a negative value or Zero, it will be decremented until it reaches zero.

## Dehumidify Heat Current Integral

This is the calculated value for the integral control component expressed as a parentage of the
ventilation resources required.

## Total Heating Required for Dehumidification

This is the calculated total heating value that is currently required for dehumidification.

## Maximum Heating for Dehumidification Limit

This reading displays the Maximum Heating for Dehumidification Limit from the Maximum Heating for
Dehumidification Limit source assigned to this program. The Total Heating Required for
Dehumidification value will not exceed this limit amount.

# P R O G R A M

-   27 -

### Heating Outdoor Temperature Effect

This section of the Climate Energy Balance program is used to define how the current outdoor
temperature should affect the Heating Required for Temperature Control calculations. In general, highly
insulated buildings have a lower outdoor effect than poorly insulated building such as greenhouses.

#### How it works

The current difference between the Heating Target and the Outdoor Temperature Reading is
calculated as the Heating Target minus Outdoor Temperature reading.

There are two sets of parameters for calculating the current outdoor heating effect. One is used if a
shade or thermal curtain system is installed and is fully Extended (Closed), and another is used when
no curtain system is installed, or when the curtain system is currently retracted or partially Retracted
(Open). Since shade and thermal curtains can affect the current rate of heat loss, this provides for
calculating different outdoor temperature effects based on the position of the curtain system. Normally,
when the shade is fully Extended (closed), less heat is lost and less heating is required. Consequently,
the outdoor effect will also be less. When the shade is not used or only partially used, more heating is
generally required and the outdoor effect will be greater.

The Current Shade Position parameter is used to determine which set of heat effect proportioning
calculations will be used. When the current shade position is greater than 1% or Failed (Not Used), the
SHADE RETRACTED (Open) position settings are used. When the current shade position = 0% (Fully
Extended) the shade EXTENDED (Closed) settings are used.

# P R O G R A M

-   28 -

For each of the Shade/Thermal Curtain settings, the difference between the Current Outdoor
Temperature and the Target Temperature is scaled over an operator defined range using the
Outside Temp Effect Input Start/End settings and the Outside Temp Effect Output Start/End
Settings.

In the above illustration, as the difference between the outdoor temperature and the indoor temperature
changes between 5 °C and 60 °C, the Heating Outdoor effect modifier will be scaled between 0.00 %
and 30 %.

Generally, as the difference between the indoor and outdoor temperature increases, the Heating
Outdoor Effect should be configured to increase the amount of heating resources required.

### Heating Light Effect

The Heating Light Effect modifier is used to ‘throttle-back’ the heating response in relation to the
amount of available sunlight. It works in opposition to the Heating Outdoor Temperature Effect which
is used to increase the heating response in relation to how cold it is outside. This parameter is only
useful for buildings with significant solar heating efficiency such as greenhouses. Generally, as light
radiation levels increase, the greater the natural heating effect.

This modifier is only used to offset the Heating Outdoor Temperature Effect at times when high light
levels will offset some or all of the additional energy required for heating because of cold outdoor
temperatures.

# P R O G R A M

-   29 -

#### How it Works

1. A Predicted Light Value for Heating is calculated from the current Light Reading and the
   Integrated Light Reading.
2. If a Light Prediction Modifier is used, the Heating Predicted Light value is adjusted
   accordingly.
3. The current predicted light is then scaled over the Light Effect Scaling values to determine
   the Heating Light Effect amount. The result is a negative percentage value that is used to
   offset the Heating Outdoor Temperature Effect.

```
Notes: The Heating Light effect is only used to offset the Heating Outdoor Temperature
Effect during periods of cold weather and high light. If the calculated Heating Light Effect is
greater than the current Outdoor Effect, it will be limited to the Outdoor Effect's current value
(canceling each other out). In the above example, the Calculated Temperature Heating Light
Effect has been reduced to -3.45%.
```

Many heating systems have a considerable process lag between the time heat is requested and the
time it is delivered and measured.

The light prediction section monitors trends in the light levels and allows selective amplification of these
trends which are then used in the Heating Light Effect portion of the heating calculations. It attempts to
predict what the light levels may be by the time the heat is delivered.

The light prediction is determined by the difference between the Light Reading and the Integrated Light
Reading (integrated over the past 15 minutes).

The difference is then multiplied by the Light Predictor setting and then added or subtracted from the
Outdoor Light Energy level, depending on whether the light levels are decreasing or increasing.

Example: If the Current Light Reading energy level is 100 Wm² and the Integrated Light Reading
level over the past 15 minutes is 90 Wm² (the light has been increasing), a Light Prediction Modifier
setting of 1 would give a predicted light level of 110 Wm² which is then used in the Light Effect
calculation.

If the setting is 2 the predicted light level would be 120 Wm², and so on.

# P R O G R A M

-   30 -

Similarly, If the Current Light Energy is 180 Wm² and the Integrated Light Reading is 200 Wm²
(decreasing light), a Light Predictor setting of 3 would give a predicted light level of 120 Wm² and a
setting of 1 would give 160 Wm².

A light prediction of one or two looks forward approximately 15 to 30 minutes and is a good match for
most heating system process lags. For very responsive heating systems (such as Unit Heaters) this
feature can be disabled by setting it to zero.

### Heating System Request Temperature

This section of the Energy Balance program is used to generate a proportioned temperature request for
use by heating system programs. The Broadcast information program collates heating system request
values and Heating system numbers for use by other programs.

#### How it Works

The program considers the current Heating Required for Dehumidification and Heating Required
for Temperature Control values and uses either the highest of the two, or the sum of the two requests
depending on the Heating System Request Type setting.

A Current Heating System Request Temperature is then calculated by proportioning the above
current heating required percentage over the Minimum Heating System Temperature Request and
Maximum Heating System Temperature Request span setting values.

In the above example the Minimum and Maximum System Request scaling values have been set to
produce a water temperature request that is scaled between 20.00 °C and 100 °C. Therefore, 20.00 °C

-   (.7661% x (100 – 20)) = 81.28 °C.

The Heating System number (1-16) is used to will identify this request. The Broadcast information
program collates heating system request values and Heating System numbers for use by other
programs.

```
.
```

# P R O G R A M

-   31 -

### Ventilation Tuning

This section includes all the parameters used to calculate the current ventilation demand for
temperature and humidity control. It consists of the following subsections:

-   Ventilation Required for Temperature Control
-   Ventilation Required for Dehumidification
-   Ventilation Light Effect
-   Ventilation Outdoor Temperature Effect

#### How it Works

Without climate tuning and proportioned control, as soon as the temperature in the climate rises above
the setpoint, the full resources of the ventilation system would be requested (100% ventilation). This
would cause the ventilation equipment to supply all the air exchange it can, as soon as it can.

This might take a little while to have an effect, depending on capabilities and the process lag of
equipment used, during which time, the climate temperature might continue to rise. Eventually, as the
air is exchanged, the heat energy is removed from the climate, and the temperature sensors begin to
register a drop in temperature. By the time the measured climate temperature drops below the setpoint,
the ventilation system may have removed more heat than is necessary due to the process lag. This can
result in what is called “overshoot”.

The above scenario would also result in “undershoot” problems as well, since it would tend to operate
the ventilation system in an “all-or-nothing” fashion. The example below illustrates the classic
temperature oscillation behavior typical with this type of unturned feedback control:

# P R O G R A M

-   32 -

The Ventilation Tuning section attempts to correct both overshoot and undershoot problems by
producing proportioned ventilation request values that will result in more ‘steady state’ conditions,
where the correct amount of air is exchanged for the current conditions, with compensation for process
lags.

Instead of requesting 100% ventilation as soon as the climate temperature rises above the setpoint, the
program begins asking for ventilation in proportion to the amount of deviation, with additional
consideration for how long the deviation has occurred. Therefore, while a temperature deviation is
relatively minor and recent, the system will ask for a correspondingly small amount of ventilation. If the
deviation persists for a longer time, or increases, the program asks for more ventilation.

Similarly, as the measured temperature begins to fall back towards the setpoint, the program begins to
taper off the ventilation demand. The illustration below shows a properly tuned Ventilation response:

# P R O G R A M

-   33 -

Since ventilation equipment can also be used for dehumidification purposes, separate calculations are
performed for cooling and dehumidification in the Ventilation Tuning section.

The primary method for tuning this type of measured response is to use the Ventilation Proportional
Span and Ventilation Integral Timer settings in the Ventilation Required for Temperature Control
section.

The Ventilation tuning section also contains built-in Ventilation Modifiers to compensate for the effects
of outdoor temperature and outdoor light levels.

If you require additional or different Ventilation modifiers, these can be configured in other control
programs by using the Ventilation Required for Temperature Control result from this program as an
input in a control program that is configured to modify this value based on other measured conditions).

### Ventilation Required for Temperature Control

This section considers the current Zone Temperature conditions in relation to the Cooling Target
Temperature and develops a Total Ventilation Required for Temperature Control result from 0-
100%. At zero percent, no ventilation is required for temperature management. At 100%, full ventilation
resources are required to meet the current demand.

#### How it Works

At any time, an enclosed climate may be either:

1. Losing heat energy to the surrounding environment
2. Gaining heat energy from the surrounding environment
3. In balance with the surrounding environment (neither gaining nor losing energy).

# P R O G R A M

-   34 -

```
At times when the controlled climate is gaining heat energy, passive or forced ventilation can be
used to maintain temperature (when outside temperatures are lower then indoor temperatures).
Since the rate of heat loss or gain is constantly changing due to fluctuating external factors such as
wind, solar radiation, rain, snow, and relative humidity levels, the ventilation system must be able to
deliver an amount of cooler exchange air that correctly compensates for the current rate of
temperature gain. This is normally accomplished by either varying the On to Off ratio of digital
devices such as wall fans, or by controlling the amount of ventilation delivered by proportional
systems such as passive roof vents or forced air exchange units.
Whenever the ventilation system is providing the correct amount of air exchange needed to
maintain the target temperature setpoint, the amount of heat removal that is being applied will
match the amount of heat that is being gained. To accomplish this, it is necessary for the control
system to measure the current conditions and compare them to the current target temperature
values for the climate. Since most ventilation systems cannot deliver heat removal instantly in
response to a control system request, there is usually a Process Lag.
This portion of the Energy Balance program calculates the current amount of ventilation that is
required for cooling. It is particularly suited to applications that use ventilation (the exchange of
inside air with outside air) for cooling. Although it is primarily suited to ventilation cooling, it may be
possible to use this for other types of cooling including closed or partially closed loop mechanical
cooling equipment.
```

The program uses the following steps to determine the current ventilation required:

1. The program calculates the difference between the Integrated Temperature Reading and the
   Cooling Target.
2. If the above difference is greater than the Cooling Deadband value, then the Difference
   between Reading and Target value will be greater than zero.
3. The Difference between Reading and Target value is then proportioned over the operator
   defined Cooling Proportional Span setting and a desired Current Proportional value is
   calculated. It is expressed as percent of ventilation required.
4. If Integral control is used, the Cooling Integral Timer will also accumulate whenever there is
   a deviation greater than the deadband. The rate of Integral accumulation is determined by the
   Cooling Integral Time setting.
5. The Current Proportional and the Cooling Current Integral values are summed in the
   Proportional plus Integral reading.
6. If a Ventilation Light Effect is used, then the current Ventilation Light Effect Multiplier will
   be applied. The result is displayed as the Light Adjusted Cooling Total.
7. If a Ventilation Outdoor Temperature Effect is used, then the Ventilation Outdoor
   Temperature Effect Multiplier will be applied. The result is displayed as the Outdoor
   Temperature Adjusted Ventilation Total.

The final result of the Ventilation Required for Temperature Control section is the Total Ventilation
required for Temperature Control reading. This value can then used by other control programs to
position the ventilation equipment accordingly.

#### Integrated Temperature Reading

This reading displays the current integrated zone temperature. If a Backup Zone Temperature Reading
sensor is assigned this reading will display the backup value if the primary Zone Temperature Reading
sensor fails.

# P R O G R A M

-   35 -

#### Cooling Target

This reading displays the current cooling target value from the cooling target source assigned to this
program.

#### Cooling Band

The Cooling Band is used to reduce cycling of cooling equipment caused by small changes in the
Climate Temperature.

Increases in the amount of Cooling Required will not occur unless the Climate Temperature is greater
than the Cooling Band setting. Similarly, when the Climate Temperature drops below the Cooling
Control Target the 'P' difference is calculated from the Cooling Band + the Cooling Control target value.
This will help to promote quicker action for clearing accumulated Integral values when the target is
higher than the reading value.

Here's how the cooling band is used to calculate the "P" difference:

While the Integrated Temperature Reading is greater than the Cooling Target plus the Cooling
Band :

Cooling 'P' Difference = Integrated Temperature Reading - Cooling Target

While the Integrated Temperature Reading is less than the ( Cooling Target plus the Cooling
Band) and the Integrated Temperature Reading is Greater than the Cooling Target:

Cooling 'P' Difference = Zero

While the Integrated Temperature Reading is less than Cooling Target:

Cooling 'P' Difference for Integral Calculations = ( Cooling Target + Cooling Band) - Integrated
Temperature Reading

Cooling 'P' Difference for Proportional Calculations = Cooling Target - Integrated Temperature
Reading

Settings of 0.9°F (0.5°C) to 1.8°F (1.0 °C) provide a good compromise between good temperature
control and excessive equipment cycling (set to zero to disable entirely).

#### Cooling 'P' Difference

This calculated value is used in the proportional and integral control settings. Here's how the Cooling
"P" difference is calculated:

While the Integrated Temperature Reading is greater than the Cooling Target plus the Cooling
Band : Cooling 'P' Difference = Integrated Temperature Reading - Cooling Target

While the Integrated Temperature Reading is less than the ( Cooling Target plus the Cooling
Band) and the Integrated Temperature Reading is Greater than the Cooling Target: Cooling 'P'
Difference = Zero

While the Integrated Temperature Reading is less than Cooling Target:

Cooling 'P' Difference for Integral Calculations = ( Cooling Target + Cooling Band) - Integrated
Temperature Reading

Cooling 'P' Difference for Proportional Calculations = Cooling Target - Integrated Temperature
Reading

# P R O G R A M

-   36 -

#### Cooling Proportional Span

Enter the setting as the number of degrees above the Cooling Control Target (plus the Cooling
Band) at which the Proportional value will be +100%.

Proportional settings are used to scale or proportion a control response over a specified range.
Proportional cooling is based on the difference between the Climate Temperature and the Cooling
Target. The difference (positive or negative) is used with this setting to calculate the current value.

For example: A Proportional setting of 18.0°F (10.0°C) and a Climate Temperature of 4.5°F (2.5°C)
above the Cooling Control Target (plus the Cooling Band) will result in a Proportional value of +25%. If
the deviation from the target reaches 18.0°F or greater, the proportional value will be 100%.

A suggested setting is approximately 12.6 to 18.0°F (7.0 to 10.0°C) when Integral cooling is also used.
Lower values will tend to result in 'overshoot', when the controller requests more cooling than is
required. Higher values tend to result in an unresponsive system. These reactions will vary from
compartment to compartment. Use a Cooling Proportional Span setting of 5.5 to 9.0°F (3.0 to 5.0°C)
when Integral control is not used (the Integral setting is zero).

#### Current Proportional

This reading displays the current cooling Proportional value for this climate. It indicates how much
cooling response is currently required due to deviation of the Integrated Temperature Reading from
the Cooling Target. It is calculated from the Cooling Proportional Span setting.

#### Cooling Integral Time

An integral setting value can be entered between 0 and 255 seconds. A low integral setting value will
result in a more aggressive integral response (the calculated integral response will change rapidly). A
high integral setting value will result in a less aggressive integral response (the calculated integral
response will change slowly).

Integral settings are used in a variety of control functions to deliver an output response based upon the
length of time a current value has deviated from its target value. The Argus system calculates an
integral value based upon the current deviation from the cooling target and the length of time the
deviation has been in effect.

The Factory Default setting is 50.

Set to zero to disable entirely.

Setting values of 30 to 100 are typical. Try a larger number first and only make it smaller if it is clearly
taking too long (more than one hour) to correct a deviation.

Overly aggressive settings (where integral setting value is too low) can cause excessive equipment
cycling and poor control. Settings that are too passive (where the integral setting value is to large) will
also produce poor control due to lack of equipment responsiveness.

#### Cooling Integral Timer

The current Cooling Integral Timer value is the time-weighted summation of the differences between
the Climate Temperature and the Cooling Target after allowing for the Cooling Deadband. This value is
used to calculate the Current Integral.

# P R O G R A M

-   37 -

#### Cooling Current Integral

This reading displays the current cooling Integral value for this climate. It indicates how much cooling
response is currently required due to the length of time of a deviation of the Integrated Temperature
Reading from the Cooling Target.

#### Proportional plus Integral

This reading displays the sum of the proportional and integral calculations.

#### Ventilation Light Effect Multiplier...................................................................................................

This multiplier is the result of the Ventilation Light Effect calculations. It is used to increase or decrease
the amount of ventilation control applied due to the heating effects of current light levels.

#### Light Adjusted Cooling Total

This reading displays the current cooling required after the Ventilation Light Effect has been calculated.

#### Ventilation Outdoor Temperature Effect Multiplier

This reading is the result of the Ventilation Outdoor Temperature Effect settings. It is used to increase
or decrease the amount of ventilation control applied due to the effects of current outdoor temperatures.

## Outdoor Temperature Adjusted Ventilation Total

This reading displays the current ventilation required after adjustment for the Ventilation Outdoor
Temperature Effect.

# P R O G R A M

-   38 -

### Ventilation Required for Dehumidification

This portion of the Energy Balance program calculates the current amount of ventilation that is required
for dehumidification. It is particularly suited to applications that use ventilation (the exchange of inside
air with outside air) in conjunction with heating to manage humidity levels. For more information on
humidity management, see the Air/Water Relationships section on page 5 of this document.

#### Maximum Ventilation for Dehumidification Limit

This reading displays the Maximum Ventilation for Dehumidification Limit from the Maximum Ventilation
for Dehumidification Limit source assigned to this program.

Note: The Total Ventilation required for Dehumidification will not exceed this value.

#### Integrated Humidity Reading

This reading displays the current Integrated Humidity Reading value calculated from the Humidity
Reading source assigned to this program.

#### Ventilation Dehumidify Target

This reading displays the Ventilation Dehumidify Target from the Ventilation Dehumidify Target source
assigned to this program.

#### Dehumidify Ventilation Difference between Reading and Target...............................................

This reading is the current climate humidity reading minus the current dehumidify ventilation target.

# P R O G R A M

-   39 -

#### Dehumidify Ventilation Proportional Span

Enter the setting as the number of percent above the Ventilation Dehumidify Target at which the
Dehumidify Ventilation Current Proportional will be +100%.

Proportional settings are used to scale or proportion a control response over a specified range based
on how far away the measured control condition is from the target. The Dehumidify Ventilation
Proportional Span setting is based on the difference between the current b for the climate and the
Ventilation Dehumidify Target. The difference (positive or negative) is used with this Proportional
Span setting to calculate the current value.

Example: If you enter a Proportional Span setting of 3 0.0% Rh and the Integrated Humidity Reading
rises to 3 .0% Rh above the Ventilation Dehumidify Target, a Dehumidify Ventilation Current
Proportional value of 10% will be produced. Similarly, if the humidity reaches 15 % above the Target,
the proportional value will be 50% and so on. Typically, values of 30.0% Rh to 40.0% Rh will yield
good results when used in conjunction with Integral humidity control.

#### Dehumidify Ventilation Current Proportional

This reading represents the current amount of ventilation required, expressed as a percentage that is
required due to deviation of the climate humidity reading from the target value. It is calculated from the
Dehumidify Ventilation Proportional Span setting.

#### Dehumidify Ventilation Integral Time

Integral settings are used in a variety of control functions to deliver an output response based upon the
length of time a current value has deviated from its target value. The Argus system calculates an
integral value based upon the current deviation from the dehumidify ventilation target and the length of
time the deviation has been in effect.

An integral setting value can be entered between 0 and 255 seconds.

The Factory Default setting is 30.

Set to zero to disable entirely.

A low integral setting value will result in a more aggressive integral response (the calculated integral
response will change rapidly). A high integral setting value will result in a less aggressive integral
response (the calculated integral response will change slowly).

Overly aggressive settings (where integral setting value is too low) can cause excessive equipment
cycling and poor control. Settings that are too passive (where the integral setting value is to large) will
also produce poor control due to lack of equipment responsiveness.

Setting values of 30 to 100 are typical. Try a larger number first and only make it smaller if it is clearly
taking too long to correct a deviation.

#### Dehumidify Ventilation Integral Timer

This value displays current Integral time accumulation. It is derived from the Dehumidify Ventilation
Integral Time settings and used to calculate the Current Integral.

# P R O G R A M

-   40 -

#### Dehumidify Ventilation Current Integral

This is the calculated value for the integral control component expressed as a parentage of the
ventilation resources required.

#### Dehumidify Ventilation Proportional plus Integral

This is the total of the Dehumidify Ventilation Current Proportional and Dehumidify Ventilation
Current Integral control calculations.

#### Ventilation Outdoor Temperature Effect Multiplier

This reading is the result of the Ventilation Outdoor Temperature Effect settings. It is used to
increase or decrease the amount of ventilation control applied due to the effects of current outdoor
temperatures.

#### Dehumidify Ventilation Outdoor Temperature Adjusted Total

This is the desired ventilation value after adjustment by the Ventilation Outdoor Temperature Effect
Multiplier.

#### Dehumidify Ventilation Heat Reduction Setting

This setting is used to select a method of ventilation reduction when the temperature in the
compartment drops below the Heating Control Target. When the [Less 'P' Heat Component] selection
is made, the dehumidify ventilation value will be reduced by subtracting the current Heating Current
Proportional value required to maintain the compartment temperature.

When [Heat Reduction Disabled] mode is selected, heat reduction will not be used unless the Climate
Temperature drops by more than 4.5°F (2.5°C) below the Heating Target. When this occurs, the
Dehumidification Ventilation amount will be zeroed. Under these conditions, allowing the
compartment temperature to return to normal will do more for dehumidification than continued
excessive ventilation.

#### Heating Current Proportional

This reading displays the current Heating Proportional value for this climate. It indicates how much
heating response is currently required due to deviation of the Integrated Temperature Reading from
the Heating Target. It is calculated from the Heating Proportional Span setting.

#### Total Ventilation required for Dehumidification

This is the final result of the ventilation control calculations for dehumidification.

# P R O G R A M

-   41 -

### Ventilation Light Effect

This section is used to modify the ventilation rate based upon outdoor light levels. Typically, as light
levels increase so does the required ventilation rate, since the light energy is converted to heat energy
when it passes through glass, plastic, or other transparent or semi-transparent materials.

#### How it Works

The Current Light Reading displays the current value for the outdoor light level.

The Ventilation Light Effect Scaling settings are used to adjust the ventilation rate as light levels
change.

The Ventilation Light Effect Multiplier is the result of the scaled light adjustment. It is then applied to
the current Ventilation Required for Temperature Control calculations.

### Ventilation Outdoor Temperature Effect

This section of the Climate Energy Balance program is used to automatically modify the ventilation rate
based on the temperature of the incoming ventilation air. This effect is expressed as a multiplier value
that is then applied in the Ventilation Required for Temperature Control calculations (see page 33 ).
As the difference between the cooling target and the outdoor air increases, less air exchange required
to achieve cooling. This section provides a means of scaling the relationship between outdoor air
temperature and ventilation rate.

# P R O G R A M

-   42 -

#### How it Works

The current difference between the Cooling Target and the Outdoor Temperature Reading is
calculated and displayed as the Cooling Target minus Outdoor Temperature reading.

The current Ventilation Outdoor Temperature Effect Multiplier is then calculated by scaling the
Cooling Target minus Outdoor Temperature by the Ventilation Outdoor Temperature Effect
Scaling values.

In the above example there is currently a difference of 10 ºC between the Cooling Target and the
outdoor temperature. As the difference increases, less air exchange is needed to achieve the same
amount of cooling. After the scaling values are applied, a Ventilation Outdoor Temperature Effect of
0.60 will then be applied in the Ventilation Required for Temperature Control calculations (see page
33 ).

## INSTALLATION AND SERVICE SCREENS

The Installation and Service section contains readings and settings not normally required by users.
They are used for diagnosing problems and other special situations. On occasion, you may be asked
by Argus technical support personnel to review or modify these settings.

```
Warning: Always check with Argus before you modify any of the values on Installation and
Service Screens.
```

# P R O G R A M

# P R O G R A M

##### ARGUS CONTROL SYSTEMS LTD.

Telephone: (************* or (604) 536- 9100
Toll Free Sales: (************* (North America)
Toll Free Service: (************* (North America)
Fax: (604) 538- 4728
E-mail: <EMAIL>
Web Site: [http://www.arguscontrols.com](http://www.arguscontrols.com)

Information in this manual is subject to change without notice.
 Copyright 2009 Argus Control Systems Ltd. Printed in Canada

Argus is a registered trademark of Argus Control Systems Ltd.

# Technical Architecture & Configuration Guide: Integrated Climate Control System

###

### **1. Introduction: System Design Philosophy**

**1.1. Target Audience and Document Purpose**
This document provides a comprehensive technical overview of the Integrated Climate Control System's architecture, logic flow, and configuration parameters, based on the official Climate Energy Balance Program operator manual and detailed system screens. It is intended for system designers, developers, and advanced technical operators who require a deep understanding of the system's internal workings for the purposes of maintenance, troubleshooting, performance tuning, and future development. This guide will move beyond user-level descriptions to explain the underlying engineering principles and data processing pipelines.

**1.2. Core Philosophy: Plant Empowerment as an Architectural Driver**
The system is designed around the core philosophy of **Plant Empowerment**. From an architectural standpoint, this means the system is not a monolithic controller but a modular, multi-threaded engine that prioritizes the plant's biological needs over simple environmental metrics. The central design principle is to calculate environmental demands (e.g., heating, cooling) based on multiple, independent drivers (e.g., temperature vs. humidity) and then use a clear, hierarchical prioritization logic to issue a single, unambiguous command to the physical equipment. This modularity is paramount; it prevents control loop conflicts that plague simpler systems and allows for sophisticated, energy-efficient strategies by incorporating predictive, feed-forward influences based on external environmental factors.

**1.3. Rate Management vs. State Management**
A key architectural distinction of this system is its focus on **rate management**, not just state management. A basic thermostat manages a state (e.g., keeping the temperature at 21°C). This advanced system manages the _rate of change_. Through its PID (Proportional-Integral-Derivative) loops and ramp functions, it controls how _fast_ the environment is allowed to change. This prevents shocking the crop, reduces equipment wear, and provides a much more stable and energy-efficient climate.

**1.4. Economic Implications of Configuration**
It is critical for a developer or designer to understand that the configuration parameters discussed herein have direct and significant financial consequences. A poorly tuned PID loop can cause heating and ventilation systems to oscillate, fighting each other and wasting thousands of dollars in energy costs over a season. Conversely, a well-tuned system that leverages feed-forward influences to minimize waste can be a significant source of profit for the end-user. Every parameter adjustment should be considered in terms of its impact on both plant health and operational expenditure.

### **2. System Architecture & Logic Flow**

The system operates on a continuous, cyclical logic flow, typically executing a full calculation cycle every few seconds. This process determines the state of its primary outputs: the Heating System and the Ventilation System. This flow can be understood as a four-stage pipeline for each output.

**2.1. Stage 1: Setpoint Ingestion (The Goal)**
The system begins by fetching the primary control targets from the **Diurnal Setpoint Module**. These targets (`Heating Target`, `Cooling Target`, `Dehumidify Target`, etc.) are the desired state for the current time period. This is not just a static lookup; if a "Ramp Rate" is active between two diurnal periods, the system will interpolate the target value for the current moment in time, ensuring a smooth transition rather than an abrupt jump in the goal state.

**2.2. Stage 2: Independent Demand Calculation (The "Why")**
The system calculates the demand for each output based on different potential needs. It runs two parallel subroutines, each with its own dedicated PID control loop:

-   **Subroutine A: Temperature Demand:** A PID control loop calculates the required heating/ventilation purely to meet the `Heating Target` or `Cooling Target`.
-   **Subroutine B: Dehumidification Demand:** A second, independent PID loop calculates the required heating/ventilation purely to meet the `Dehumidify Target`.
    This results in four initial demand values: `HeatReqForTemperatureControl`, `VentReqForTemperatureControl`, `HeatReqForHumidityControl`, and `VentReqForHumidityControl`. This parallel processing ensures that a high demand from one driver (e.g., dehumidification) does not blind the system to a separate, simultaneous demand from another driver (e.g., temperature).

**2.3. Stage 3: Feed-Forward Influence Adjustment (The "Prediction")**
The demand values from Stage 2 are adjusted based on predictive, external factors.

-   **Outdoor Temperature Effect:** A calculated value, typically derived from a user-defined response curve, is added to the heating/ventilation demands to proactively counteract the influence of the external air temperature.
-   **Light Effect:** A calculated value (negative for heating, positive for ventilation) is added to the demands to account for the "free" energy being provided by solar radiation.
    This stage produces the final, adjusted demand values for each of the four drivers.

**2.4. Stage 4: Prioritization and Synthesis (The "Decision")**
The system must now issue a single, coherent command to the hardware.

-   **Highest Request Logic:** For each output system (Heating and Ventilation), the controller compares the final demand values from its constituent drivers.
    -   `HighestHeatRequest = MAX(Adjusted_HeatReqForTemperatureControl, Adjusted_HeatReqForHumidityControl)`
    -   `HighestVentRequest = MAX(Adjusted_VentReqForTemperatureControl, Adjusted_VentReqForHumidityControl)`
    -   This `Highest Request` value is the primary command signal, ensuring the most critical need is always met.
-   **Sum of Requests Logic:** In parallel, the system also calculates the sum of all requests for a given output.
    -   `SumOfHeatRequests = (Adjusted_HeatReqForTemperatureControl + Adjusted_HeatReqForHumidityControl)`
    -   `SumOfVentRequests = (Adjusted_VentReqForTemperatureControl + Adjusted_VentReqForHumidityControl)`
    -   These values are not used for primary command and control but are crucial for **load analysis and staging**. A developer could use these values to control staged boiler equipment (e.g., if `SumOfHeatRequests` > 100%, activate Boiler 2) or for creating detailed energy consumption reports that break down how much energy was used for temperature control versus dehumidification.

### **3. Advanced System Interlocks & Data Processing**

The true sophistication of the system lies in how the modules interact and process data.

-   **Integrated Sensor Readings & Data Latency:**
    -   The system does not use raw sensor data (`Humidity Reading`) directly in its PID calculations. It uses an **`Integrated Humidity`** or **`Integrated Temperature`**.
    -   **Purpose for Developers:** This is a low-pass filter, typically implemented as a moving average or an exponential smoothing algorithm. Its purpose is to dampen sensor noise and prevent the control loops from reacting erratically to brief, insignificant fluctuations. The time constant of this integration is a critical tuning parameter; a longer integration period provides more stability but reduces responsiveness. The `Cross Module Request Timer` (e.g., 30 seconds) seen in the screenshots likely defines the polling interval for fetching data from other sensor modules, establishing the base data latency of the system.
-   **The Heat/Vent Dehumidification Interlock (`Less 'P' Heat Component`):**
    -   **Architectural Significance:** This is a critical energy-saving feature that creates a direct, negative interaction between the heating and ventilation dehumidification subroutines. It represents a fundamental design choice to prioritize cooperative control over independent action.
    -   **Mechanism:** When calculating the `Dehumidify Venting Required`, the system takes the standard `P + I` components and then _subtracts_ the Proportional component of the _heating_ dehumidification request (`DEHUMIDIFY HEATING P COMPONENT`).
    -   **Purpose for Developers:** This logic prevents a massively inefficient scenario where the system is simultaneously heating the air to lower RH while also venting that same heated air outside to lower RH. This interlock tells the ventilation system: "If the heating system is already working to dehumidify, reduce your own effort proportionally." A developer extending the system should consider implementing similar interlocks for any new, potentially conflicting control actions.
-   **Cooling/Heating Integral Interlock:**
    -   **Architectural Significance:** Visible in the `Ventilation for Temperature Control Settings` is the note: `'I' is zeroed if Heating Required > 0%`. This is another vital energy-saving interlock.
    -   **Mechanism:** If _any_ module is requesting heat (`Highest Heat Request > 0%`), the integral (`I`) component of the cooling ventilation logic is reset to zero and prevented from accumulating.
    -   **Purpose for Developers:** This prevents a scenario where the system is actively heating, but a brief temperature spike (e.g., from a light turning on) causes the cooling integral to "wind up." Without this interlock, when the heating turns off, the wound-up integral could immediately open the vents, starting a wasteful heating/cooling oscillation.

### **4. Detailed Configuration Guide: Applying the Principles**

This section explains each configurable module on the "Energy Balance Program Summary" screen, providing the technical detail required for system tuning and development.

**The 9 Energy Balance Program Outputs:**

The system produces exactly 9 standardized outputs that serve as the foundation for all climate control decisions:

1. **VentReqForTemperatureControl** - Ventilation Required for Temperature Control (0-100%)
2. **VentReqForHumidityControl** - Ventilation Required for Humidity Control (0-100%)
3. **HighestVentRequest** - Highest Ventilation Request (MAX of outputs 1 & 2)
4. **SumOfVentRequests** - Sum of Ventilation Requests (SUM of outputs 1 & 2)
5. **HeatReqForTemperatureControl** - Heating Required for Temperature Control (0-100%)
6. **HeatReqForHumidityControl** - Heating Required for Humidity Control (0-100%)
7. **HighestHeatRequest** - Highest Heating Request (MAX of outputs 5 & 6)
8. **SumOfHeatRequests** - Sum of Heating Requests (SUM of outputs 5 & 6)
9. **HeatingSystemTempRequest** - Current Temperature Request to Heating System (°C)

**Equipment Control Logic:** The system always uses the highest value (outputs 3 & 7) for primary equipment control, ensuring the most critical need is met. The sum values (outputs 4 & 8) are used for load analysis, staging multiple equipment units, and energy consumption reporting. Output 9 converts the heating percentage to actual equipment temperature commands.

**Part I: Heating Tuning**

This section configures the entire heating request pipeline.

-   **1. Heat Required for Temperature Control Settings:**
    -   **Purpose:** Configures the primary PID feedback loop for temperature.
    -   **Key Parameters & Tuning Strategy:**
        -   `Heating Proportional Span`: Defines aggressiveness. The relationship to standard controller gain is `Gain (K_p) = 100 / Span`. A span of 4.0°C equates to a gain of 25. **Tuning Strategy (Ziegler-Nichols Method):**
            1. Set `Heating Integral Time` to its highest value (disabling the 'I' component).
            2. Start with a wide `Proportional Span`.
            3. Gradually narrow the span until the system temperature begins to oscillate with a stable, constant amplitude. Record this "Ultimate Span" (S_u) and the oscillation period (P_u).
            4. For a stable P or PI controller, set the final span to `~2.0 * S_u` and the `Integral Time` to `~0.8 * P_u`.
        -   `Heating Integral Time`: Defines error correction speed. **Pitfall:** An integral time that is too low (too fast) is the most common cause of control loop oscillation, as the system's "memory" over-corrects for errors too quickly.
-   **2. Heat Required for Dehumidification Settings:**
    -   **Purpose:** Configures the secondary PID loop that uses heat as a tool to lower humidity.
    -   **Key Parameters & Tuning Strategy:**
        -   `Max. Dehumidify Heat`: An essential safety limiter. **Architectural Implication:** This parameter ensures that the secondary goal (dehumidification) can never catastrophically compromise the primary goal (temperature stability). It should be set high enough to be effective on damp days but low enough to prevent significant, unwanted temperature increases (e.g., 15-20% is common).
        -   `Dehumidify Heat Proportional Span`: Defines how aggressively heat is applied as humidity rises.
        -   `Dehumidify Heat Offset`: Creates a dead band. **Strategy:** Set this just above the expected sensor noise level (e.g., 1-2% RH) to prevent the system from reacting to nothing, thereby saving energy and reducing equipment wear.
-   **3. Heating Light Effect Settings:**
    -   **Purpose:** Configures the feed-forward logic that reduces the heating demand based on incoming solar energy.
    -   **Key Parameters & Tuning Strategy:** The scaling relationship (e.g., `From 100 W/m² to 500 W/m²` results in a `0% to 50%` reduction). **Strategy:** Tune this on a clear, cold day. Observe the actual heating demand versus the solar radiation level. If the system is still heating when the sun is providing significant energy, increase the percentage reduction to make the effect more aggressive. Note the critical interlock: the light effect can only _reduce_ the `Outdoor Temperature Effect`, it cannot create a negative heating demand on its own.
-   **4. Heating Outdoor Temperature Effect Settings:**
    -   **Purpose:** Configures the feed-forward logic that proactively increases heat demand based on cold outdoor temperatures, with added complexity for thermal screens.
    -   **Key Parameters & Tuning Strategy:** This module has two independent scaling blocks based on the `Current Shade Position`. One for when the curtain is **RETRACTED** (open) and one for when it is **EXTENDED** (closed). This is because a closed thermal curtain dramatically reduces heat loss.
        -   **Strategy:** The `RETRACTED` settings should be much more aggressive (e.g., provide up to 80% heating effect) than the `EXTENDED` settings (e.g., provide up to 50% effect), reflecting the change in the building's thermal properties.
-   **5. Heating System Request Temperature Settings:**
    -   **Purpose:** Maps the abstract, final `HighestHeatRequest` percentage to the real-world operational range of the heating hardware, producing the `HeatingSystemTempRequest` output.
    -   **Key Parameters:** `Minimum Heating System Temperature Request` (e.g., 30°C min pipe temp) and `Maximum Heating System Temperature Request` (e.g., 80°C max pipe temp).
    -   **Calculation:** `HeatingSystemTempRequest = MinTemp + (HighestHeatRequest/100) * (MaxTemp - MinTemp)`
    -   **Example:** With 50% heating demand and range 30-80°C: `Temperature = 30 + (50/100) * (80-30) = 55°C`
    -   **Architectural Implication:** This module acts as the hardware abstraction layer (HAL) for the heating command. This design means the entire core logic can remain identical even if the physical heating system is changed (e.g., from hot water pipes to forced-air heaters); only the parameters within this final module need to be updated.

**Part II: Ventilation Tuning**

This section configures the parallel pipeline for cooling and dehumidification via ventilation.

-   **6. Ventilation for Temperature Control Settings:**
    -   **Purpose:** Configures the primary PID loop for cooling via ventilation.
    -   **Key Parameters & Tuning Strategy:** `Cooling Proportional Span` and `Cooling Integral Time`. Note the `Cooling Band` parameter, which acts as a dead band to prevent cooling from activating for minor excursions above the cooling target. Also note the critical `'I' is zeroed` interlock.
-   **7. Ventilation Required for Dehumidification Settings:**
    -   **Purpose:** Configures the PID loop that uses ventilation to release excess humidity. Contains the crucial `Less 'P' Heat Component` interlock.
    -   **Key Parameters & Tuning Strategy:** The PID tuning parameters for humidity-based venting. A key strategy is to make this loop significantly more aggressive than the `Heat Required for Dehumidification` loop, as venting is the cheaper, preferred method. Note the temperature interlock: this entire function is disabled if the room temperature is too far below the target, preventing the use of cold outside air for dehumidification.
-   **8. Ventilation Light Effect Settings:**
    -   **Purpose:** Configures the feed-forward logic that proactively increases ventilation based on incoming solar energy.
    -   **Key Parameters & Tuning Strategy:** The `Ventilation Light Effect Multiplier`. **Architectural Implication:** This is a multiplier, not a simple percentage, because its effect should scale with the base demand. For example, a 60% increase on a base demand of 10% is a small adjustment, while a 60% increase on a base demand of 50% is a very large, aggressive adjustment. This multiplicative approach ensures the response is proportional to the existing need.
-   **9. Ventilation Outdoor Temperature Effect Settings:**
    -   **Purpose:** Increases the ventilation aggressiveness on hot days when more air exchange is needed for the same cooling effect.
    -   **Key Parameters & Tuning Strategy:** The `Ventilation Outdoor Effect Multiplier`. **Strategy:** This should be tuned to prevent the vents from opening too wide when the outside air is very hot and offers little cooling potential. The scaling is based on the difference between the indoor `Cooling Target` and the `Outdoor Temperature`, making it an intelligent assessment of the cooling power of the outside air. It can be used to temper the system's response on the hottest days of the year, relying more on other cooling methods like shade screens if available.

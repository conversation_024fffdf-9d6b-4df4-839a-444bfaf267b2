# Diurnal Setpoints Program

## User Guide

```
Rev. November 2017
```

D I U R N A L S E T P O I N T S U S E R G U I D E

© November 2017 Argus Control Systems Limited. All Rights Reserved.

This publication may not be duplicated in whole or in part by any means without the prior written permission of
Argus Control Systems Limited.

Limits of Liability and Disclaimer of Warranty:

The information in this manual is furnished for informational use only and is subject to change without notice.
Although our best efforts were used in preparing this book, Argus Control Systems Limited assumes no
responsibility or liability for any errors or inaccuracies that may appear.

Trademarks:
Argus Controls, Argus Control Systems, Titan, and the Argus logo are trademarks of Argus Control Systems
Limited.

Argus Control Systems Ltd.

Telephone: (************* or (604) 536- 9100
Toll Free Sales: (************* (North America)
Toll Free Service: (************* (North America)
Fax: (604) 538- 4728
E-mail: <EMAIL>
Web: [http://www.arguscontrols.com](http://www.arguscontrols.com)

Written and designed by Argus Control Systems Limited.
Published in Canada.

Revised November 2017

## D I U R N A L S E T P O I N T S U S E R G U I D E

-   ABOUT THIS MANUAL Contents
-   INTRODUCTION
    -   When to use the Program
    -   Alternatives to this Program
    -   How the Program Works
-   PROGRAM SECTIONS
    -   The Main Screen
    -   Big Overview Window
        -   Period Settings & Status
        -   Scheduled Setpoints
        -   Status Information
    -   Period Screens
        -   Time Window Settings
        -   Period Setpoints
        -   External Enable Settings
    -   Setpoint Screens
        -   Setpoint Ramp Rate Alarms
    -   Alarms Window
        -   Period Overlap Alarm
    -   Installation and Service Screens
        -   Shutdown Source
        -   Cross Module Request Timers
-   SETTING UP AND USING THE DIURNAL SETPOINTS PROGRAM
    -   Before You Start.....................................................................................................
    -   Deciding How Many Diurnal Setpoints Programs are Required
    -   Adding the Program
    -   Configuring the Setpoints
        -   Using External Setpoints
        -   Configuring the Periods
        -   Testing and Monitoring
        -   Using the Program Results for Control
        -   Saving a Program Snapshot

## ABOUT THIS MANUAL Contents

```
This manual describes the operation of the Diurnal Setpoints program and its
typical uses.
It is intended as an operator reference for using the program. It also contains setup
information, although, in most cases, this program is preconfigured by Argus.
Please read this manual carefully to familiarize yourself with the properties and
features of the program.
If at any time you encounter problems with this program, or are unsure how to
proceed, please call Argus Support for assistance.
```

## INTRODUCTION

This program is used to define up to 8 Periods in a 24 - hour day.

These time Periods are then used to declare setpoint values and calculate control
targets that can be varied with each Period. Each day, these Periods begin and end
at the times you set.

You can define up to 8 different control setpoints that can be varied at each enabled
Period. This enables you to coordinate equipment use by varying several
parameters within common time Periods.

It is most commonly used for setting up day/night setpoints for heating, cooling, and
humidity management. It can also be used to vary other parameters that you need to
change with the time of day.

If you leave a sufficient time interval between the end of one Period and the start of
the next, the control system gradually ramps the setpoint values between them,
providing a smooth transition from one Period to another.

One advantage to using this program is that many programs can use its results.
Therefore, you don’t need to double-enter your climate targets in more than one
place. A single change in the Diurnal Setpoints program is affected in all the
programs that rely upon its output values.

### When to use the Program

Use this program whenever you need vary setpoints cyclically over a 24 - hour Period.
The scheduled target values produced by this program can then be used by other
control programs then responds to these changing target values throughout the day.

### Alternatives to this Program

If you need to schedule setpoint changes over a Period of time other than diurnal,
you can use the Multi-Step Setpoint Schedule in conjunction with a Triggers
program. They enable you to define shorter or longer-term cyclical or one-time event
schedules. In more advanced configurations you can use a Diurnal Setpoints
program and a Multi-Step Setpoint Schedule to vary setpoints both diurnally and
over a Period of days weeks or months.

### How the Program Works

Up to eight Periods can be defined with sequential start and end times. These times
can be absolute (time of day) or they can be relative to dawn and dusk. When
active, each Period controls the values of the up to eight defined control setpoints it
contains.

Up to 8 different setpoint parameters can be created within each diurnal program,
and you can assign up to 8 different values (one for each Period) for each setpoint.

For example, if you use the first setpoint for defining a climate heating setpoint, you
can then enter up to 8 different heating setpoint temperatures to be used throughout
each day.

Once configured and linked to a heating application, this target heating value is
adjusted according to the setpoint values you enter for each active Period.

## PROGRAM SECTIONS

The Diurnal Setpoints program consists of:

-   a Main Screen
-   a Big Overview window so that you can see the effects of all Periods and
    settings on a single screen
-   separate Setpoint screens for configuring each setpoint
-   separate Period screens for fully defining each Period, and an alarms
    overview screen.
-   An alarms screen for configuring optional Period overlap and ramp rate
    alarms

In addition, there is a Service and Installation screen used primarily for service
diagnostics and program setup.

### The Main Screen

This screen provides a summary of the Diurnal Setpoints program, the current
status of the program, as well as menu entries for each Period and Setpoint screen.

The current Scheduled output values are displayed for each setpoint. Only [Enabled]
Periods are used to determine the current output values.

Clicking the green menu entry for each Period and Setpoint takes you to a details
window for that item.

### Big Overview Window

From the main screen of the Diurnal Settings program, click the Big Overview
menu entry. A screen like the one below is displayed:

The Big Overview window displays all Periods and all setpoints as well as the time
window settings for each Period. This window contains a lot of information, but when
you need to make changes to a Diurnal Setpoint schedule, it is handy for seeing
everything that is happening in one spot.

#### Period Settings & Status

**_Period Enable – Disable_**

Use this setting to Enable or Disable each Period. All the settings in Disabled
Periods are ignored.

```
Period Settings & Status
```

```
Output Values Status Information
```

**_Period Status Message_**

This message displays the current status of each Period:

[Active] - This Period is currently in use. The current Scheduled Setpoint output
values for the program are being calculated from this Period.

[External Not Active] – The External Enable feature for this Period (located on the
Period Screens) is currently preventing the Period from becoming active

[Inactive] - The Period is not currently in use.

[Disabled] - This Period has been disabled by the [Enable/Disable] setting.

[Not Used: Overlap] - This Period is not used since it is overlapped by the time
settings of a previous Period. If any part of this Period shares the same time as a
previous Period, an overlap occurs.

For example, if Period 1 ends at 2:00 am, and Period 2 is set to start at 2:00 am, then
an overlap occurs, and Period 2 is ignored. To avoid overlaps, make sure there is at
least a 1 - minute separation between the end of a Period and the start of the next.

Time Window Settings

Use the Time Window parameters to define specific days of the week, and a time
range within these days when each Period is active:

**_Period Start/End Times_**

Enter a time value in Military time: 00:00 to 23:

Also, select between Relative time and Absolute time.

```
Note: If the Start Time and End Time for the first Enabled Period are both
set to the same time, then that Period is in use continuously, and all other
enabled Periods display [Not Used: Overlapped]. Whenever two or more
heating Periods overlap, then the lower numbered Period takes
priority and any higher numbered Periods that overlap are bypassed.
```

Using Time Settings

Time settings are entered as hours; then minutes with no separating characters (e.g.
you enter 330 and it is accepted as an entry of [03:30].

Note that times are based on a 24-hour clock (00:00 to 23:59). This prevents
confusion between AM and PM.

If both the Start and End Times are set to identical values, (for example, [0:00]) and
the corresponding time flag is set to [Hours], the time window is active 24 hours per
day).

Time Flags set the corresponding time values to Absolute or Relative. If a Time
Flag is set to [Hours] the corresponding start or end time is absolute, or the exact
time of the day. If a Time Flag is set to [Before Dawn], [After Dawn], [Before Dusk],
or [After Dusk], the corresponding start or end time is relative to the specified event.
Since dawn and dusk times shift with the seasons depending on your latitude, time
settings using relative times shift according to the geographical settings on
your system controller. These settings are found in the Program Manager of the
controller that is currently set as the master controller (broadcasts the Network List).

When using time flags, you should try to use the same function (absolute or relative)
for your Start and End times unless you want the specified time in between to vary
with the seasons.

Otherwise, one time is fixed, and the other is floating. For example: if you set a start
time of [7:00] [Hours] (absolute time), and an end time of [1:00] [After Dawn] (relative
time), the duration of the window changes with the seasons, depending upon your
latitude. It may even cause an overlap between the Start and End times at certain
times of the year. Overlapped times are treated literally.

For example, if the calculated Start time is [8:00] and the End time is [7:50], then the
Time window is valid from 8:00 am until 7:50 am and not valid for only 10 minutes
between 7:50 AM and 8:00 AM.

Similarly, it is often good practice to use the same functions (absolute or relative)
between the end of one controlled Period and the start of the next. Otherwise, the
interval between these Periods varies with the season. When this happens, the
normal variations in solar dawn or dusk may change the ramp rate or cause an
overlap. If this practice is not followed, unpredictable behavior may result.

The duration of the time window changes with the seasons if you set one time
relative to dawn and the other relative to dusk. However, the time window duration
does not change if both times reference the same event, either dawn or dusk.

**_Period Calculated Start Time_**

This reading displays the calculated Start Time for this Period.

**_Period Calculated End Time_**

This reading displays the calculated End Time for this Period.

**_Period Day of the Week Setting_**

This selection launches the following dialog:

Use this dialog to select the days of the week when this Period is used. Select the
OK button when finished.

**_Period Day of the Week Status_**

This message indicates whether this Period is used today, according the Period Day
of the Week Setting.

-   [Active] Today is a valid day.
-   [Inactive] Today is not a valid day.

**_Period Setpoints_**

Each Diurnal Setpoint program can vary up to 8 different setpoints over up to 8 daily
Periods. In this section you can define the setpoint values for up to 8 different
setpoints to be used during this Period.

Enter a local value or assign an external parameter value for each setpoint that is in
use in this period.

For more information on using external setpoint items see Using External Setpoints
in the Setting Up and Using the Diurnal Setpoints section of this manual.

```
Warning: Use external setpoints with extreme care! Remember that
whenever you use external variables as setpoints, the values you see may
not be the values that are used when the Period becomes active.
```

```
Since they are calculated external to this program, external values are
therefore more prone to create setpoint change rate problems and possible
control overlaps if they are not used carefully.
```

```
For example, if the underlying heating setpoint calculator program were to
produce a value that is higher than the current cooling setpoint, both the
heating and cooling equipment might operate simultaneously.
```

#### Scheduled Setpoints

This section displays the currently scheduled setpoints for this parameter.

To use these values in other programs, you must create a link to it from the source
program. This section displays the current calculated output values for each
configured setpoint. If a Period is currently active, these scheduled setpoints values is
the same as the setpoints for that Period. In between Periods, these output amounts
are calculated to ramp gradually to the setpoint values in the next active period.
These output values are used as the inputs to drive other programs.

#### Status Information

The Status Information area displays the current day and time, and today’s Dawn
and Dusk time. In addition, the current Period and ramping status is displayed:

**_Period Day of Week_**

This displays the current day of the week according to the time, date, and geographic
settings entered for your control system. These settings can be found in the System
Clock section in the Program Manager program.

To adjust the time on multiple controller systems, you must change the values on the
controller that is currently designated as the master controller (the controller with the
Network List).

**_Period Current Time_**

Displays the current time of day according to the time, date, and geographic settings
entered for your control system. These settings can be found in the System Clock
section in the Program Manager program.

To adjust the time on multiple controller systems, you must change the values on the
controller that is currently designated as the master controller (the controller with the
Network List).

**_Diurnal Program Status_**

This reading shows the current Period in use, or the ramping status if there is no
Period currently in use.

**_Dawn_**

This reading displays the calculated time for solar dawn according to the time, date,
and geographic settings entered for your control system. These settings can be
found in the System Clock section in the Program Manager program.

To adjust the time on multiple controller systems, you must change the values on the
controller that is currently designated as the master controller (the controller with the
Network List).

**_Dusk_**

This reading displays the calculated time for solar dusk according to the time, date,
and geographic settings entered for your control system. These settings can be
found in the System Clock section in the Program Manager program.

To adjust the time on multiple controller systems, you must change the values on the
controller that is currently designated as the master controller (the controller with the
Network List).

### Period Screens

If you click on any Period menu entry on the Main Screen a screen like the following
is displayed:

The Period window contains the Time Window settings for the Period as well as the
setpoint values to be used. For each Period you can define:

-   Whether the Period is [Enabled] or [Disabled] (Disabled Periods are ignored).
-   The Start Time and End Time of the Period.
-   The Days of the Week when the Period is active.
-   Up to 8 setpoint values to be used for this Period.
-   An optional External Enabler for the Period.

**_Period Name_**

You can enter a name for this Period if desired, or you can just leave the default
Period numbers as the Period names.

**_Period Enable – Disable_**

Use this setting to [Enable] or [Disable] each Period. All settings in Disabled Periods
are ignored.

**_Period Status Message_**

This message displays the current status of each Period:

[Active] - This Period is currently in use. The current Scheduled Setpoint output
values for the program are being calculated from this Period.

[External Not Active] – The External Enable feature for this Period (located on the
Period Screens) is currently preventing the Period from becoming active.

[Inactive] - The Period is not currently in use.

[Disabled] - This Period has been disabled by the [Enable/Disable] setting.

[Not Used: Overlap] - This Period is not used since it is overlapped by the time
settings of a previous Period. If any part of this Period shares the same time as a
previous Period, an overlap occurs.

For example, if Period 1 ends at 2:00 am, and Period 2 is set to start at 2:00 am, then
an overlap occurs, and Period 2 is ignored. To avoid overlaps, make sure there is at
least a 1 - minute separation between the end of a Period and the start of the next.

#### Time Window Settings

Use the Time Window parameters to define specific days of the week, and a time
range within these days when each Period is active:

**_Period Start/End Times_**

**_Time Entry_**

Enter a time value in Military time: 00:00 to 23:

Also, select between Relative time and Absolute time.

```
Note: If the Start Time and End Time for the first Enabled Period are both
set to the same time, then that Period is in use continuously, and all other
enabled Periods displays [Not Used: Overlapped]. Whenever two or more
heating Periods overlap, then the lower numbered Period takes priority and
any higher numbered Periods that overlap are bypassed.
```

Using Time Settings

Time settings are entered as hours; then minutes with no separating characters (e.g.
you enter 330 and it is accepted as an entry of [03:30].

Note that times are based on a 24-hour clock (00:00 to 23:59). This prevents
confusion between AM and PM If both the Start and End Times are set to identical
values, (for example, [0:00]) and the corresponding time flag is set to [Hours], the
time window is active 24 hours per day).

Time Flags set the corresponding time values to Absolute or Relative. If a Time
Flag is set to [Hours] the corresponding start or end time is absolute, or the exact
time of the day. If a Time Flag is set to [Before Dawn], [After Dawn], [Before Dusk],
or [After Dusk], the corresponding start or end time is relative to the specified event.

Since dawn and dusk times shift with the seasons depending on your latitude, time
settings using relative times shift according to the geographical settings on
your system controller. These settings are found in the Program Manager of the
controller that is currently set as the master controller (broadcasts the Network List).

When using time flags, you should try to use the same function (absolute or relative)
for your Start and End times unless you want the specified time in between to vary
with the seasons. Otherwise, one time is fixed, and the other is floating.

For example: if you set a start time of [7:00] [Hours] (absolute time), and an end time
of [1:00] [After Dawn] (relative time), the duration of the window changes with the
seasons, depending upon your latitude. It may even cause an overlap between the
Start and End times at certain times of the year. Overlapped times are treated
literally.

For example, if the calculated Start time is [8:00] and the End time is [7:50], then the
Time window is valid from 8:00 am until 7:50 am and not valid for only 10 minutes
between 7:50 AM and 8:00 AM.

Similarly, it is often good practice to use the same functions (absolute or relative)
between the end of one controlled Period and the start of the next. Otherwise, the
interval between these Periods varies with the season. When this happens, the
normal variations in solar dawn or dusk may change the ramp rate or cause an
overlap. If this practice is not followed, unpredictable behavior may result.

The duration of the time window changes with the seasons if you set one time
relative to dawn and the other relative to dusk. However, the time window duration
does not change if both times reference the same event, either dawn or dusk.

**_Period Calculated Start Time_**

This reading displays the calculated Start Time for this Period.

**_Period Calculated End Time_**

This reading displays the calculated End Time for this Period.

**_Period Day of the Week Setting_**

This selection launches the following dialog:

Use this dialog to select the days of the week when this Period is used.

**_Period Day of the Week Status_**

This message indicates whether this Period is used today, according the Period Day
of the Week Setting.

-   [Active] The Period is set to be used today.
-   [Inactive] the Period is not set to be used today.

#### Period Setpoints

Each Diurnal Setpoint program can vary up to 8 different setpoints over up to 8 daily
Periods. In this section you can define the setpoint values that a single parameter
should use during each Period.

You can also define a target ramp rate to be used when changing from one Period to
the next. If this ramp rate is exceeded, an alarm notification occurs.

**_Period Setpoints_**

Enter a local value or assign an external parameter value for each setpoint that is in
use in this period. For more information on using external setpoint items see Using
External Setpoints in the Setting Up and Using the Diurnal Setpoints section of
this manual.

Warning: Use external setpoints with extreme care! Remember that whenever
you use external variables as setpoints, the values you see may not be the values
that are used when the Period becomes active. Since they are calculated external to
this program, external values are therefore more prone to create setpoint change rate
problems and possible control overlaps if they are not used carefully.

For example, if the underlying heating setpoint calculator program were to produce a
value that is higher than the current cooling setpoint, both the heating and cooling
equipment might operate simultaneously.

#### External Enable Settings

Like the Time window, the External Enable Settings to provide another means of
regulating when the Period can become Active. An External Enable Source Value
is assigned, and the External Enable Value Comparison is set to evaluate whether
the current value is either zero or not zero.

Depending on the value comparison logic, the External Enable Status indicates
whether the External Enable is Active or not Active. When Active, the Period is in
turn allowed to become Active (provided all the other tests are passed). When
Inactive, the Period is prevented from becoming Active.

**_Period External Enable Value Comparison_**

Select an optional source value for enabling this Period. This can be used as an
additional condition that must be true before the Period is used. When an external
value is in use, this parameter displays:

-   [Non-Zero] - When the external value is greater than zero.
-   [Zero] - When the external value is zero.
-   [Not Used] - When no External Override source is used.

**_Period External Enable Logic_**

This setting is used to establish the logic for Externally Enabling a Period. The
External Enabler is used as an additional condition that must be true before the
Period is used. When Active, the Period is used, so long as the other time and day
settings are correct, and the Period is Enabled.

**_Period Override External Value Status_**

This reading displays:

[External Active] - When the Period External Enabler Source and the Period
External Enabler Logic setting are producing an active state.

[External Not Active] - When the Period External Enabler Source and the Period
External Enabler Logic setting are producing an inactive state. This prevents the
Period from being used.

[No External Pointer] –The External Enabler Source value is set to [Not Used].

### Setpoint Screens

If you click a menu entry for any of the 8 setpoints, a screen like the following is
displayed:

The Setpoint window contains the setpoint values for each Period for this item, as
well as settings for display scaling, and configuring optional ramp rate alarms.

**_Setpoint Name_**

Enter a name to describe this setpoint, for example, Cooling Target.

**_Period Status Message_**

This message displays the current status of each Period:

[Active] - This Period is currently in use. The current Scheduled Setpoint output
values for the program are being calculated from this Period.

[External Not Active] – The External Enable feature for this Period (located on the
Period Screens) is currently preventing the Period from becoming active

[Inactive] - The Period is not currently in use

[Disabled] - This Period has been disabled by the [Enable/Disable] setting.

[Not Used: Overlap] - This Period is not used since it is overlapped by the time
settings of a previous Period. If any part of this Period shares the same time as a
previous Period, an overlap occurs.

For example, if Period 1 ends at 2:00 am, and Period 2 is set to start at 2:00 am, then
an overlap occurs, and Period 2 is ignored. To avoid overlaps, make sure there is at
least a 1 - minute separation between the end of a Period and the start of the next.

**_Period Setpoints_**

Enter a local value or assign an external parameter value for each setpoint that is in
use in this period. For more information on using external setpoint items see Using
External Setpoints in the Setting Up and Using the Diurnal Setpoints section of
this manual.

Warning: Use external setpoints with extreme care! Remember that whenever
you use external variables as setpoints, the values you see may not be the values
that are used when the Period becomes active.

Since they are calculated external to this program, external values are therefore more
prone to create setpoint change rate problems and possible control overlaps if they
are not used carefully.

For example, if the underlying heating setpoint calculator program were to produce a
value that is higher than the current cooling setpoint, both the heating and cooling
equipment might operate simultaneously.

#### Setpoint Ramp Rate Alarms

In between Periods, the system gradually ramps the Period setpoints from the
previously used Period to the next Period to be used. You can set maximum increase
and decrease ramp rates to help protect against undesirable change rates.

For example, low values (for example, 1.0°C per hour) may be required for crops or
climate contents that are susceptible to disease and humidity problems, while higher
values (for example, 3.5°C per hour) may be acceptable for hardier crops or crops
grown at low humidity. Gradual ramping from one heating Period to the next can also
help protect heating and ventilation equipment.

Ramp rate alarms can be configured for each Setpoint to notify you if the setpoint
changes to rapidly from the Period to the next. The program considers all Enabled
Periods in calculating the change rates.

The Setpoint Ramp Alarm Status reading displays the ramping status for each
Period. Use the Setpoint Ramp Rate Alarm to set the alarm priority for each
setpoint. Use the Setpoint Ramp Rate Alarm Clearing Setting to clear past alarms.

**_Setpoint Ramp Alarm Status_**

This reading indicates whether the setpoint ramp rate is exceeded when ramping to
or from this Period. The program continuously checks the Period setpoints and the
times between Periods to see if the increase or decrease ramp rate is exceed when
transitioning to and from the enabled Periods.

-   [ Okay ] - Indicates that the ramp rate is not exceeded.
-   [ Fault! ] - Indicates that the ramp rate is exceeded.

Whenever a fault is detected, the Setpoint Ramp Rate Alarm has become active (if
it is enabled).

**_Ramp Rate Increase Alarm Setting_**

Enter a Ramp Rate Increase Alarm value.

In between Periods, the system gradually changes the setpoint output values from
the previously used Period to the next Period to be used. You can set a maximum
increase Ramp Rate to warn against undesirable change rates.

For example, low values (for example, 1.0°C per hour) may be required for crops
very sensitive to disease and humidity problems, while higher values (for example,
3.5°C per hour) may be acceptable for hardier crops or crops grown at low humidity.

Gradual ramping from one heating Period to the next can also help protect your
heating and ventilation equipment, and may be more energy efficient then sudden
changes. To disable this alarm so that it is never activated, enter a very high value
such as 100°C if this is a temperature value.

**_Ramp Rate Decrease Alarm Setting_**

Enter a Ramp Rate Decrease Alarm value.

In between Periods, the system gradually changes the setpoint output values from
the previously used Period to the next Period to be used. You can set a maximum
decrease ramp rate Ramp Rate to warn against undesirable change rates.

For example, low values (for example, 1.0°C per hour) may be required for crops
very sensitive to disease and humidity problems, while higher values (for example,
3.5°C per hour) may be acceptable for hardier crops or crops grown at low humidity.

Gradual ramping from one heating Period to the next can also help protect your
heating and ventilation equipment, and may be more energy efficient then sudden
changes.

To disable this alarm so that it is never activated, enter a very high value such as
100°C if this is a temperature value.

**_Current Ramp Rate_**

This reading displays the current ramp rate for this setpoint.

**_Setpoint Ramp Rate Alarm_**

Select an alarm priority for this setpoint. Whenever the program calculates that the
Ramp Rate Decrease Alarm Setting or the Ramp Rate Increase Alarm Setting is
or is exceeded.

Once an alarm condition has passed (is no longer active) this alarm can be cleared
with the Setpoint Ramp Rate Alarm Clearing Setting.

Note: to use the level 1 to 5 alarms, an alarm outputs program must be installed, and
the alarm outputs must be wired accordingly on the controller. On multiple controller
systems the alarm outputs are normally located on the master Controller (the
controller that broadcasts the network list).

```
[No Alarm] the alarm is disabled.
```

```
[Silent] This is the lowest priority alarm intended for management
purposes. Users are notified through the control program
interface only.
```

```
[Module] The beeper in the controller module sounds.
[Level #1] Actions associated with a Level #1 alarm in an Alarm Outputs
Program is initiated.
```

```
[Level #2] Actions associated with a Level #2 alarm in an Alarm Outputs
Program is initiated.
```

```
[Level #3] Actions associated with a Level #3 alarm in an Alarm Outputs
Program is initiated.
```

```
[Level #4] Actions associated with a Level #4 alarm in an Alarm Outputs
Program is initiated.
```

```
[Level #5] Actions associated with a Level #5 alarm in an Alarm Outputs
Program is initiated.
```

**_Setpoint Ramp Rate Alarm Clearing Setting_**

Whenever its associated setpoint ramp rate alarm is or has been active, this setting
displays [Clear Ramp Rate Alarms]. Once you have resolved the active alarm
condition, or the condition has resolved itself, this message reads [No Ramp Rate
Alarms].

### Alarms Window

The Diurnal Setpoint program has many built in alarms to help with program
configuration and warn against problems with setpoint ramp rates and Period
overlaps.

These alarms are optional.

When you click on the Alarms menu entry on the main Screen of the Diurnal
Setpoints program this screen appears:

You can use the optional setpoint alarms to warn you if the rate of change (Ramp
Rate) between one Period and the next exceeds the Ramp Rate Increase or
Decrease settings for each setpoint.

These settings are found on the setup screens for each setpoint.

**_Clear Latched Alarms_**

Use this setting to clear any past alarms for this program.

**_Diurnal Program Status_**

This reading shows the current Period in use, or the ramping status if there is no
Period currently in use.

**_Setpoint Ramp Alarm Status_**

This reading indicates whether the setpoint ramp rate is exceeded when ramping to
or from this Period. The program continuously checks the Period setpoints and the
times between Periods to see if the increase or decrease ramp rate is exceed when
transitioning to and from the enabled Periods.

-   [ Okay ] - Indicates the ramp rate is not exceeded.
-   [ Fault! ] - Indicates the ramp rate is exceeded.

Whenever a fault is detected, the Setpoint Ramp Rate Alarm becomes active (if it is
enabled).

**_Current Ramp Rate_**

This reading displays the current ramp rate for this setpoint.

**_Setpoint Ramp Rate Alarm_**

Select an alarm priority for this setpoint. Whenever the program calculates that the
Ramp Rate Decrease Alarm Setting or the Ramp Rate Increase Alarm Setting is
or is exceeded. Once an alarm condition has passed (is no longer active) this alarm
can be cleared with the Setpoint Ramp Rate Alarm Clearing Setting.

```
[No Alarm] The alarm is disabled
```

```
[Silent] This is the lowest priority alarm intended for
management purposes.
Users are notified through the control program interface
only.
[Module] The beeper in the controller module sounds.
```

```
[Level #1] Actions associated with a Level #1 alarm in an Alarm
Outputs Program is initiated.
```

```
[Level #2] Actions associated with a Level #2 alarm in an Alarm
Outputs Program is initiated.
[Level #3] Actions associated with a Level #3 alarm in an Alarm
Outputs Program is initiated.
```

```
[Level #4] Actions associated with a Level #4 alarm in an Alarm
Outputs Program is initiated.
```

```
[Level #5] Actions associated with a Level #5 alarm in an Alarm
Outputs Program is initiated.
```

**_Setpoint Ramp Rate Alarm Clearing Setting_**

Whenever its associated setpoint ramp rate alarm is active, this setting displays
[Clear Ramp Rate Alarms].

Once you have resolved the active alarm condition, or the condition has resolved
itself, this message change to read [No Ramp Rate Alarms].

#### Period Overlap Alarm

Use this section to set the Period Overlap Alarm Priority whenever the program
calculates that there is or is a Period overlap. Period overlaps can occur when any
Enabled Periods that overlaps the active time of another Enabled Period.

Overlaps can occur because of mistakes in setting up the Period start and end times.
Periods that previously did not overlap may do so if relative time settings are used,
since some start and end times shift with the seasons while other remains fixed.

Once an alarm condition has passed, use the Period Overlap Alarm Clearing
setting to clear it.

**_Period Overlap Alarm Priority_**

Select an alarm priority for whenever the program calculates that there is or is a
Period overlap.

```
[No Alarm] The alarm is disabled
```

```
[Silent] This is the lowest priority alarm intended for management
purposes.
Users are notified through the control program interface
only.
```

```
[Module] The beeper in the controller module sounds.
```

```
[Level #1] Actions associated with a Level #1 alarm in an Alarm
Outputs Program is initiated.
```

```
[Level #2] Actions associated with a Level #2 alarm in an Alarm
Outputs Program is initiated.
```

```
[Level #3] Actions associated with a Level #3 alarm in an Alarm
Outputs Program is initiated.
[Level #4] Actions associated with a Level #4 alarm in an Alarm
Outputs Program is initiated.
```

```
[Level #5] Actions associated with a Level #5 alarm in an Alarm
Outputs Program is initiated.
```

**_Period Overlap Alarm Clearing_**

When there are one or more active Period Overlaps this setting displays [Clear
Overlap Latched Alarms]. When there are no Period overlaps this setting displays
[No Period Overlaps]

### Installation and Service Screens

The Installation and Service section contains readings and settings not normally
required by users. They are used for diagnosing problems and other special
situations. On occasion, you may be asked by Argus technical support personnel to
review or modify these settings.

Warning **–** always check with Argus before you modify any of the values on
Installation and Service Screens.

#### Shutdown Source

This section contains settings for configuring an emergency shutdown of this control
program. It suspends program operations while the configured shutdown condition is
in effect. This might be required during a power outage, when the Argus system
remains running on emergency power, but control of some or all the connected
equipment may no longer be possible.

The shutdown feature can be useful to suspend the operations and calculations
within programs without losing current values. The Shutdown source can be any
numeric reading on your system. It is often assigned to a sensor channel that has
been connected to a relay which is held closed by an electrical circuit. When the
power fails, the circuit opens, signaling a power outage.

Whenever the Shutdown Source Reading [Is More Than] or [Is Less Than] the
Shutdown Threshold setting, this section registers a [SHUTDOWN!!] condition. If
the Shutdown Action setting is set to [Force Shutdown], program operation is
suspended. If the Shutdown Action setting is set to [Ignore Shutdown], the shutdown
section is ignored (not used).

**_Restarting_**

When the Shutdown condition clears, or when the Argus controller has restarted after
a power failure, it waits for the power staging delay time before allowing this control
program to resume operation.

Therefore, if you enter different delay times for your controlled equipment systems
you can stagger the restart timing. This provides effective load management during
startup, preventing possible overload situations as all systems try to restart together.

If the Shutdown Source Reading fails, you can configure the system to either
[Ignore Shutdown] or [Force Shutdown].

Warning: Consider the consequences of these shutdown actions carefully. If you are
using emergency power generation to hold up selected equipment during power
outages, these systems should be hard-wired onto backup power. Do not use the
Argus System as your sole means of load management during power outages.
Use Alarms and Data Recording to warn you of possible dangerous situations. The
improper shutdown of critical equipment could have disastrous consequences if not
promptly rectified.

**_Shutdown Parameters_**

Shutdown Source Label

This is the numeric source value that is used to indicate a shutdown condition. It is
often assigned to a sensor channel configured to sense a loss of electrical power on
a designated circuit.

Shutdown Action

This is used to enable and disable the Shutdown section. If the Shutdown Action
setting is set to [Force Shutdown], program operation is suspended. If the Shutdown
Action setting is set to [Ignore Shutdown], the shutdown section is ignored (not used).

Shutdown Source Reading

This reading displays the current value of the shutdown source parameter. It is often
assigned to a sensor channel configured to sense a loss of electrical power on a
designated circuit.

Shutdown Reading to Threshold Comparison

Select the type of logical comparison to use between the Shutdown Source
Reading and the Shutdown Threshold Value. Whenever the Shutdown Source
Reading [Is More Than] or [Is Less Than] the Shutdown Threshold setting, this
section registers a [SHUTDOWN!!] condition.

Shutdown Threshold Value

Enter a threshold value to be used to determine a shutdown condition. Whenever the
Shutdown Source Reading [Is More Than] or [Is Less Than] the Shutdown
Threshold setting, this section registers a [SHUTDOWN!!] condition.

Power Staging Delay Time

Enter a delay time to be used after the Shutdown condition clears. When the
Shutdown condition clears, or when the Argus controller has restarted after a power
failure, it waits for the power staging delay time before allowing this control program
to resume operation.

Therefore, if you enter different delay times for your controlled equipment systems
you can stagger the restart timing. This provides effective load management during
startup, preventing possible overload situations as all systems try to restart together.

Power Staging Delay Timer

This timer reading counts the Power Staging Delay Time after a shutdown condition
has cleared.

Current Shutdown State

This reading displays [SHUTDOWN!!] when a shutdown condition is in effect. Note:
for the program to be suspended, the Shutdown Action setting must be set to
[Force Shutdown]

#### Cross Module Request Timers

This section contains timers for making cross module requests. Whenever one or
more of the input source parameters for this program are from other controllers, you
can use these timers to regulate how often the information is requested.

This can help cut down on system network traffic for readings that don’t need
refreshing as often as others.

The default value for cross module requests is 30 seconds. Values from 0 - 255 are
accepted.

## SETTING UP AND USING THE DIURNAL SETPOINTS PROGRAM

### Before You Start.....................................................................................................

Normally, your Diurnal Setpoints program is pre-configured by Argus prior to
delivery. Should you require additional Diurnal Setpoints programs, Argus can add
and configure these for you.

If you choose to add programs yourself, or substantially alter your existing
Diurnal Setpoints programs, we advise that you check Argus first, since
changing your factory programming may affect your free technical support.

### Deciding How Many Diurnal Setpoints Programs are Required

Normally, you’ll need a separate Diurnal Setpoints control program for each
separately controlled Climate zone. One Diurnal setpoints program per controlled
zone is often enough, however, you can use more than one program if you have
more than eight setpoints to manage.

### Adding the Program

If you have more than one controller, you’ll need to decide where to install the
program. Since a Diurnal Setpoints program is often part of a climate management
group, you’ll probably want to install the program on the controller where the other
zone heating/cooling equipment is to be installed.

To add a Diurnal Setpoints program:

1. On the controller where you want to add a Diurnal Setpoints Program
   select Add Program:

2. From the program list, select Diurnal Setpoints:
3. Select default settings (you may also see some other selections for
   previously saved Diurnal Setpoints settings. You may select these as
   appropriate:

4. The Diurnal Setpoints screen is displayed as the program is added:
5. Select the Program Name setting at the top of the screen and give the
   program a unique name to identify it:

```
Your Diurnal Setpoints program is now ready for setup.
```

### Configuring the Setpoints

When you first add a blank Diurnal Setpoints program. The setpoints are given
default names, Setpoint #001, Setpoint #002 and so forth (unless you have
selected a previously saved setup). Each setpoint that you intend to use needs to be
configured.

1. From the main screen select the menu entry Setpoint #001.

```
The following Setpoint Screen is displayed:
```

2. At the top of the screen, select the Setpoint Name and enter a name for
   this setpoint. For example, Cooling Target:

3. Since we want to set up this setpoint to define temperature values for
   cooling we need to select a temperature scale from the display scaling
   options. Select the display scaling parameter and the following dialog is
   displayed:
4. Click on the dropdown icon in the Name Field and select Arg: Temp.
   (Abs)

5. Next, click the Finished button:
6. Select the Yes button to save your selection:
7. All the Period setpoints for this setpoint displays as temperature values
   and the Display Scaling reads [Arg: Temp (Abs)]:

```
Your cooling setpoint has now been configured. You could start entering
the temperature values for each Period on this screen, but since we
haven’t yet defined when each Period starts or ends we should do that
first.
```

8. Before leaving the setpoint screen you should decide whether you want to
   configure Ramp Rate alarms to warn you of schedules that results in
   excessive rates of increase or decrease in setpoint values.
   Enter increase and decrease rates and set the Alarm priority as described
   in the Alarms section of this manual. If you do not ever wish to use ramp
   rate alarms, set the priority to [No Alarm].
   To effectively disable the ramp alarm so that it is never activated and never
   displays a ramp fault on the alarms screen, enter a very high value (such as
   300°C in this case) for the Maximum Increase and Decrease rate settings.
9. For each of the 8 setpoints that you wish to configure, repeat the above
   steps.

#### Using External Setpoints

Each setpoint value can be entered as a local value or an external reading value. For
local values, you simply enter a numeric value and it remains as a constant until the
next time you change it.

For external values, you assign a parameter from an external location.

For example, if the you wish to have another program calculate the current value for
a heating setpoint rather than declare it, you can use other programs such as the
Math Matrix or the Multi-Setpoint Schedule, to generate a calculated value.

You then point to the result to use it in one or more of the Periods for this setpoint.

Here’s how:

1. First, we assume that you have already have one or more programs
   preconfigured to decide what the heating setpoint should be. This is the
   value you want to use for your daytime heating periods from just before
   dawn till after dusk.
   From the Big Overview window, click on the heating setpoint in the first
   Period that occurs in the daylight hours:

```
The numeric Entry dialog is displayed:
```

```
Instead of entering a value click the External Item button.
```

2. Navigate to the program screen that contains your calculated heating
   setpoint and select it by clicking the right mouse button on the parameter:

3. You are then returned to the Big Overview window where you started,
   and your setpoint is now displayed in red type to indicate that it is now
   a variable amount that is being read from another location.

The Heating Setpoint for this Period is now linked to the value of the source
parameter. It is currently reading 18.45 ºC but changes as the underlying program
changes.

You can link other heating setpoint Periods to this variable as well, or leave them as
local values (constants). In the example below, the night time Period (4) has been left
as a constant, and the first three Periods are using the same calculated external
heating setpoint value(red).

```
Warning: Use external setpoints with extreme care! Remember that
whenever you use external variables as setpoints, the values you see may
not be the values that are used when the Period becomes active. Since
they are calculated external to this program, external values are therefore
more prone to create setpoint change rate problems and possible control
overlaps if they are not used carefully.
```

```
For example, if the underlying heating setpoint calculator program were to
produce a value that is higher than the current cooling setpoint, both the
heating and cooling equipment might operate simultaneously.
```

#### Configuring the Periods

You can perform most of the program setup for Periods in the Big Overview window.

However, if you wish to use any External Enable logic for particular Periods, you
must do this from the Period Screens. See the External Enable settings in the
Period Screens section of this manual for further details.

When setting up your diurnal Periods you should enter sequential Start and End
times, starting at Period 1, and using as many Periods as you need to describe the
settings changes for a 24 - hour Period.

To avoid overlaps, each Period should start after the end of the previous one, and
the last Period used should end before the start of the first Period. This creates a
cycle of start and end times throughout the day.

Although you do not need to enter start and end times sequentially (the program
figures out the order) it is much easier for you to use the program if one Period
follows the last.

As you enter start and end times and enable the Periods, the program tests each one
to see if it is overlapped by a previously occurring Period and it determines the
current position with respect to the enabled Periods.

**_Leave a Time Space Between Periods_**

Although it is not mandatory, under most circumstances you should leave a
reasonable time interval between the end of one Period and the start of the
next.

This allows the program to gradually ramp up or down to the next setpoints, which in
turn provides for efficient and smooth control of your environmental control
equipment.

It also helps avoid the ‘slamming effect’ when target values are changed too rapidly,
causing an extreme control response (such as full heating or full venting) to try to
achieve the new targets.

You can use the increase/decrease setpoint change rate settings and optional
alarms on the setpoint screens to warn you of insufficient intervals between Periods.
You can then see if there are any ramp rate problems on the Alarms screen.

The rate of setpoint increase or decrease between Periods is a function of the time
between Periods and the degree of change in the setpoint values. Therefore, you
may require more time separation between Periods the greater the change in
setpoint values.

**_Use Ramp and Overlap Alarms_**

Whenever you use relative times, the length of each Period and the times in between
Periods changes with the seasons.

Therefore, you may have no problems with ramp rate alarms or Period overlaps at
one time of year, but encounter them as the seasons change. Use management
alarms on the alarms screen to monitor these events and let you know when they
occur.

Note: The program is always either in a Period or ramping between Periods. Period
1 is the default Period even when all Periods are disabled. If there are currently
no Periods to ramp to or from, the program uses Period 1’s settings continuously.

The simplified diagram below shows a cooling setpoint (blue) and a heating setpoint
(red) configured over 2 Periods. Period 1 starts at 8:00 hours and ends at 17:00
hours.

Period 2 starts at 20:00 hours and ends at 5:00 hours. The spaces in between
(yellow) are the ramp intervals.

Notice that the heating and cooling setpoints have been offset by a couple of degrees
to avoid ping-pong cycling of the heating and cooling equipment when the thresholds
between them are set too narrow.

**_Using Time Windowing_**

There are many ways that you can use the Time Windowing settings to produce
varying daily schedules throughout the week. The example below shows an example
where different daytime temperature are used on alternating days. This is
accomplished by using two sets of 4 periods.

The first set (Periods 1 to 4) are valid on Mondays, Wednesdays, Fridays, and
Sundays.

The second set (Periods 5-8) are valid on Tuesdays, Thursdays and Saturdays.

This type of schedule might be used for a tomato crop where you wish to provide a
midday heat boost on alternate days to aid in pollination.

#### Testing and Monitoring

As you set up your Periods and enter setpoint values, the program continuously
calculates the current target values and reports where it currently is with respect to
the enabled Periods. You should record and graph the Scheduled Setpoints
(outputs) over the next 24 hours to make sure they behave as expected

#### Using the Program Results for Control

Once you have everything set the way you want it, you are ready to start using the
scheduled setpoints for control.

Several programs can make use of scheduled setpoints. These include the Energy
Balance program and other programs such as Equations programs that are
configured for heating and cooling. Usually, these programs compare the target
values generated by the Diurnal Setpoints program with current conditions to
determine a course of action with respect to equipment control.

The Energy Balance program contains external pointer inputs that are configured to
read the heating, cooling, and humidity setpoints. Equations programs can be used
to compare any types of setpoint values against currently measured values.

When using diurnal setpoints in control programs you can compare the recorded
scheduled targets against the actual values that are achieved to measure your
equipment performance and keep track of your controlled climates.

#### Saving a Program Snapshot

You can use the Copy Program utility at any time to save a ‘snapshot’ of your
program. When you save a copy, all the settings, labels and display scaling values
are saved. You can then create copies of this program configuration for use
elsewhere or save seasonal snapshots to paste back in to your program at different
times of year.

Note: Whenever you use the copy program to create a new program or paste saved
values back into an existing program, the previous external linkages (if any) may not
be correct for your new location. They are displayed with a green background until
you click on each one to verify or change the location to which they point or to select
another value to assign.

ARGUS CONTROL SYSTEMS LTD.

```
Telephone: (************* or (604) 536- 9100
Toll Free Sales: (************* (North America)
Toll Free Service: (************* (North America)
Fax: (604) 538- 4728
E-mail: <EMAIL>
Web Site: http://www.arguscontrols.com
```

Information in this manual is subject to change without notice.

 Copyright 2017 Argus Control Systems Ltd.

Published in Canada
Argus is a registered trademark of Argus Control Systems Ltd.

;	NOLIST

NUMENT: EQU	8	;8 SETPOINTS
NUMPER:	EQU	8	;8 PERIODS
NMPTRS:	EQU	(NUMENT*NUMPER)+NUMPER

	;THE SETPOINT CALCULATION TABLES ARE AS FOLLOWS:
	;FIXED
	off .set 0		;INITIALIZE THE OFFSET VALUE 'OFF=0'
 o	SETPTS,NUMPER*4	;SETPOINTS FOR EACH TIME PERIOD
 o	SCALE,2		;DISPLAY SCALING
 o	URATE,2		;Alarm if Increase Ramp Rate Exceeds
 o	DRATE,2		;Alarm if Decrease Ramp Rate Exceeds
 o	RMPALP,1		;Setpoint Ramp Rate Alarm Priority
 offalign 4	;FORCE LONG ALIGNMENT
 o	STPFXL,0		;TABLE LENGTH
 
	;VARIABLE
	off .set 0		;INITIALIZE THE OFFSET VALUE 'OFF=0'
 o	RDG,NUMPER*4 ;READINGS AND WATCHDOGS FOR EACH SETPOINT
 o	CLCSPT,2	;SCHEDULED SETPOINT
 o	CRMPRT,2	;CURRENT RAMP RATE (SIGNED)
 o	SPTVEC,1	;CALCULATION VECTOR
 o	RMPALM,1	;RAMP RATE EXCEEDED ALARM
 o	RMPHAL,1	;RAMP RATE WAS EXCEEDED ALARMS
 
 offalign 4	;FORCE LONG ALIGNMENT
 o	STPVRL,0

	;THE 'RMPALM' RAMP ALARM BITS ARE AS FOLLOWS:
RP1ALM:	EQU	0	;RAMP 1 RATE EXCEEDED
RP2ALM:	EQU	1	;RAMP 2 RATE EXCEEDED
RP3ALM:	EQU	2	;RAMP 3 RATE EXCEEDED
RP4ALM:	EQU	3	;RAMP 4 RATE EXCEEDED
RP5ALM:	EQU	4	;RAMP 5 RATE EXCEEDED
RP6ALM:	EQU	5	;RAMP 6 RATE EXCEEDED
RP7ALM:	EQU	6	;RAMP 7 RATE EXCEEDED
RP8ALM:	EQU	7	;RAMP 8 RATE EXCEEDED

	;THE TIME PERIOD GROUP FIXED TABLES ARE:
	off .set 0		;INITIALIZE THE OFFSET VALUE 'OFF=0'
 o	EXTPTR,4	;Period Override External Pointer
 o	STIME,2	;Period Start Time
 o	ETIME,2	;Period End Time
 o	DOW,1	;Period Days of the Week
 o	PERFFL,1	;Period Settings Flag Bits
 offalign 4	;FORCE LONG ALIGNMENT
 o	PERFXL,0

	;VARIABLES
	off .set 0		;INITIALIZE THE OFFSET VALUE 'OFF=0'
 o	CSTIME,2	;Period Calculated Start Time
 o	CETIME,2	;Period Calculated End Time
 o	OVRRDG,1	;Period Override Reading
 o	OVRWDG,1	;Period Override Reading Watchdog
 o	PERVFL,1	;Period Variables flags
 o	ETVDOW,1	;End Time Valid on these Days Of the Week
 offalign 2	;FORCE WORD ALIGNMENT
 o	PERVRL,0

	;The 'PERVFL' flag bits are:
HASPTR:	EQU	0	;HAS AN EXTERNAL POINTER
EXTACT:	EQU	1	;EXTERNAL READING ACTIVE OR NO POINTER ASSIGNED
INTIMW:	EQU	2	;INSIDE THE TIME WINDOW
ACTSTM:	EQU	3	;START TIME IS VALID FOR DAY OF WEEK
ACTETM:	EQU	4	;END TIME IS VALID FOR DAY OF WEEK
ACTIVE:	EQU	5	;THIS PERIOD IS ACTIVE
PEROVR:	EQU	6	;PERIOD OVERLAPS
;ENBPER: EQU	7	;PERIOD IS ENABLED

	;The 'ETVDOW' flag bits are:
;MON:	EQU	0	;1 = valid on this day
;TUE:	EQU	1
;WED:	EQU	2
;THU:	EQU	3
;FRI:	EQU	4
;SAT:	EQU	5
;SUN:	EQU	6
Wraps_Through_Midnight: EQU 7	;end time lower than start time

	;The 'PERFFL' flag bits are:
ACZERO:	EQU	0	;EXTERNAL READING ACTIVE WHEN ZERO
ENBPER:	EQU	7	;ENABLE THE PERIOD

	;THE 'SETPON' SETPOINT USING FLAG BITS ARE:
	;BITS 0-3 ARE ENCODED AS FOLLOWS:
RAMP1:	EQU	0	;RAMPING FROM PERIOD 1 TO PERIOD 2
RAMP2:	EQU	1	;RAMPING FROM PERIOD 2 TO PERIOD 3
RAMP3:	EQU	2	;RAMPING FROM PERIOD 3 TO PERIOD 4
RAMP4:	EQU	3	;RAMPING FROM PERIOD 4 TO PERIOD 1
RAMP5:	EQU	4	;RAMPING FROM PERIOD 5 TO PERIOD 2
RAMP6:	EQU	5	;RAMPING FROM PERIOD 6 TO PERIOD 3
RAMP7:	EQU	6	;RAMPING FROM PERIOD 7 TO PERIOD 4
RAMP8:	EQU	7	;RAMPING FROM PERIOD 8 TO PERIOD 1
PERD1:	EQU	8	;IN PERIOD 1
PERD2:	EQU	9	;IN PERIOD 2
PERD3:	EQU	10	;IN PERIOD 3
PERD4:	EQU	11	;IN PERIOD 4
PERD5:	EQU	12	;IN PERIOD 5
PERD6:	EQU	13	;IN PERIOD 6
PERD7:	EQU	14	;IN PERIOD 7
PERD8:	EQU	15	;IN PERIOD 8


	;THE 'BADPER' AND 'SETPAL' BITS ARE AS FOLLOWS:
P1IVAL:	EQU	0	;PERIOD 1 INVALID
P2IVAL:	EQU	1	;PERIOD 2 INVALID
P3IVAL:	EQU	2	;PERIOD 3 INVALID
P4IVAL:	EQU	3	;PERIOD 4 INVALID
P5IVAL:	EQU	4	;PERIOD 5 INVALID
P6IVAL:	EQU	5	;PERIOD 6 INVALID
P7IVAL:	EQU	6	;PERIOD 7 INVALID
P8IVAL:	EQU	7	;PERIOD 8 INVALID


	;THE CLIMATE SETPOINT FIXED SETTINGS ARE:
	off .set 0		;INITIALIZE THE OFFSET VALUE 'OFF=0'
;************
 o	_DEVTYP,1	;'DEVTYP' DEVICE PROGRAM ASSIGNED TYPE
 o	_ORDER,1	;'ORDER' DEVICE EXECUTION ORDERING
 o	_PWFXFL,1	;'PWFXFL' POWER STAGING FLAGS
 o	_STGTIM,1	;'STGTIM' POWER STAGING DELAY TIME
 o	_PWRTHR,4	;'PWRTHR' POWER FAIL THRESHOLD (32 BITS)
 o	_PWRPTR,4	;'PWRPTR' POWER FAIL INPUT POINTER
 o	_REQTIM,NMPTRS*1 ;'REQTIM' FOR 'N_PTRS' POINTERS
	;LONGS
 offalign 4	;FORCE LONG ALIGNMENT
 o	TIME_P,4	;REAL TIME CLOCK VALUES DEVICE PROGRAM POINTER
 o	PER1FX,PERFXL	;PERIOD 1
 o	PER2FX,PERFXL	;PERIOD 2
 o	PER3FX,PERFXL	;PERIOD 3
 o	PER4FX,PERFXL	;PERIOD 4
 o	PER5FX,PERFXL	;PERIOD 5
 o	PER6FX,PERFXL	;PERIOD 6
 o	PER7FX,PERFXL	;PERIOD 7
 o	PER8FX,PERFXL	;PERIOD 8

 o	SET1FX,STPFXL	;SETPOINT 1
 o	SET2FX,STPFXL	;SETPOINT 2
 o	SET3FX,STPFXL	;SETPOINT 3
 o	SET4FX,STPFXL	;SETPOINT 4
 o	SET5FX,STPFXL	;SETPOINT 5
 o	SET6FX,STPFXL	;SETPOINT 6
 o	SET7FX,STPFXL	;SETPOINT 7
 o	SET8FX,STPFXL	;SETPOINT 8
 o	POVALP,1		;ALARM PRIORITY FOR PERIOD OVERLAPS
 
 offalign 4	;FORCE LONG ALIGNMENT
	;

	;THE CLIMATE SETPOINT VARIABLES ARE:
 o	DEVVST,0	;START OF DEVICE VARIABLES
 o	PWREAD,4	;'PWREAD' POWER FAIL INPUT
 o	PWRDWD,1	;'WDG'
 o	DEVFLT,1	;DEVICE FAULT FLAGS
 o	DEVVFL,1	;VARIABLES FLAGS
 o	STGTMR,1	;STAGING DELAY TIMER - used to delay alarms
 o	REQTMR,NMPTRS*1 ;FOR POINTERS
 offalign 4	;FORCE LONG ALIGNMENT
 o	TIME_A,4	;REAL TIME CLOCK VALUES ADDRESS
 o	C_HOUR,2	;USED HH:MM
 o	C_DAWN,2	;USED SOLAR DAWN
 o	C_DUSK,2	;USED SOLAR DUSK
 o	C_DYOFWK,1	;USED DAY OF THE WEEK
 o	C_SECOND,1	;USED :SS
 offalign 4	;FORCE LONG ALIGNMENT
 o	PER1VR,PERVRL	;PERIOD 1
 o	PER2VR,PERVRL	;PERIOD 2
 o	PER3VR,PERVRL	;PERIOD 3
 o	PER4VR,PERVRL	;PERIOD 4
 o	PER5VR,PERVRL	;PERIOD 5
 o	PER6VR,PERVRL	;PERIOD 6
 o	PER7VR,PERVRL	;PERIOD 7
 o	PER8VR,PERVRL	;PERIOD 8
 offalign 4	;FORCE LONG ALIGNMENT
 o	SET1VR,STPVRL	;SETPOINT 1
 o	SET2VR,STPVRL	;SETPOINT 2
 o	SET3VR,STPVRL	;SETPOINT 3
 o	SET4VR,STPVRL	;SETPOINT 4
 o	SET5VR,STPVRL	;SETPOINT 5
 o	SET6VR,STPVRL	;SETPOINT 6
 o	SET7VR,STPVRL	;SETPOINT 7
 o	SET8VR,STPVRL	;SETPOINT 8
	;=== note these three bytes must be in order
 o	DSBPFL,1	;DISABLED PERIODS 
 o	PERALM,1	;PERIOD ALARM FLAGS
 o	InactiveExternals,1
	;===
 o	BADPER,1	;SETPOINT WORKING FLAGS
 o	PVECTR,1	;PERIOD CALCULATION VECTOR
 o	DVECTR,1	;DETERMINE PERIOD ON VECTOR
 o	SETPON,1	;SETPOINT PERIOD ON
 o	RAMP_END,1	;PERIOD OF RAMP END (IF A RAMP)
 o	ERAMP1,1	;END OF RAMP 1
 o	ERAMP2,1	;END OF RAMP 2
 o	ERAMP3,1	;END OF RAMP 3
 o	ERAMP4,1	;END OF RAMP 4
 o	ERAMP5,1	;END OF RAMP 5
 o	ERAMP6,1	;END OF RAMP 6
 o	ERAMP7,1	;END OF RAMP 7
 o	ERAMP8,1	;END OF RAMP 8
 o	PERHAL,1	;PERIOD HAD ALARM FLAGS
 o	DebugOn,1	;Code Debugging value
 o	EnabledPeriodsCount,1
 offalign 4	;FORCE LONG ALIGNMENT
 o	InPeriods,NUMPER*1
 o	ToRamps,NUMPER*1
 o	FromRamps,NUMPER*1
 o	InPeriodsOrToRamp,NUMPER*1
 o	InPeriodsOrFromRamp,NUMPER*1
 o	InPeriodsOrToFromRamp,NUMPER*1
 offalign 4	;FORCE LONG ALIGNMENT
 o	LOCAL_ALARMS_BUILDING,4
 o	LOCAL_ALARMS_BUILT,4
 offalign 4	;FORCE LONG ALIGNMENT
 o	DEVVED,0	;END OF DEVICE VARIABLES
DVLENG:	EQU	DEVVED-DEVVST	;TABLE LENGTH


	LIST


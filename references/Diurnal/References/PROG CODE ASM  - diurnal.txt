	TTL "DIURNAL SETPOINT MODULE"
	SBTTL "August 8,2002"

	;C:\Titan\firmware\diurnal.asm
	
	;LATEST VERSION AS OF AUGUST 7, 2002

	;This Program Produces 8 separate Diurnal Setpoints
	;There are up to 8 periods.
	;Each period has:
	;1)	a start time
	;2)	an end time
	;3)	days of the week active on
	;4)	an external period enable pointer
	;5)	a 32-bit value for each of the 8 setpoints
	;	note: if the upper 16-bits are zero then
	;	      then the lower 16-bits is the target setting.
	;	      If the upper 16-bit are non-zero then
	;	      the 32-bit value is a pointer to a setpoint
	

	XDEF	DIURNL

	XREF	DIV32,SOLROF,TIMPER,GETRDS,XMODA4
	XREF	SUBTME,<PERSON>IVF32,<PERSON><PERSON><PERSON><PERSON>,CRC_BLK
	XREF	DIVS32,ADDSGN,SUBSGN
	XREF	ADSF32,SBSF32


	INCLUDE MACROS.INC
	INCLUDE COMMON.INC
	INCLUDE DIURNAL.INC
	INCLUDE DEVICE.INC
	INCLUDE MCF5xxx.INC

	text

	ALIGN	4	;LONG WORD ALIGNMENT
DIURNL:
CODEST:
	DC.B	DS_DV	;DEVICE PROGRAM TYPE
	DC.B	0	;SPARE
	DC.W	CODEND-CODEST	;CODE SEGEMENT LENGTH
	JMP	MAIN(PC)	;PROGRAM START
	JMP	BACKGD(PC)	;BACKGROUND START
	JMP	UNSMSG(PC)	;QUASI-BACKGROUND START
	JMP	PWR_UP(PC)	;CODE RUN AT POWER UP
	JMP	RESETP(PC)	;CODE RUN AT MANUAL RESET
	JMP	RETLNG(PC)	;RETURNS MEMORY LENGTHS/START
	DC.L	PARAMETER_MAP	;OFFSET OF PARAMETER# TO OFFSET
	DC.L	$20070706	;DATE CODE - database
	DC.W	$1730	;TIME
	DC.L	$20140128	;DATE CODE - code
	DC.W	$1200	;TIME
	
;###########
PARAMETER_MAP:
	DC.W	((PAR_END-P00)>>3)-1	;# OF PARAMETERS
	DC.W	DEVVST	;CHECKSUM BLOCK SIZE
	DC.W	DVLENG	;VARIABLES BLOCK SIZE
	DC.W	0	;VERSION
	;PARAMETER OFFSETS AND SIZE
	;
	;THE FIRST 16 PARAMETERS ARE USED
	;TO ALLOW DIRECT ACCESS INTO THE
	;DEVICE PROGRAM'S MEMORY AREA
	;(UP TO 16 Kilobytes)
P00:	DC.W	$0000,0,1,0
P01:	DC.W	$0400,0,1,0
P02:	DC.W	$0800,0,1,0
P03:	DC.W	$0C00,0,1,0
P04:	DC.W	$1000,0,1,0
P05:	DC.W	$1400,0,1,0
P06:	DC.W	$1800,0,1,0
P07:	DC.W	$1C00,0,1,0
P08:	DC.W	$2000,0,1,0
P09:	DC.W	$2400,0,1,0
P10:	DC.W	$2800,0,1,0
P11:	DC.W	$2C00,0,1,0
P12:	DC.W	$3000,0,1,0
P13:	DC.W	$3400,0,1,0
P14:	DC.W	$3800,0,1,0
P15:	DC.W	$3C00,0,1,0
	;
P16:	DC.W	DEVTYP+B,1+S,1,0	;<type=device types><name=Device Program Assigned Type><access=R>
P17:	DC.W	ORDER+B,1+S,1,0	;<type=unsigned><name=Device Execution Ordering>
P18:	DC.W	PWFXFL+B,1+S,1,0	;Power Staging Flags
			;<type=label><name=Shutdown Reading Failed Action - NOT USED><length=15><mask=32>
			;<states=
			;0,RW,Ignore Shutdown
			;32,RW,Force Shutdown
			;>
			;<type=label><name=Shutdown Reading to Threshold Comparison - NOT USED><length=12><mask=128>
			;<states=
			;0,RW,Is Less Than
			;128,RW,Is More Than
			;>
P19:	DC.W	STGTIM+B,1+S,1,0	;<type=unsigned><name=Power Staging Delay Time - NOT USED>
P20:	DC.W	PWRTHR+L,1+S,4,0	;<type=linked32><link=P21><name=Power Fail Threshold - NOT USED>
P21:	DC.W	PWRPTR+P,1+S,4,0	;<type=pointer32><name=Power Fail Input - NOT USED><access=R>
P22:	DC.W	0+B,0+S,1,0 ;SPARE
	;common setpoint values
P23:	DC.W	SET1VR+SPTVEC+B,NUMENT+R,STPVRL,0	;<type=unsigned><name=Setpoint { Calculation Vector>
P24:	DC.W	SET1VR+RMPALM+B,NUMENT+R,STPVRL,0	;<type=unsigned><name=Setpoint { Ramp Alarm>
P25:	DC.W	SET1VR+RMPALM+B,NUMENT+R,STPVRL,0	;<type=label><name=Setpoint { Ramp 1 Alarm><length=6><mask=1>
					;<states=
					;0,R,Okay
					;1,R,Fault!
					;>
P26:	DC.W	SET1VR+RMPALM+B,NUMENT+R,STPVRL,0	;<type=label><name=Setpoint { Ramp 2 Alarm><length=6><mask=2>
					;<states=
					;0,R,Okay
					;2,R,Fault!
					;>
P27:	DC.W	SET1VR+RMPALM+B,NUMENT+R,STPVRL,0	;<type=label><name=Setpoint { Ramp 3 Alarm><length=6><mask=4>
					;<states=
					;0,R,Okay
					;4,R,Fault!
					;>
P28:	DC.W	SET1VR+RMPALM+B,NUMENT+R,STPVRL,0	;<type=label><name=Setpoint { Ramp 4 Alarm><length=6><mask=8>
					;<states=
					;0,R,Okay
					;8,R,Fault!
					;>
P29:	DC.W	SET1VR+RMPALM+B,NUMENT+R,STPVRL,0	;<type=label><name=Setpoint { Ramp 5 Alarm><length=6><mask=16>
					;<states=
					;0,R,Okay
					;16,R,Fault!
					;>
P30:	DC.W	SET1VR+RMPALM+B,NUMENT+R,STPVRL,0	;<type=label><name=Setpoint { Ramp 6 Alarm><length=6><mask=32>
					;<states=
					;0,R,Okay
					;32,R,Fault!
					;>
P31:	DC.W	SET1VR+RMPALM+B,NUMENT+R,STPVRL,0	;<type=label><name=Setpoint { Ramp 7 Alarm><length=6><mask=64>
					;<states=
					;0,R,Okay
					;64,R,Fault!
					;>
P32:	DC.W	SET1VR+RMPALM+B,NUMENT+R,STPVRL,0	;<type=label><name=Setpoint { Ramp 8 Alarm><length=6><mask=128>
					;<states=
					;0,R,Okay
					;128,R,Fault!
					;>
P33:	DC.W	TIME_P+P,1+S,4,0	;<type=pointer32>
			;<name=Clock Time Pointer>
			;<PointerLabelDisplay=program>
			;<ProgramTypeForSelection=43>
			;<LabelSize=40>
P34:	DC.W	0+B,0+S,1,0	;spare
	;setpoint 1
	;********* do not move this P number *********
P35:	DC.W	SET1FX+SETPTS+L,NUMPER+S,4,35	;<type=pointer32><name=Setpoint 1 External Pointer {><access=R><pNumberShift=5>
P36:	DC.W	SET1FX+SCALE+W,1+S,2,0	;<type=scaling><name=Setpoint 1 Display Scaling><DisplayFailed=Yes><pNumberShift=5>
P37:	DC.W	SET1FX+URATE+W,1+S,2,0	;<type=scaled><name=Setpoint 1 Alarm if Increase Ramp Rate Exceeds><link=P36><pNumberShift=5><Relative>
	;********* do not move this P number *********
P38:	DC.W	SET1VR+RDG+W,NUMPER+R,4,35	;<type=scaled><name=Setpoint 1 Reading {><link=P36><DisplayFailed=Yes><local><localLink=P35><pNumberShift=5><access=RW>
P39:	DC.W	SET1VR+CLCSPT+W,1+R,2,35	;<type=scaled><name=Scheduled Setpoint 1><link=P36><DisplayFailed=Yes><pNumberShift=5>
	;setpoint 2
	;********* do not move this P number *********
P40:	DC.W	SET2FX+SETPTS+L,NUMPER+S,4,40	;<type=pointer32><name=Setpoint 2 External Pointer {><access=R><pNumberShift=5>
P41:	DC.W	SET2FX+SCALE+W,1+S,2,0	;<type=scaling><name=Setpoint 2 Display Scaling><DisplayFailed=Yes><pNumberShift=5>
P42:	DC.W	SET2FX+URATE+W,1+S,2,0	;<type=scaled><name=Setpoint 2 Alarm if Increase Ramp Rate Exceeds><link=P41><pNumberShift=5><Relative>
	;********* do not move this P number *********
P43:	DC.W	SET2VR+RDG+W,NUMPER+R,4,40	;<type=scaled><name=Setpoint 2 Reading {><link=P41><DisplayFailed=Yes><local><localLink=P40><access=RW><pNumberShift=5>
P44:	DC.W	SET2VR+CLCSPT+W,1+R,2,40	;<type=scaled><name=Scheduled Setpoint 2><link=P41><DisplayFailed=Yes><pNumberShift=5>
	;setpoint 3
	;********* do not move this P number *********
P45:	DC.W	SET3FX+SETPTS+L,NUMPER+S,4,45	;<type=pointer32><name=Setpoint 3 External Pointer {><access=R><pNumberShift=5>
P46:	DC.W	SET3FX+SCALE+W,1+S,2,0	;<type=scaling><name=Setpoint 3 Display Scaling><DisplayFailed=Yes><pNumberShift=5>
P47:	DC.W	SET3FX+URATE+W,1+S,2,0	;<type=scaled><name=Setpoint 3 Alarm if Increase Ramp Rate Exceeds><link=P46><pNumberShift=5><Relative>
	;********* do not move this P number *********
P48:	DC.W	SET3VR+RDG+W,NUMPER+R,4,45	;<type=scaled><name=Setpoint 3 Reading {><link=P46><DisplayFailed=Yes><local><localLink=P45><access=RW><pNumberShift=5>
P49:	DC.W	SET3VR+CLCSPT+W,1+R,2,45	;<type=scaled><name=Scheduled Setpoint 3><link=P46><DisplayFailed=Yes><pNumberShift=5>
	;setpoint 4
	;********* do not move this P number *********
P50:	DC.W	SET4FX+SETPTS+L,NUMPER+S,4,50	;<type=pointer32><name=Setpoint 4 External Pointer {><access=R><pNumberShift=5>
P51:	DC.W	SET4FX+SCALE+W,1+S,2,0	;<type=scaling><name=Setpoint 4 Display Scaling><DisplayFailed=Yes><pNumberShift=5>
P52:	DC.W	SET4FX+URATE+W,1+S,2,0	;<type=scaled><name=Setpoint 4 Alarm if Increase Ramp Rate Exceeds><link=P51><pNumberShift=5><Relative>
	;********* do not move this P number *********
P53:	DC.W	SET4VR+RDG+W,NUMPER+R,4,50	;<type=scaled><name=Setpoint 4 Reading {><link=P51><DisplayFailed=Yes><local><localLink=P50><access=RW><pNumberShift=5>
P54:	DC.W	SET4VR+CLCSPT+W,1+R,2,50	;<type=scaled><name=Scheduled Setpoint 4><link=P51><DisplayFailed=Yes><pNumberShift=5>
	;setpoint 5
	;********* do not move this P number *********
P55:	DC.W	SET5FX+SETPTS+L,NUMPER+S,4,55	;<type=pointer32><name=Setpoint 5 External Pointer {><access=R><pNumberShift=5>
P56:	DC.W	SET5FX+SCALE+W,1+S,2,0	;<type=scaling><name=Setpoint 5 Display Scaling><DisplayFailed=Yes><pNumberShift=5>
P57:	DC.W	SET5FX+URATE+W,1+S,2,0	;<type=scaled><name=Setpoint 5 Alarm if Increase Ramp Rate Exceeds><link=P56><pNumberShift=5><Relative>
	;********* do not move this P number *********
P58:	DC.W	SET5VR+RDG+W,NUMPER+R,4,55	;<type=scaled><name=Setpoint 5 Reading {><link=P56><DisplayFailed=Yes><local><localLink=P55><access=RW><pNumberShift=5>
P59:	DC.W	SET5VR+CLCSPT+W,1+R,2,55	;<type=scaled><name=Scheduled Setpoint 5><link=P56><DisplayFailed=Yes><pNumberShift=5>
	;setpoint 6
	;********* do not move this P number *********
P60:	DC.W	SET6FX+SETPTS+L,NUMPER+S,4,60	;<type=pointer32><name=Setpoint 6 External Pointer {><access=R><pNumberShift=5>
P61:	DC.W	SET6FX+SCALE+W,1+S,2,0	;<type=scaling><name=Setpoint 6 Display Scaling><DisplayFailed=Yes><pNumberShift=5>
P62:	DC.W	SET6FX+URATE+W,1+S,2,0	;<type=scaled><name=Setpoint 6 Alarm if Increase Ramp Rate Exceeds><link=P61><pNumberShift=5><Relative>
	;********* do not move this P number *********
P63:	DC.W	SET6VR+RDG+W,NUMPER+R,4,60	;<type=scaled><name=Setpoint 6 Reading {><link=P61><DisplayFailed=Yes><local><localLink=P60><access=RW><pNumberShift=5>
P64:	DC.W	SET6VR+CLCSPT+W,1+R,2,60	;<type=scaled><name=Scheduled Setpoint 6><link=P61><DisplayFailed=Yes><pNumberShift=5>
	;setpoint 7
	;********* do not move this P number *********
P65:	DC.W	SET7FX+SETPTS+L,NUMPER+S,4,65	;<type=pointer32><name=Setpoint 7 External Pointer {><access=R><pNumberShift=5>
P66:	DC.W	SET7FX+SCALE+W,1+S,2,0	;<type=scaling><name=Setpoint 7 Display Scaling><DisplayFailed=Yes><pNumberShift=5>
P67:	DC.W	SET7FX+URATE+W,1+S,2,0	;<type=scaled><name=Setpoint 7 Alarm if Increase Ramp Rate Exceeds><link=P66><pNumberShift=5><Relative>
	;********* do not move this P number *********
P68:	DC.W	SET7VR+RDG+W,NUMPER+R,4,65	;<type=scaled><name=Setpoint 7 Reading {><link=P66><DisplayFailed=Yes><local><localLink=P65><access=RW><pNumberShift=5>
P69:	DC.W	SET7VR+CLCSPT+W,1+R,2,65	;<type=scaled><name=Scheduled Setpoint 7><link=P66><DisplayFailed=Yes><pNumberShift=5>
	;setpoint 8
	;********* do not move this P number *********
P70:	DC.W	SET8FX+SETPTS+L,NUMPER+S,4,70	;<type=pointer32><name=Setpoint 8 External Pointer {><access=R><pNumberShift=5>
P71:	DC.W	SET8FX+SCALE+W,1+S,2,0	;<type=scaling><name=Setpoint 8 Display Scaling><DisplayFailed=Yes><pNumberShift=5>
P72:	DC.W	SET8FX+URATE+W,1+S,2,0	;<type=scaled><name=Setpoint 8 Alarm if Increase Ramp Rate Exceeds><link=P71><pNumberShift=5><Relative>
	;********* do not move this P number *********
P73:	DC.W	SET8VR+RDG+W,NUMPER+R,4,70	;<type=scaled><name=Setpoint 8 Reading {><link=P71><DisplayFailed=Yes><local><localLink=P70><access=RW><pNumberShift=5>
P74:	DC.W	SET8VR+CLCSPT+W,1+R,2,70	;<type=scaled><name=Scheduled Setpoint 8><link=P71><DisplayFailed=Yes><pNumberShift=5>
	;Date/Time Periods Settings
P75:	DC.W	PER1FX+EXTPTR+L,NUMPER+S,PERFXL,0	;<type=pointer32><name=Period { Override External Pointer><access=R>
	;********* do not move this P number *********
P76:	DC.W	PER1FX+STIME+W,NUMPER+S,PERFXL,0	;<type=Solar><name=Period { Start Time>
	;********* do not move this P number *********
P77:	DC.W	PER1FX+ETIME+W,NUMPER+S,PERFXL,0	;<type=Solar><name=Period { End Time>
	;********* do not move this P number *********
P78:	DC.W	PER1FX+DOW+B,NUMPER+S,PERFXL,0	;<type=Days of Week><name=Period { Days of the Week>
P79:	DC.W	PER1FX+PERFFL+B,NUMPER+S,PERFXL,0	;<type=label><name=Period { Override External Value Comparison><length=20><mask=1>
					;<states=
					;0,RW,Active when Non-Zero
					;1,RW,Active when Zero
					;>
	;Date/Time Periods Variables
P80:	DC.W	PER1VR+CSTIME+W,NUMPER+R,PERVRL,0	;<type=HHMM><name=Period { Calculated Start Time>
P81:	DC.W	PER1VR+CETIME+W,NUMPER+R,PERVRL,0	;<type=HHMM><name=Period { Calculated End Time>
P82:	DC.W	PER1VR+OVRRDG+B,NUMPER+R,PERVRL,0	;<type=unsigned><name=Period { Override Reading><length=8><pointerLink=P75><IgnoreFiltering>
					;<states=
					;0,R,Zero
					;1 to 255,R,Non-Zero
P83:	DC.W	PER1VR+OVRRDG+1+B,NUMPER+R,PERVRL,0	;<type=unsigned><name=Period { Override Reading Watchdog>
P84:	DC.W	PER1VR+PERVFL+B,NUMPER+R,PERVRL,0	;<type=label><name=Period { Override External Value Status><length=19><mask=3>
					;<states=
					;0,R, 
					;1,R,External Not Active
					;2,R,No External Pointer
					;3,R,External Active
					;>
P85:	DC.W	PER1VR+PERVFL+B,NUMPER+R,PERVRL,0	;<type=label><name=Period { Time Window Status><length=22><mask=4>
					;<states=
					;0,R,Outside of Time Window
					;4,R,Within Time Window
					;>
P86:	DC.W	PER1VR+PERVFL+B,NUMPER+R,PERVRL,0	;<type=label><name=Period { Day of the Week Status><length=15><mask=24>
					;<states=
					;0,R,Inactive
					;8,R,Start Time Only
					;16,R,End Time Only
					;24,R,Active
					;>
P87:	DC.W	PER1VR+PERVFL+B,NUMPER+R,PERVRL,0	;<type=label><name=Period { Status><length=8><mask=32>
					;<states=
					;0,R,Inactive
					;32,R,Active
					;>
	;VARIABLES
P88:	DC.W	PWREAD+L,1+R,4,0	;<type=linked32><link=P21><name=Shutdown Source Reading - NOT USED><pointerLink=P21>
P89:	DC.W	PWREAD+4+B,1+R,1,0	;<type=unsigned><name=Shutdown Source Reading Watchdog - NOT USED>
P90:	DC.W	DEVFLT+B,1+R,1,0	;<type=label><name=Settings CRC Fault><length=15><mask=192><access=RW>
			;<states=
			;0,R,CRC Okay
			;128,RW,Recalculate CRC
			;64,R,CRC Bad!
			;192,R,Recalculating
			;>
P91:	DC.W	DEVVFL+B,1+R,1,0	;VARIABLES FLAGS
			;<type=label><name=Shutdown State - NOT USED><length=9><mask=8>
			;<states=
			;0,R, 
			;8,R,SHUTDOWN!
			;>
			;<type=label><name=Clear Latched Alarms In This Program><length=21><mask=128><DisplayFailed=no><access=RW>
			;<ClearAllLatched>
			;<states=
			;0,R,Clear Latched Alarms
			;128,W,Clearing...
			;>        
P92:	DC.W	STGTMR+B,1+R,1,0	;<type=unsignedN><name=Staging Delay Timer>
P93:	DC.W	0+B,0+S,1,0 ;SPARE
	;
	;********* do not move this P number *********
P94:	DC.W	C_HOUR+W,3+R,2,0	;<type=HHMM><name=Current Time>
			;<shift=1><name=Solar Dawn>
			;<shift=2><name=Solar Dusk>
P95:	DC.W	C_DYOFWK+B,1+R,1,0	;<type=Day Of Week><name=Current Day Of The Week>
P96:	DC.W	C_SECOND+B,1+R,1,0	;<type=unsigned><name=Current Seconds>
P97:	DC.W	BADPER+B,1+R,1,0	;<type=hex8><name=Bad Period Bit Flags>
P98:	DC.W	SETPON+B,1+R,1,0	;<type=label><name=SETPOINT PERIOD ON>
			;<length=22>
			;<LCDLabel=84>
			;<states=
			;0,R,Ramping From Period 1
			;1,R,Ramping From Period 2
			;2,R,Ramping From Period 3
			;3,R,Ramping From Period 4
			;4,R,Ramping From Period 5
			;5,R,Ramping From Period 6
			;6,R,Ramping From Period 7
			;7,R,Ramping From Period 8
			;8,R,In Period 1
			;9,R,In Period 2
			;10,R,In Period 3
			;11,R,In Period 4
			;12,R,In Period 5
			;13,R,In Period 6
			;14,R,In Period 7
			;15,R,In Period 8
			;16 to 254,R,Unknown Value {
			;255,R,Defaulting to Period 1
			;>
P99:	DC.W	ERAMP1+B,8+R,1,0	;<type=label><name=End Of Ramp {><mask=15>
			;<length=13>
			;<states=
			;0,R,No End!?
			;1,R,Period 1
			;2,R,Period 2
			;3,R,Period 3
			;4,R,Period 4
			;5,R,Period 5
			;6,R,Period 6
			;7,R,Period 7
			;8,R,Period 8
			;9 to 15,R,Unknown {
			;>
			;<type=label><name=End Of Ramp { # Days Between>
			;<mask=240><length=13>
			;<states=
			;0,R,Same Day
			;16,R,1 Day
			;32,R,2 Days
			;48,R,3 Days
			;64,R,4 Days
			;80,R,5 Days
			;96,R,6 Days
			;112,R,7 Days
			;113 to 255,R,Unknown {
			;>
P100:	DC.W	PER1VR+ETVDOW+B,NUMPER+R,PERVRL,0
	;<type=Days of Week><name=Period { End Time Days of the Week>
	;<type=label><name=Period { End Time Midnight Wrap Status>
	;><length=1><mask=128>
	;<states=
	;0,RW,  
	;128,RW,Wraps Through Midnight
	;>
P101:	DC.W	SET2VR+RDG+2+B,NUMPER+R,4,0	;<type=unsigned><name=Setpoint 2 Reading Watchdog {>
P102:	DC.W	0+W,0+S,2,0	;SPARE
;P102:	DC.W	SET3FX+SETPTS+L,NUMPER+S,4,0	;<type=pointer32><name=Setpoint 3 External Pointer {><access=R>
P103:	DC.W	SET3VR+RDG+2+B,NUMPER+R,4,0	;<type=unsigned><name=Setpoint 3 Reading Watchdog {>
P104:	DC.W	0+W,0+S,2,0	;SPARE
;P104:	DC.W	SET4FX+SETPTS+L,NUMPER+S,4,0	;<type=pointer32><name=Setpoint 4 External Pointer {><access=R>
P105:	DC.W	SET4VR+RDG+2+B,NUMPER+R,4,0	;<type=unsigned><name=Setpoint 4 Reading Watchdog {>
P106:	DC.W	0+W,0+S,2,0	;SPARE
;P106:	DC.W	SET5FX+SETPTS+L,NUMPER+S,4,0	;<type=pointer32><name=Setpoint 5 External Pointer {><access=R>
P107:	DC.W	SET5VR+RDG+2+B,NUMPER+R,4,0	;<type=unsigned><name=Setpoint 5 Reading Watchdog {>
P108:	DC.W	0+W,0+S,2,0	;SPARE
;P108:	DC.W	SET6FX+SETPTS+L,NUMPER+S,4,0	;<type=pointer32><name=Setpoint 6 External Pointer {><access=R>
P109:	DC.W	SET6VR+RDG+2+B,NUMPER+R,4,0	;<type=unsigned><name=Setpoint 6 Reading Watchdog {>
P110:	DC.W	0+W,0+S,2,0	;SPARE
;P110:	DC.W	SET7FX+SETPTS+L,NUMPER+S,4,0	;<type=pointer32><name=Setpoint 7 External Pointer {><access=R>
P111:	DC.W	SET7VR+RDG+2+B,NUMPER+R,4,0	;<type=unsigned><name=Setpoint 7 Reading Watchdog {>
P112:	DC.W	0+W,0+S,2,0	;SPARE
;P112:	DC.W	SET8FX+SETPTS+L,NUMPER+S,4,0	;<type=pointer32><name=Setpoint 8 External Pointer {><access=R>
P113:	DC.W	SET8VR+RDG+2+B,NUMPER+R,4,0	;<type=unsigned><name=Setpoint 8 Reading Watchdog {>
	;********* do not move this P number *********
P114:	DC.W	PER1FX+PERFFL+B,NUMPER+S,PERFXL,0	;<type=label><name=Period { ><length=8><mask=128>
					;<states=
					;0,RW,Disabled
					;128,RW,Enabled
					;>
P115:	DC.W	PER1VR+PERVFL+B,NUMPER+R,PERVRL,0	;<type=label>
			;<name=Period { Status>
			;<length=19>
			;<mask=226>
			;<states=
			;0 to 2,R,Disabled
			;32 to 34,R,Disabled
			;64 to 66,R,Disabled
			;96 to 98,R,Disabled
			;128,R,External Not Active
			;130,R,Inactive
			;160 to 162,R,Active
			;192 to 194,R,Not Used: Overlap
			;224 to 226,R,Not Used: Overlap
			;>
P116:	DC.W	DSBPFL+B,3+R,1,0	;<type=hex8><name=Disabled Period Bit Flags>
			;<shift=1><name=Period Overlap Alarm>
			;<shift=2><name=Inactive Externals>
P117:	DC.W	SET1FX+SETPTS+B,NUMPER+S,4,0	;<type=unsigned><name=DEBUG: Setpoint 1 Controller# {>
P118:	DC.W	RAMP_END+B,1+R,1,0	;<type=label><name=Period Ramp End>
			;<length=21><mask=15>
			;<states=
			;0,R,
			;1,R,to Period 1
			;2,R,to Period 2
			;3,R,to Period 3
			;4,R,to Period 4
			;5,R,to Period 5
			;6,R,to Period 6
			;7,R,to Period 7
			;8,R,to Period 8
			;9 to 255,R,
			;>
P119:	DC.W	POVALP+B,1+S,1,0 ;<type=Alarm Priority 2><name=Period Overlap Alarm Priority>
P120:	DC.W	SET1FX+RMPALP+B,NUMENT+S,STPFXL,0
	 ;<type=Alarm Priority 2><name=Setpoint { Ramp Rate Alarm Priority>
P121:	DC.W	PERHAL+B,1+R,1,0
	;<type=unsigned><name=Period Overlap Latched Alarms Clear>
	;<access=RW><DisplayFailed=no>
	;<AlarmPriorityLink=P119><AlarmActiveState=0>
	;<length=28>
	;<states=
	;0,R,No Overlap Alarms
	;0,W,Clearing...
	;1 to 255,R,Clear Overlap Latched Alarms
	;>
	;<type=HadOverlapped><mask=1><name=Period 1 Had Overlap>
	;<AlarmPriorityLink=P119><AlarmActiveState=1>
	;<type=HadOverlapped><mask=2><name=Period 2 Had Overlap>
	;<AlarmPriorityLink=P119><AlarmActiveState=2>
	;<type=HadOverlapped><mask=4><name=Period 3 Had Overlap>
	;<AlarmPriorityLink=P119><AlarmActiveState=4>
	;<type=HadOverlapped><mask=8><name=Period 4 Had Overlap>
	;<AlarmPriorityLink=P119><AlarmActiveState=8>
	;<type=HadOverlapped><mask=16><name=Period 5 Had Overlap>
	;<AlarmPriorityLink=P119><AlarmActiveState=16>
	;<type=HadOverlapped><mask=32><name=Period 6 Had Overlap>
	;<AlarmPriorityLink=P119><AlarmActiveState=32>
	;<type=HadOverlapped><mask=64><name=Period 7 Had Overlap>
	;<AlarmPriorityLink=P119><AlarmActiveState=64>
	;<type=HadOverlapped><mask=128><name=Period 8 Had Overlap>
	;<AlarmPriorityLink=P119><AlarmActiveState=128>

P122:	DC.W	SET1VR+RMPHAL+B,NUMENT+R,STPVRL,0
 	;<type=unsigned><name=Setpoint { Ramp Rate Latched Alarms Clear>
 	;<access=RW><DisplayFailed=no>
	;<AlarmPriorityLink=P120><AlarmActiveState=0>
	;<length=22>
	;<states=
	;0,R,No Ramp Rate Alarms
	;0,W,Clearing...
	;1 to 255,R,Clear Ramp Rate Alarms
	;>
P123:	DC.W	SET1VR+RMPHAL+B,NUMENT+R,STPVRL,0
	;<type=label><name=Setpoint { Ramp 1 Latched Alarm><length=6><mask=1>
	;<AlarmPriorityLink=P120><AlarmActiveState=1>
	;<states=
	;0,R,Okay
	;1,R,Fault!
	;>
P124:	DC.W	SET1VR+RMPHAL+B,NUMENT+R,STPVRL,0
	;<type=label><name=Setpoint { Ramp 2 Latched Alarm><length=6><mask=2>
	;<AlarmPriorityLink=P120><AlarmActiveState=2>
	;<states=
	;0,R,Okay
	;2,R,Fault!
	;>
P125:	DC.W	SET1VR+RMPHAL+B,NUMENT+R,STPVRL,0
	;<type=label><name=Setpoint { Ramp 3 Latched Alarm><length=6><mask=4>
	;<AlarmPriorityLink=P120><AlarmActiveState=4>
	;<states=
	;0,R,Okay
	;4,R,Fault!
	;>
P126:	DC.W	SET1VR+RMPHAL+B,NUMENT+R,STPVRL,0
	;<type=label><name=Setpoint { Ramp 4 Latched Alarm><length=6><mask=8>
	;<AlarmPriorityLink=P120><AlarmActiveState=8>
	;<states=
	;0,R,Okay
	;8,R,Fault!
	;>
P127:	DC.W	SET1VR+RMPHAL+B,NUMENT+R,STPVRL,0
	;<type=label><name=Setpoint { Ramp 5 Latched Alarm><length=6><mask=16>
	;<AlarmPriorityLink=P120><AlarmActiveState=16>
	;<states=
	;0,R,Okay
	;16,R,Fault!
	;>
P128:	DC.W	SET1VR+RMPHAL+B,NUMENT+R,STPVRL,0
	;<type=label><name=Setpoint { Ramp 6 Latched Alarm><length=6><mask=32>
	;<AlarmPriorityLink=P120><AlarmActiveState=32>
	;<states=
	;0,R,Okay
	;32,R,Fault!
	;>
P129:	DC.W	SET1VR+RMPHAL+B,NUMENT+R,STPVRL,0
	;<type=label><name=Setpoint { Ramp 7 Latched Alarm><length=6><mask=64>
	;<AlarmPriorityLink=P120><AlarmActiveState=64>
	;<states=
	;0,R,Okay
	;64,R,Fault!
	;>
P130:	DC.W	SET1VR+RMPHAL+B,NUMENT+R,STPVRL,0
	;<type=label><name=Setpoint { Ramp 8 Latched Alarm><length=6><mask=128>
	;<AlarmPriorityLink=P120><AlarmActiveState=128>
	;<states=
	;0,R,Okay
	;128,R,Fault!
	;>
P131:	DC.W	LOCAL_ALARMS_BUILT+L,1+R,4,0	;<type=alarm><name=Local Alarm Priorities><programAlarmDWord>
					;<type=hex32><name=Local Alarm Priorities>

P132:	DC.W	REQTIM+0+B,NUMPER+S,1,0 ;<type=unsigned><initial=30>
	;<name=Cross Module Reading Request Interval: Setpoint 1 Period { Override Reading><pNumberShift=1>
P133:	DC.W	REQTIM+NUMPER+B,NUMPER+S,1,0 ;<type=unsigned><initial=30>
	;<name=Cross Module Reading Request Interval: Setpoint 2 Period { Override Reading><pNumberShift=1>
P134:	DC.W	REQTIM+(NUMPER*2)+B,NUMPER+S,1,0 ;<type=unsigned><initial=30>
	;<name=Cross Module Reading Request Interval: Setpoint 3 Period { Override Reading><pNumberShift=1>
P135:	DC.W	REQTIM+(NUMPER*3)+B,NUMPER+S,1,0 ;<type=unsigned><initial=30>
	;<name=Cross Module Reading Request Interval: Setpoint 4 Period { Override Reading><pNumberShift=1>
P136:	DC.W	REQTIM+(NUMPER*4)+B,NUMPER+S,1,0 ;<type=unsigned><initial=30>
	;<name=Cross Module Reading Request Interval: Setpoint 5 Period { Override Reading><pNumberShift=1>
P137:	DC.W	REQTIM+(NUMPER*5)+B,NUMPER+S,1,0 ;<type=unsigned><initial=30>
	;<name=Cross Module Reading Request Interval: Setpoint 6 Period { Override Reading><pNumberShift=1>
P138:	DC.W	REQTIM+(NUMPER*6)+B,NUMPER+S,1,0 ;<type=unsigned><initial=30>
	;<name=Cross Module Reading Request Interval: Setpoint 7 Period { Override Reading><pNumberShift=1>
P139:	DC.W	REQTIM+(NUMPER*7)+B,NUMPER+S,1,0 ;<type=unsigned><initial=30>
	;<name=Cross Module Reading Request Interval: Setpoint 8 Period { Override Reading><pNumberShift=1>
P140:	DC.W	REQTIM+(NUMENT*NUMPER)+B,NUMPER+S,1,0 ;<type=unsigned><initial=30>
	;<name=Cross Module Reading Request Interval: Period { Override Reading>
P141:	DC.W	REQTMR+0+B,NUMPER+R,1,0 ;<type=unsigned><DisplayFailed=no>
	;<name=Cross Module Reading Request Timer: Setpoint 1 Period { Override Reading><pNumberShift=1>
P142:	DC.W	REQTMR+NUMPER+B,NUMPER+R,1,0 ;<type=unsigned><DisplayFailed=no>
	;<name=Cross Module Reading Request Timer: Setpoint 2 Period { Override Reading><pNumberShift=1>
P143:	DC.W	REQTMR+(NUMPER*2)+B,NUMPER+R,1,0 ;<type=unsigned><DisplayFailed=no>
	;<name=Cross Module Reading Request Timer: Setpoint 3 Period { Override Reading><pNumberShift=1>
P144:	DC.W	REQTMR+(NUMPER*3)+B,NUMPER+R,1,0 ;<type=unsigned><DisplayFailed=no>
	;<name=Cross Module Reading Request Timer: Setpoint 4 Period { Override Reading><pNumberShift=1>
P145:	DC.W	REQTMR+(NUMPER*4)+B,NUMPER+R,1,0 ;<type=unsigned><DisplayFailed=no>
	;<name=Cross Module Reading Request Timer: Setpoint 5 Period { Override Reading><pNumberShift=1>
P146:	DC.W	REQTMR+(NUMPER*5)+B,NUMPER+R,1,0 ;<type=unsigned><DisplayFailed=no>
	;<name=Cross Module Reading Request Timer: Setpoint 6 Period { Override Reading><pNumberShift=1>
P147:	DC.W	REQTMR+(NUMPER*6)+B,NUMPER+R,1,0 ;<type=unsigned><DisplayFailed=no>
	;<name=Cross Module Reading Request Timer: Setpoint 7 Period { Override Reading><pNumberShift=1>
P148:	DC.W	REQTMR+(NUMPER*7)+B,NUMPER+R,1,0 ;<type=unsigned><DisplayFailed=no>
	;<name=Cross Module Reading Request Timer: Setpoint 8 Period { Override Reading><pNumberShift=1>
P149:	DC.W	REQTMR+(NUMENT*NUMPER)+B,NUMPER+R,1,0 ;<type=unsigned><DisplayFailed=no>
	;<name=Cross Module Reading Request Timer: Period { Override Reading>
P150:	DC.W	SET1VR+RDG+2+B,NUMPER+R,4,0
	;<type=unsigned><name=Setpoint 1 Reading Watchdog {><pNumberShift=1>
P151:	DC.W	SET2VR+RDG+2+B,NUMPER+R,4,0
	;<type=unsigned><name=Setpoint 2 Reading Watchdog {><pNumberShift=1>
P152:	DC.W	SET3VR+RDG+2+B,NUMPER+R,4,0
	;<type=unsigned><name=Setpoint 3 Reading Watchdog {><pNumberShift=1>
P153:	DC.W	SET4VR+RDG+2+B,NUMPER+R,4,0
	;<type=unsigned><name=Setpoint 4 Reading Watchdog {><pNumberShift=1>
P154:	DC.W	SET5VR+RDG+2+B,NUMPER+R,4,0
	;<type=unsigned><name=Setpoint 5 Reading Watchdog {><pNumberShift=1>
P155:	DC.W	SET6VR+RDG+2+B,NUMPER+R,4,0
	;<type=unsigned><name=Setpoint 6 Reading Watchdog {><pNumberShift=1>
P156:	DC.W	SET7VR+RDG+2+B,NUMPER+R,4,0
	;<type=unsigned><name=Setpoint 7 Reading Watchdog {><pNumberShift=1>
P157:	DC.W	SET8VR+RDG+2+B,NUMPER+R,4,0
	;<type=unsigned><name=Setpoint 8 Reading Watchdog {><pNumberShift=1>
;P158:	DC.W	SET1FX+DRATE+W,NUMENT+S,STPFXL,0	;<type=scaled><name=Setpoint { Maximum Alarm if Decrease Ramp Rate Exceeds><link=P36>
;P159:	DC.W	SET1VR+CRMPRT+W,NUMENT+R,STPVRL,0	;<type=scaled><name=Setpoint { Current Ramp Rate><link=P36><DisplayFailed=Yes>
P158:	DC.W	SET1FX+DRATE+W,1+S,2,0	;<type=scaled><name=Setpoint 1 Maximum Alarm if Decrease Ramp Rate Exceeds><link=P36><pNumberShift=5><Relative>
P159:	DC.W	SET1VR+CRMPRT+W,1+R,2,0	;<type=scaled><name=Setpoint 1 Current Ramp Rate><link=P36><DisplayFailed=Yes><pNumberShift=5><Relative>
P160	DC.W	0+B,0+S,1,0 ;SPARE
P161:	DC.W	0+B,0+S,1,0 ;SPARE
P162:	DC.W	0+B,0+S,1,0 ;SPARE

P163:	DC.W	SET2FX+DRATE+W,1+S,2,0	;<type=scaled><name=Setpoint 2 Maximum Alarm if Decrease Ramp Rate Exceeds><link=P41><pNumberShift=5><Relative>
P164:	DC.W	SET2VR+CRMPRT+W,1+R,2,0	;<type=scaled><name=Setpoint 2 Current Ramp Rate><link=P41><DisplayFailed=Yes><pNumberShift=5><Relative>
P165	DC.W	0+B,0+S,1,0 ;SPARE
P166:	DC.W	0+B,0+S,1,0 ;SPARE
P167:	DC.W	0+B,0+S,1,0 ;SPARE

P168:	DC.W	SET3FX+DRATE+W,1+S,2,0	;<type=scaled><name=Setpoint 3 Maximum Alarm if Decrease Ramp Rate Exceeds><link=P46><pNumberShift=5><Relative>
P169:	DC.W	SET3VR+CRMPRT+W,1+R,2,0	;<type=scaled><name=Setpoint 3 Current Ramp Rate><link=P46><DisplayFailed=Yes><pNumberShift=5><Relative>
P170	DC.W	0+B,0+S,1,0 ;SPARE
P171:	DC.W	0+B,0+S,1,0 ;SPARE
P172:	DC.W	0+B,0+S,1,0 ;SPARE

P173:	DC.W	SET4FX+DRATE+W,1+S,2,0	;<type=scaled><name=Setpoint 4 Maximum Alarm if Decrease Ramp Rate Exceeds><link=P51><pNumberShift=5><Relative>
P174:	DC.W	SET4VR+CRMPRT+W,1+R,2,0	;<type=scaled><name=Setpoint 4 Current Ramp Rate><link=P51><DisplayFailed=Yes><pNumberShift=5><Relative>
P175	DC.W	0+B,0+S,1,0 ;SPARE
P176:	DC.W	0+B,0+S,1,0 ;SPARE
P177:	DC.W	0+B,0+S,1,0 ;SPARE

P178:	DC.W	SET5FX+DRATE+W,1+S,2,0	;<type=scaled><name=Setpoint 5 Maximum Alarm if Decrease Ramp Rate Exceeds><link=P56><pNumberShift=5><Relative>
P179:	DC.W	SET5VR+CRMPRT+W,1+R,2,0	;<type=scaled><name=Setpoint 5 Current Ramp Rate><link=P56><DisplayFailed=Yes><pNumberShift=5><Relative>
P180	DC.W	0+B,0+S,1,0 ;SPARE
P181:	DC.W	0+B,0+S,1,0 ;SPARE
P182:	DC.W	0+B,0+S,1,0 ;SPARE

P183:	DC.W	SET6FX+DRATE+W,1+S,2,0	;<type=scaled><name=Setpoint 6 Maximum Alarm if Decrease Ramp Rate Exceeds><link=P61><pNumberShift=5><Relative>
P184:	DC.W	SET6VR+CRMPRT+W,1+R,2,0	;<type=scaled><name=Setpoint 6 Current Ramp Rate><link=P61><DisplayFailed=Yes><pNumberShift=5><Relative>
P185	DC.W	InPeriodsOrToRamp+B,NUMPER+R,1,0
	;<type=unsigned><name=In or Ramping to Period { Flag>
P186:	DC.W	InPeriodsOrFromRamp+B,NUMPER+R,1,0
	;<type=unsigned><name=In or Ramping from Period { Flag>
P187:	DC.W	InPeriodsOrToFromRamp+B,NUMPER+R,1,0
	;<type=unsigned><name=In or Ramping to or from Period { Flag>

P188:	DC.W	SET7FX+DRATE+W,1+S,2,0	;<type=scaled><name=Setpoint 7 Maximum Alarm if Decrease Ramp Rate Exceeds><link=P66><pNumberShift=5><Relative>
P189:	DC.W	SET7VR+CRMPRT+W,1+R,2,0	;<type=scaled><name=Setpoint 7 Current Ramp Rate><link=P66><DisplayFailed=Yes><pNumberShift=5><Relative>
P190	DC.W	InPeriods+B,NUMPER+R,1,0
	;<type=hex8><name=In Period { Flag>
P191:	DC.W	ToRamps+B,NUMPER+R,1,0
	;<type=hex8><name=Ramping to Period { Flag>
P192:	DC.W	FromRamps+B,NUMPER+R,1,0
	;<type=hex8><name=Ramping from Period { Flag>

P193:	DC.W	SET8FX+DRATE+W,1+S,2,0	;<type=scaled><name=Setpoint 8 Maximum Alarm if Decrease Ramp Rate Exceeds><link=P71><pNumberShift=5><Relative>
P194:	DC.W	SET8VR+CRMPRT+W,1+R,2,0	;<type=scaled><name=Setpoint 8 Current Ramp Rate><link=P71><DisplayFailed=Yes><pNumberShift=5><Relative>

PAR_END:


	;ON ENTRY:
	;'A6' POINTS TO THE WORKING TABLE
	;'A2' POINTS TO THE DEVICE SETTINGS
	;
	;REGISTER USAGE:
	;'A4' USUALLY POINTS TO A SUB-TABLE IN THE DEVICE SETTINGS
	;'A3' USUALLY POINTS TO A SUB-TABLE IN THE DEVICE VARIABLES



;######
	;THIS ROUTINE CALCULATES THE CURRENT SETPOINTS
	;CALLED EVERY 100MS
	;CHECK IF THE MEMORY BLOCK'S CRC VALUE IS OKAY.
	;IF IT IS BAD THEN SET ALL PRODUCED VALUES TO FAILED
	;AND THEN EXIT.
MAIN:	CLR.L	LOCAL_ALARMS_BUILDING(A2)	;INITIALIZE

	check_recalc_CRC

	;GET THE EXTERNALLY REFERENCE VALUES
	LEA	SETPXR,A4	;READINGS LIST
	LEA	REQTMR(A2),A1	;REQUEST TIMERS
	JSR	GETRDS
	;
	;TIME VALUES ARE NOW EXTERNALLY REFERENCED
	MOVE.L	TIME_P(A2),D1	;TIME POINTER
	JSR	GET_TIME_POINTER
	MOVE.L	A0,TIME_A(A2)	;ABSOLUTE ADDRESS
	MOVE.W	HOUR(A0),C_HOUR(A2)	;DISPLAY FOR OPERATOR
	MOVE.W	DAWN(A0),C_DAWN(A2)
	MOVE.W	DUSK(A0),C_DUSK(A2)
	MOVE.B	DYOFWK(A0),C_DYOFWK(A2)
	MOVE.B	SECOND(A0),C_SECOND(A2)
	;
	BSR.W	PERIOD	;Calculate The Time Period Values
	BSR.W	DETPER	;Determine the Current Period or Ramp
	BSR.W	SETPTC	;Calculate the Setpoints
	BSR.W	PeriodStatus	;generate status 8-bit 0 or 100 values

;*** 20130214
;	TST.B	STIMER(A6)	;FULLY POWERED UP?
;	BNE.B	ALARM_EXIT	;JUST POWERING UP
;***
	;

;*** 20140128 - don't alarm if only 1 period enabled
	MOVEQ	#0,D1
	MOVE.B	EnabledPeriodsCount(A2),D1
	MOVEQ	#1,D0
	CMP.L	D0,D1	;7 (or 8) disabled periods?
	BLS.B	_SinglePeriod	;only 1 (or 0) enabled... clear ramp alarms
;***

	MOVE.B	DSBPFL(A2),D0	;GET DISABLED PERIODS
	MOVE.B	PERHAL(A2),D1
	NOT.L	D0
	AND.L	D0,D1
	MOVE.B	D1,PERHAL(A2)	;CLEAR DISABLED PERIODS HAD ALARMS

;*** 20130214 - It takes extra time to initialize the overlap & ramp alarms
	TST.B	oSTIMER(A6)	;FULLY POWERED UP?
	BNE.B	PoweringUp	;JUST POWERING UP
	MOVEQ	#0,D1
	MOVE.B	STGTMR(A2),D1
	BEQ.B	_OkayToAlarm
	SUBQ.L	#1,D1
	BRA.B	DelayAfterPoweringUp

_OkayToAlarm:
;***
	;
	MOVE.B	BADPER(A2),D7	;GET INVALID PERIODS
	AND.L	D7,D0
	MOVE.B	InactiveExternals(A2),D7
	NOT.L	D7
	AND.L	D7,D0
	MOVE.B	D0,PERALM(A2)	;OVERLAPPED PERIODS
	BEQ.B	NO_PERIOD_OVERLAPS
	;AT LEAST ONE PERIOD OVERLAPS ANOTHER
	MOVE.B	PERHAL(A2),D1
	OR.L	D0,D1
	MOVE.B	D1,PERHAL(A2)	;RECORD THE HAD ALARMS
	;RING THE ALARM!
	MOVE.B	POVALP(A2),D0	;GET THE ALARM PRIOIRITY
	ANDI.L	#Alarm_Priority_Mask,D0
	BNE.B	SET_ALARM
	CLR.B	PERHAL(A2)	;NO ALARMS USED
NO_PERIOD_OVERLAPS:
	TST.B	PERHAL(A2)	;CHECK FOR HAD ALARMS
	BNE.B	LATCHED_ALARM
ALARM_EXIT:
	;NOW UPDATE THE CONTROLLERS OVERALL ALARMS
	update_controller_alarms
	RTS	

;*** 20130214
PoweringUp:
	MOVEQ	#26,D1
DelayAfterPoweringUp:
	MOVE.B	D1,STGTMR(A2)
_SinglePeriod:
	CLR.B	PERALM(A2)	;OVERLAPPED PERIODS
;*** 20130214
;	CLR.B	RMPALM(A3)	;RAMP ALARMS
;***
	BRA.B	ALARM_EXIT	;JUST POWERING UP
;***


LATCHED_ALARM:
	MOVEQ	#Had_An_Alarm_Priority,D0
SET_ALARM:
	MOVE.L	LOCAL_ALARMS_BUILDING(A2),D2
	BSET.L	D0,D2	;SET THE ALARM'S PRIORITY
	MOVE.L	D2,LOCAL_ALARMS_BUILDING(A2)
	BRA.B	ALARM_EXIT


;######
	;Breakout the possible period values as 8-bit 0 or 100 values
	;There are six types broken out:
	;1)    In Period 1
	;2)    In Period 1 or Ramping From Period 1
	;3)    In Period 1 or Ramping to Period 1
	;4)    In Period 1 or Ramping to Period 1 or Ramping from Period 1
	;5)    Ramping to Period 1
	;6)    Ramping from Period 1
PeriodStatus:
	MOVEQ	#0,D2	;In Period
	MOVEQ	#0,D3	;Ramping to Period
	MOVEQ	#0,D4	;Ramping from Period
	MOVEQ	#0,D0
	MOVE.B	RAMP_END(A2),D0
	BEQ.B	NotRampingTo
	SUBQ.L	#1,D0
	CMPI.L	#NUMPER,D0	;0-7 are ramps
	BHS.B	NotRampingTo
	BSET.L	D0,D3
NotRampingTo:
	MOVEQ	#0,D0
	MOVE.B	SETPON(A2),D0
	CMPI.L	#NUMPER,D0	;0-7 are ramps
	BLO.B	InRamps
	SUBI.L	#NUMPER,D0
	CMPI.L	#NUMPER,D0	;0-7 are periods
	BLO.B	InPeriod
	CMPI.L	#254,D0
	BNE.B	GotRampsPeriods
	BSET.L	#0,D2	;Force period 1
	BRA.B	GotRampsPeriods
InRamps:
	BSET.L	D0,D4
	BRA.B	GotRampsPeriods
InPeriod:
	BSET.L	D0,D2
GotRampsPeriods:
	MOVEQ	#100,D1
	MOVEQ	#NUMPER,D5

	LEA	InPeriods(A2),A0
	MOVE.L	D2,D0
	BSR.B	Single

	LEA	ToRamps(A2),A0
	MOVE.L	D3,D0
	BSR.B	Single

	LEA	FromRamps(A2),A0
	MOVE.L	D4,D0
	BSR.B	Single

	LEA	InPeriodsOrToRamp(A2),A0
	MOVE.L	D3,D0
	BSR.B	Double

	LEA	InPeriodsOrFromRamp(A2),A0
	MOVE.L	D4,D0
	BSR.B	Double
	LEA	InPeriodsOrToFromRamp(A2),A0

Triple:
	MOVEQ	#0,D6
TripleLoop:
	BTST.L	D6,D2	;In Period?
	BNE.B	IsActiveT
	BTST.L	D6,D3	;To Ramp?
	BNE.B	IsActiveT
	BTST.L	D6,D4	;From Ramp?
	BNE.B	IsActiveT
	CLR.B	0(A0,D6.L*1)
TripleCommon:
	ADDQ.L	#1,D6
	CMP.L	D5,D6
	BLO.B	TripleLoop	
	RTS
IsActiveT:
	MOVE.B	D1,0(A0,D6.L*1)
	BRA.B	TripleCommon	

Single:
	MOVEQ	#0,D6
SingleLoop:
	BTST.L	D6,D0
	BNE.B	IsActive
	CLR.B	0(A0,D6.L*1)
SingleCommon:
	ADDQ.L	#1,D6
	CMP.L	D5,D6
	BLO.B	SingleLoop	
	RTS
IsActive:
	MOVE.B	D1,0(A0,D6.L*1)
	BRA.B	SingleCommon	


Double:
	MOVEQ	#0,D6
DoubleLoop:
	BTST.L	D6,D2	;In Period?
	BNE.B	IsActiveD
	BTST.L	D6,D0	;In Ramp?
	BNE.B	IsActiveD
	CLR.B	0(A0,D6.L*1)
DoubleCommon:
	ADDQ.L	#1,D6
	CMP.L	D5,D6
	BLO.B	DoubleLoop	
	RTS
IsActiveD:
	MOVE.B	D1,0(A0,D6.L*1)
	BRA.B	DoubleCommon	


;########
	;THESE ARE THE TIME PERIOD CALCULATIONS
PERIOD:
;* JAN 13,2003
	BSR.W	PER1
	BSR.W	PER2
	BSR.W	PER3
	BSR.W	PER4
	BSR.W	PER5
	BSR.W	PER6
	BSR.W	PER7
	BRA.W	PER8

PER1:	LEA	PER1FX(A2),A4
	LEA	PER1VR(A2),A3
	BRA.B	PERCLC

PER2:	LEA	PER2FX(A2),A4
	LEA	PER2VR(A2),A3
	BRA.B	PERCLC

PER3:	LEA	PER3FX(A2),A4
	LEA	PER3VR(A2),A3
	BRA.B	PERCLC

PER4:	LEA	PER4FX(A2),A4
	LEA	PER4VR(A2),A3
	BRA.B	PERCLC

PER5:	LEA	PER5FX(A2),A4
	LEA	PER5VR(A2),A3
	BRA.B	PERCLC

PER6:	LEA	PER6FX(A2),A4
	LEA	PER6VR(A2),A3
	BRA.B	PERCLC

PER7:	LEA	PER7FX(A2),A4
	LEA	PER7VR(A2),A3
	BRA.B	PERCLC

PER8:	LEA	PER8FX(A2),A4
	LEA	PER8VR(A2),A3


;######
	;CALCULATE ALL THE TIME PERIOD VALUES
	;ON ENTRY:
	;'A4' POINTS TO THE PERIOD TABLE SETTINGS
	;'A3' POINTS TO THE PERIOD TABLE VARIABLES
	;'A2' POINTS TO THE DEVICE PROGRAM MEMORY
PERCLC:	MOVE.L	EXTPTR(A4),D0	;HAS AN EXTERNAL POINTER?
	CMPI.L	#BLANK_POINTER,D0
	BNE.B	HASX_PTR	;HAS A POINTER
	;NO POINTER ASSIGNED
	BCLR.B	#HASPTR,PERVFL(A3)	;NO POINTER
	BRA.B	EXT_ACT
HASX_PTR:
	BSET.B	#HASPTR,PERVFL(A3)	;ACTIVE
	BTST.B	#ACZERO,PERFFL(A4)	;ACTIVE WHEN ZERO
	BNE.B	ACTIVE_ZERO
	;ACTIVE WHEN OVERRIDE VALUE IS NON-ZERO
	TST.B	OVRRDG(A3)
	BNE.B	EXT_ACT
EXT_NOT_ACT:
	BCLR.B	#EXTACT,PERVFL(A3)	;NOT ACTIVE
	BRA.B	CALC_TIMES
	;
	;ACTIVE WHEN OVERRIDE VALUE IS ZERO
ACTIVE_ZERO:
	TST.B	OVRRDG(A3)
	BNE.B	EXT_NOT_ACT
	;IS ACTIVE FOR THE EXTERNAL POINTER
EXT_ACT:
	BSET.B	#EXTACT,PERVFL(A3)	;ACTIVE

CALC_TIMES:
	;CALCULATE TIME VALUES	
	MOVEA.L	TIME_A(A2),A0	;CLOCK VALUES POINTER
	MOVE.W	STIME(A4),D1	;GET START TIME
	JSR	SOLROF_A0	;CALCULATE TIME
	MOVE.W	D1,CSTIME(A3)	;CALCULATED TIME
	MOVE.W	ETIME(A4),D1	;TURN OFF TIME
	JSR	SOLROF_A0	;CALCULATE TIME
	MOVE.W	D1,CETIME(A3)	;CALCULATED TIME
	MOVEQ	#0,D0
	MOVE.W	CSTIME(A3),D0
	CMP.L	D1,D0
	BLS.W	NO_WRAP
	;THE TIME VALUES WRAP THROUGH MIDNIGHT
	;
	;BUILD THE DAYS OF THE WEEK THAT THE
	;END TIMES ARE VALID FOR
	MOVE.B	DOW(A4),D0	;GET DAYS OF WEEK
	LSL.L	#1,D0	;SHIFT UP
	BTST.L	#7,D0	;ONLY 7 DAYS/WEEK
	BEQ.B	_ISZER
	ADDQ.L	#1,D0	;ROTATE THE MSB TO LSB
_ISZER:	BSET.L	#Wraps_Through_Midnight,D0 ;WRAPS THROUGH MIDNIGHT
	MOVE.B	D0,ETVDOW(A3)	;END TIME VALID DAYS OF THE WEEK
	;	
	;CHECK IF ACTIVE FOR THIS DAY OF THE WEEK
	MOVE.B	C_DYOFWK(A2),D0
	ANDI.L	#DYOFWK_MASK,D0	;IGNORE THE OTHER BITS
	BTST.B	D0,DOW(A4)
	BEQ.B	CHECK_END_TIME
	;PERIOD STARTS TODAY BETWEEN CSTIME AND MIDNIGHT
	BSET.B	#ACTSTM,PERVFL(A3)	;ACTIVE
	MOVEQ	#0,D3
	MOVE.W	CSTIME(A3),D3	;CALCULATED START TIME
	MOVEQ	#0,D5
	MOVE.W	C_HOUR(A2),D1	;MINUTE & HOUR
	JSR	TIMPER	;DETERMINE IF BETWEEN
	BCC.B	CHECK_END_TIME_2	;IS OUTSIDE THE TIME PERIOD
	BCLR.B	#ACTETM,PERVFL(A3)	;END TIME NOT VALID YET
	BRA.W	INTIMP	;IN THE TIME PERIOD    

	;CHECK IF THE END TIME IS VALID
CHECK_END_TIME:	
	BCLR.B	#ACTSTM,PERVFL(A3)	;START TIME NOT VALID
CHECK_END_TIME_2:
	BTST.B	D0,ETVDOW(A3)	;CHECK IF END TIME IS VALID
	BEQ.B	NOTDAY
	;PERIOD STARTS TODAY BETWEEN MIDNIGHT AND CETIME
	BSET.B	#ACTETM,PERVFL(A3)	;ACTIVE
	MOVEQ	#0,D3	;MIDNIGHT
	MOVEQ	#0,D5
	MOVE.W	CETIME(A3),D5	;CALCULATED END TIME
;### June 18,2007
	BEQ.W	OUTIMP	;OUTSIDE TIME PERIOD    
;###
	MOVE.W	C_HOUR(A2),D1	;MINUTE & HOUR
	JSR	TIMPER	;DETERMINE IF BETWEEN
	BCC.W	OUTIMP	;OUTSIDE TIME PERIOD    
	BRA.W	INTIMP	;IN THE TIME PERIOD    

NOTDAY:	BCLR.B	#ACTETM,PERVFL(A3)	;NOT ACTIVE
	BRA.B	OUTIMP	;IS OUTSIDE THE TIME PERIOD

NO_WRAP:
	MOVEQ	#0,D3
	MOVEQ	#0,D5
	MOVE.W	CSTIME(A3),D3	;CALCULATED START TIME
	MOVE.W	CETIME(A3),D5	;CALCULATED END TIME
	;CHECK IF ACTIVE FOR THIS DAY OF THE WEEK
	MOVE.B	DOW(A4),D0	;GET DAYS OF WEEK
	BCLR.L	#Wraps_Through_Midnight,D0
	MOVE.B	D0,ETVDOW(A3)	;END TIMES VALID ON THESE DAYS
	MOVE.B	C_DYOFWK(A2),D1
	ANDI.L	#DYOFWK_MASK,D1	;IGNORE THE OTHER BITS
	BTST.L	D1,D0
	BEQ.B	BAD_DAY	;NOT ON DAY OF WEEK
	;IS ACTIVE FOR THIS DAY OF THE WEEK

	ADDQ.L	#1,D1
	CMPI.L	#7,D0
	BLO.B	NoDayWrap	;0 TO 6 ONLY
	MOVEQ	#0,D0	;WRAP AROUND TO MONDAY
NoDayWrap:
	BTST.L	D1,D0
	BNE.B	StartTimeActive
	MOVE.W	C_HOUR(A2),D1	;MINUTE & HOUR
	CMP.L	D3,D1	;PAST START TIME?
	BLS.B	StartTimeActive
	;PAST START TIME
	BCLR.B	#ACTSTM,PERVFL(A3)	;NOT ACTIVE
	BRA.B	CheckIfEndTimeActive

StartTimeActive:
	BSET.B	#ACTSTM,PERVFL(A3)	;ACTIVE
	;END TIME BECOMES VALID AFTER PERIOD START
CheckIfEndTimeActive:
	MOVE.W	C_HOUR(A2),D1	;MINUTE & HOUR
	CMP.L	D3,D1	;PAST START TIME?
	BLO.B	NotEndYet
	BSET.B	#ACTETM,PERVFL(A3)
	BRA.B	CHECK_TIME
BAD_DAY:
	BCLR.B	#ACTSTM,PERVFL(A3)	;NOT ACTIVE
NotEndYet:
	BCLR.B	#ACTETM,PERVFL(A3)

CHECK_TIME:
	;CHECK IF BETWEEN START AND END TIMES
	CMP.L	D3,D5
	BEQ.B	INTIMP	;MATCH DEFAULT VALID
	MOVE.W	C_HOUR(A2),D1	;MINUTE & HOUR
	JSR	TIMPER	;DETERMINE IF BETWEEN
	BCS.B	INTIMP	;OUTSIDE
	;IS OUTSIDE THE TIME PERIOD
OUTIMP:	BCLR.B	#INTIMW,PERVFL(A3)
NOTACT:	BCLR.B	#ACTIVE,PERVFL(A3)	;PERIOD IS NOT ACTIVE
_CHKEB:	BTST.B	#ENBPER,PERFFL(A4)	;IS THIS PERIOD ENABLED?
	BNE.B	_ISENB
	BCLR.B	#ENBPER,PERVFL(A3)	;PERIOD IS NOT ENABLED
	RTS
_ISENB:	BSET.B	#ENBPER,PERVFL(A3)	;PERIOD IS ENABLED
	RTS
	;
	;WITHIN THE TIME PERIOD
INTIMP:	BSET.B	#INTIMW,PERVFL(A3)
	;
	;NOW CHECK IF THE PERIOD IS ACTIVE
	BTST.B	#ENBPER,PERFFL(A4)	;IS THIS PERIOD ENABLED?
	BEQ.B	NOTACT
	BTST.B	#EXTACT,PERVFL(A3)
	BEQ.B	NOTACT	;EXTERNAL READING NOT ACTIVE
	BTST.B	#PEROVR,PERVFL(A3)	;PERIODS OVERLAP?
	BNE.B	NOTACT	;IGNORE OVERLAPPED PERIODS
	BTST.B	#ACTSTM,PERVFL(A3)	;START TIME ACTIVE?
	BNE.B	IS_ACT	;ON A VALID DAY OF THE WEEK
	BTST.B	#ACTETM,PERVFL(A3)	;END TIME ACTIVE?
	BEQ.B	NOTACT	;NOT ON A VALID DAY OF THE WEEK
	;THIS TIME PERIOD IS ACTIVE
IS_ACT:	BSET.B	#ACTIVE,PERVFL(A3)	;TIME PERIOD IS ACTIVE
	BRA.B	_CHKEB


;####
	;DETERMINE THE TIME PERIOD IN WHICH WE CURRENTLY
	;ARE IN.
DETPER:	MOVEQ	#0,D1
	MOVEQ	#0,D2
	MOVEQ	#0,D3
	MOVEQ	#0,D5
	MOVE.B	DSBPFL(A2),D0
	MOVE.B	BADPER(A2),D7
	OR.L	D0,D7	;UNUSED OR INVALID PERIODS

	BSR.W	CHP1OV	;CHECK PERIOD 1 OVERLAPS
	BSR.W	CHP2OV	;CHECK PERIOD 2 OVERLAPS
	BSR.W	CHP3OV	;CHECK PERIOD 3 OVERLAPS
	BSR.W	CHP4OV	;CHECK PERIOD 4 OVERLAPS
	BSR.W	CHP5OV	;CHECK PERIOD 5 OVERLAPS
	BSR.W	CHP6OV	;CHECK PERIOD 6 OVERLAPS
	BSR.W	CHP7OV	;CHECK PERIOD 7 OVERLAPS
	BSR.W	CHP8OV	;CHECK PERIOD 8 OVERLAPS
	BSR.W	CHRMP1	;CALCULATE AND CHECK RAMP 1
	BSR.W	CHRMP2	;CALCULATE AND CHECK RAMP 2
	BSR.W	CHRMP3	;CALCULATE AND CHECK RAMP 3
	BSR.W	CHRMP4	;CALCULATE AND CHECK RAMP 4
	BSR.W	CHRMP5	;CALCULATE AND CHECK RAMP 5
	BSR.W	CHRMP6	;CALCULATE AND CHECK RAMP 6
	BSR.W	CHRMP7	;CALCULATE AND CHECK RAMP 7
	BSR.W	CHRMP8	;CALCULATE AND CHECK RAMP 8
	BRA.W	SECTIN	;SECTION IN


;###
CHP1OV:	BCLR.B	#PEROVR,PER1VR+PERVFL(A2) ;PERIOD 1 CAN NEVER OVERLAP

	BTST.B	#P1IVAL,DSBPFL(A2)	;PERIOD ENABLED?
	BNE.B	PER1IV
	BTST.B	#EXTACT,PER1VR+PERVFL(A2)	;EXTERNAL ACTIVE?
	BEQ.B	PER1IV
	BCLR.L	#P1IVAL,D7	;ASSUME PERIOD 1 VALID
	BRA.W	DONCHK
	;
PER1IV:	BSET.L	#P1IVAL,D7	;PERIOD 1 INVALID
	BRA.W	DONCHK

;###
	;CHECK IF PERIOD 2 OVERLAPPED BY PERIOD 1
CHP2OV:	BCLR.L	#P2IVAL,D7	;ASSUME NO OVERLAP
	BTST.B	#P2IVAL,DSBPFL(A2)	;PERIOD ENABLED?
	BNE.B	PER2IV
	BTST.B	#EXTACT,PER2VR+PERVFL(A2)	;EXTERNAL ACTIVE?
	BEQ.B	PER2XI
	LEA	PER2FX(A2),A1
	LEA	PER2VR(A2),A0
	BSR.W	_PER1	;CHECK PERIOD 1
	BCC.B	P2NTOV
PER2IV:	BSET.B	#PEROVR,PER2VR+PERVFL(A2)
	BSET.L	#P2IVAL,D7	;PERIOD 2 INVALID
	BRA.W	DONCHK
	;
P2NTOV:	BCLR.L	#P2IVAL,D7	;ASSUME PERIOD 1 VALID
	BCLR.B	#PEROVR,PER2VR+PERVFL(A2)
	BRA.W	DONCHK
	;
PER2XI:	BSET.L	#P2IVAL,D7	;PERIOD 2 INVALID
	BCLR.B	#PEROVR,PER2VR+PERVFL(A2)
	BRA.W	DONCHK

;###
	;CHECK IF PERIOD 3 OVERLAPS PERIODS 1 OR 2
CHP3OV:	BCLR.L	#P3IVAL,D7	;ASSUME NO OVERLAP
	BTST.B	#P3IVAL,DSBPFL(A2)	;PERIOD ENABLED?
	BNE.B	PER3IV
	BTST.B	#EXTACT,PER3VR+PERVFL(A2)	;EXTERNAL ACTIVE?
	BEQ.B	PER3XI
	LEA	PER3FX(A2),A1
	LEA	PER3VR(A2),A0
	BSR.W	_PER2	;CHECK PERIODS 2,1
	BCC.B	P3NTOV
PER3IV:	BSET.B	#PEROVR,PER3VR+PERVFL(A2)
	BSET.L	#P3IVAL,D7	;PERIOD 3 INVALID
	BRA.B	DONCHK
	;
P3NTOV:	BCLR.L	#P3IVAL,D7	;ASSUME PERIOD 1 VALID
	BCLR.B	#PEROVR,PER3VR+PERVFL(A2)
	BRA.B	DONCHK
	;
PER3XI:	BSET.L	#P3IVAL,D7	;PERIOD 3 INVALID
	BCLR.B	#PEROVR,PER3VR+PERVFL(A2)
	BRA.B	DONCHK
	

;###
	;CHECK IF PERIOD 4 OVERLAPS PERIODS 1,2 OR 3
CHP4OV:	BCLR.L	#P4IVAL,D7	;ASSUME NO OVERLAP
	BTST.B	#P4IVAL,DSBPFL(A2)	;PERIOD ENABLED?
	BNE.B	PER4IV
	BTST.B	#EXTACT,PER4VR+PERVFL(A2)	;EXTERNAL ACTIVE?
	BEQ.B	PER4XI
	LEA	PER4FX(A2),A1
	LEA	PER4VR(A2),A0
	BSR.W	_PER3	;CHECK PERIODS 3,2,1
	BCC.B	P4NTOV
PER4IV:	BSET.B	#PEROVR,PER4VR+PERVFL(A2)
	BSET.L	#P4IVAL,D7	;PERIOD 4 INVALID?
DONCHK:	MOVE.B	D7,BADPER(A2)
	RTS
	;
P4NTOV:	BCLR.L	#P4IVAL,D7	;PERIOD VALID
	BCLR.B	#PEROVR,PER4VR+PERVFL(A2)
	BRA.B	DONCHK
	;
PER4XI:	BSET.L	#P4IVAL,D7	;PERIOD 3 INVALID
	BCLR.B	#PEROVR,PER4VR+PERVFL(A2)
	BRA.B	DONCHK


;###
	;CHECK IF PERIOD 5 OVERLAPS PERIODS 1 - 4
CHP5OV:	BCLR.L	#P5IVAL,D7	;ASSUME NO OVERLAP
	BTST.B	#P5IVAL,DSBPFL(A2)	;PERIOD ENABLED?
	BNE.B	PER5IV
	BTST.B	#EXTACT,PER5VR+PERVFL(A2)	;EXTERNAL ACTIVE?
	BEQ.B	PER5XI
	LEA	PER5FX(A2),A1
	LEA	PER5VR(A2),A0
	BSR.W	_PER4	;CHECK PERIODS 4-1
	BCC.B	P5NTOV
PER5IV:	BSET.B	#PEROVR,PER5VR+PERVFL(A2)
	BSET.L	#P5IVAL,D7	;PERIOD INVALID
	BRA.B	DONCHK
	;
P5NTOV:	BCLR.L	#P5IVAL,D7	;PERIOD VALID
	BCLR.B	#PEROVR,PER5VR+PERVFL(A2)
	BRA.B	DONCHK
	;
PER5XI:	BSET.L	#P5IVAL,D7	;PERIOD 3 INVALID
	BCLR.B	#PEROVR,PER5VR+PERVFL(A2)
	BRA.B	DONCHK


;###
	;CHECK IF PERIOD 6 OVERLAPS PERIODS 1 - 5
CHP6OV:	BCLR.L	#P6IVAL,D7	;ASSUME NO OVERLAP
	BTST.B	#P6IVAL,DSBPFL(A2)	;PERIOD ENABLED?
	BNE.B	PER6IV
	BTST.B	#EXTACT,PER6VR+PERVFL(A2)	;EXTERNAL ACTIVE?
	BEQ.B	PER6XI
	LEA	PER6FX(A2),A1
	LEA	PER6VR(A2),A0
	BSR.W	_PER5	;CHECK PERIODS 5-1
	BCC.B	P6NTOV
PER6IV:	BSET.B	#PEROVR,PER6VR+PERVFL(A2)
	BSET.L	#P6IVAL,D7	;PERIOD INVALID
	BRA.W	DONCHK
	;
P6NTOV:	BCLR.L	#P6IVAL,D7	;PERIOD VALID
	BCLR.B	#PEROVR,PER6VR+PERVFL(A2)
	BRA.W	DONCHK
	;
PER6XI:	BSET.L	#P6IVAL,D7	;PERIOD 3 INVALID
	BCLR.B	#PEROVR,PER6VR+PERVFL(A2)
	BRA.W	DONCHK


;###
	;CHECK IF PERIOD 7 OVERLAPS PERIODS 1 - 6
CHP7OV:	BCLR.L	#P7IVAL,D7	;ASSUME NO OVERLAP
	BTST.B	#P7IVAL,DSBPFL(A2)	;PERIOD ENABLED?
	BNE.B	PER7IV
	BTST.B	#EXTACT,PER7VR+PERVFL(A2)	;EXTERNAL ACTIVE?
	BEQ.B	PER7XI
	LEA	PER7FX(A2),A1
	LEA	PER7VR(A2),A0
	BSR.W	_PER6	;CHECK PERIODS 6-1
	BCC.B	P7NTOV
PER7IV:	BSET.B	#PEROVR,PER7VR+PERVFL(A2)
	BSET.L	#P7IVAL,D7	;PERIOD INVALID
	BRA.W	DONCHK
	;
P7NTOV:	BCLR.L	#P7IVAL,D7	;PERIOD VALID
	BCLR.B	#PEROVR,PER7VR+PERVFL(A2)
	BRA.W	DONCHK
	;
PER7XI:	BSET.L	#P7IVAL,D7	;PERIOD 3 INVALID
	BCLR.B	#PEROVR,PER7VR+PERVFL(A2)
	BRA.W	DONCHK


;###
	;CHECK IF PERIOD 8 OVERLAPS PERIODS 1 - 7
CHP8OV:	BCLR.L	#P8IVAL,D7	;ASSUME NO OVERLAP
	BTST.B	#P8IVAL,DSBPFL(A2)	;PERIOD ENABLED?
	BNE.B	PER8IV
	BTST.B	#EXTACT,PER8VR+PERVFL(A2)	;EXTERNAL ACTIVE?
	BEQ.B	PER8XI
	LEA	PER8FX(A2),A1
	LEA	PER8VR(A2),A0
	BSR.B	_PER7	;CHECK PERIODS 7-1
	BCC.B	P8NTOV
PER8IV:	BSET.B	#PEROVR,PER8VR+PERVFL(A2)
	BSET.L	#P8IVAL,D7	;PERIOD INVALID
	BRA.W	DONCHK
	;
P8NTOV:	BCLR.L	#P8IVAL,D7	;PERIOD VALID
	BCLR.B	#PEROVR,PER8VR+PERVFL(A2)
	BRA.W	DONCHK
	;
PER8XI:	BSET.L	#P8IVAL,D7	;PERIOD 3 INVALID
	BCLR.B	#PEROVR,PER8VR+PERVFL(A2)
	BRA.W	DONCHK


;##
	;check if the input period times settings in A1 variables in A0
	;overlaps the period's start and end times.
	;clear carry on exit if no overlap
_PER7:	BTST.L	#P7IVAL,D7	;period valid?
	BNE.B	_PER6	;check next period
	LEA	PER7FX(A2),A4
	LEA	PER7VR(A2),A3
	BSR.B	CheckPeriodWrap
	BCS.B	OVRLPS	;overlaps

	;check if the input period times settings in A1 variables in A0
	;overlaps the period's start and end times.
	;clear carry on exit if no overlap
_PER6:	BTST.L	#P6IVAL,D7	;PERIOD VALID?
	BNE.B	_PER5	;check next period
	LEA	PER6FX(A2),A4
	LEA	PER6VR(A2),A3
	BSR.B	CheckPeriodWrap
	BCS.B	OVRLPS	;overlaps

	;check if the input period times settings in A1 variables in A0
	;overlaps the period's start and end times.
	;clear carry on exit if no overlap
_PER5:	BTST.L	#P5IVAL,D7	;PERIOD VALID?
	BNE.B	_PER4	;check next period
	LEA	PER5FX(A2),A4
	LEA	PER5VR(A2),A3
	BSR.B	CheckPeriodWrap
	BCS.B	OVRLPS	;overlaps

	;check if the input period times settings in A1 variables in A0
	;overlaps the period's start and end times.
	;clear carry on exit if no overlap
_PER4:	BTST.L	#P4IVAL,D7	;PERIOD VALID?
	BNE.B	_PER3	;check next period
	LEA	PER4FX(A2),A4
	LEA	PER4VR(A2),A3
	BSR.B	CheckPeriodWrap
	BCS.B	OVRLPS	;overlaps

	;check if the input period times settings in A1 variables in A0
	;overlaps the period's start and end times.
	;clear carry on exit if no overlap
_PER3:	BTST.L	#P3IVAL,D7	;PERIOD VALID?
	BNE.B	_PER2	;check next period
	LEA	PER3FX(A2),A4
	LEA	PER3VR(A2),A3
	BSR.B	CheckPeriodWrap
	BCS.B	OVRLPS	;overlaps

	;check if the input period times settings in A1 variables in A0
	;overlaps the period's start and end times.
	;clear carry on exit if no overlap
_PER2:	BTST.L	#P2IVAL,D7	;PERIOD VALID?
	BNE.B	_PER1	;check next period
	LEA	PER2FX(A2),A4
	LEA	PER2VR(A2),A3
	BSR.B	CheckPeriodWrap
	BCS.B	OVRLPS	;overlaps

	;check if the input period times settings in A1 variables in A0
	;overlaps the period's start and end times.
	;clear carry on exit if no overlap
_PER1:	BTST.L	#P1IVAL,D7	;PERIOD VALID?
	BNE.B	NO_OVR	;check next period
	LEA	PER1FX(A2),A4
	LEA	PER1VR(A2),A3
	BSR.B	CheckPeriodWrap
	BCS.B	OVRLPS	;overlaps
	;DOESN'T OVERLAP
NO_OVR:	MOVE.W	#0,CCR	;CLEAR THE CARRY FLAG
	RTS
	;
	;OVERLAPS
OVRLPS:	MOVE.W	#C,CCR	;SET THE CARRY FLAG
	RTS


CheckPeriodWrap:
	MOVEQ	#$0000007F,D4
	BTST.B	#Wraps_Through_Midnight,ETVDOW(A0)
	BNE.B	_InPeriodWraps
	BTST.B	#Wraps_Through_Midnight,ETVDOW(A3)
	BNE.B	_TestPeriodWraps
	;NEITHER PERIOD WRAPS
	;example 8:00 to 17:00 vs 5:00 to 14:00 
	;	
	;CHECK IF THE DAYS OF THE WEEK OVERLAP
	MOVE.B	DOW(A1),D1	;INPUT PERIOD'S DAYS OF THE WEEK
	MOVE.B	DOW(A4),D0	;TEST PERIOD'S DAYS OF THE WEEK
	AND.L	D0,D1
	AND.L	D4,D1	;ONLY THE 7 WEEK DAYS
	BEQ.B	_NoOverlap	;NOT USING THE SAME DAYS
	;HAS SOME COMMON DAYS, CHECK FOR TIME OVERLAP
	MOVE.W	CSTIME(A0),D3
	MOVE.W	CETIME(A0),D5
	MOVE.W	CSTIME(A3),D1
	JSR	TIMPER
	BCS.W	OVRLPS	;OVERLAPS
	;CHECK IF THE END TIME DAYS OF THE WEEK OVERLAP
	MOVE.W	CETIME(A3),D1
	JMP	TIMPER	;carry flag set if overlap


	;INPUT PERIOD DOESN'T, TEST PERIOD WRAPS
	;example 8:00 to 17:00 vs 16:00 to 6:00 
_TestPeriodWraps:
	;	
	;CHECK IF THE DAYS OF THE WEEK OVERLAP
	MOVE.B	DOW(A1),D1	;INPUT PERIOD'S DAYS OF THE WEEK
	MOVE.B	DOW(A4),D0	;TEST PERIOD'S START TIME DAYS OF THE WEEK
	AND.L	D0,D1
	AND.L	D4,D1	;ONLY THE 7 WEEK DAYS
	BEQ.B	_CheckEndTime	;NOT USING THE SAME DAYS
	;HAS SOME COMMON DAYS, CHECK FOR TIME OVERLAP
	MOVE.W	CSTIME(A0),D3
	MOVE.W	CETIME(A0),D5
	MOVE.W	CSTIME(A3),D1
	JSR	TIMPER
	BCS.W	OVRLPS	;OVERLAPS
_CheckEndTime:
	;CHECK IF THE END TIME DAYS OF THE WEEK OVERLAP
	MOVE.B	DOW(A1),D1	;INPUT PERIOD'S DAYS OF THE WEEK
	MOVE.B	ETVDOW(A3),D0	;TEST PERIOD'S DAYS OF THE WEEK
	AND.L	D0,D1
	AND.L	D4,D1	;ONLY THE 7 WEEK DAYS
	BEQ.B	_NoOverlap	;NOT USING THE SAME DAYS
	;HAS SOME COMMON DAYS, CHECK FOR TIME OVERLAP
	MOVE.W	CETIME(A3),D1
	JMP	TIMPER	;carry flag set if overlap
	;
_NoOverlap:
	RTS


_InPeriodWraps:
	BTST.B	#Wraps_Through_Midnight,ETVDOW(A3)
	BNE.B	_BothPeriodsWrap
	;INPUT PERIOD WRAPS, TEST PERIOD DOESN'T
	;example 16:00 to 8:00 vs 7:00 to 17:00 
	MOVE.B	DOW(A1),D1	;INPUT PERIOD'S START TIME DAYS OF THE WEEK
	MOVE.B	DOW(A4),D0	;TEST PERIOD'S START TIME DAYS OF THE WEEK
	AND.L	D0,D1
	AND.L	D4,D1	;ONLY THE 7 WEEK DAYS
	BEQ.B	_CheckEndTime2	;NOT USING THE SAME DAYS
	;HAS SOME COMMON DAYS, CHECK FOR TIME OVERLAP
	MOVE.W	CSTIME(A3),D3
	MOVE.W	CETIME(A3),D5
	MOVE.W	CSTIME(A0),D1
	JSR	TIMPER
	BCS.W	OVRLPS	;OVERLAPS
_CheckEndTime2:
	;CHECK IF THE END TIME DAYS OF THE WEEK OVERLAP
	MOVE.B	ETVDOW(A0),D1	;INPUT PERIOD'S END TIMES DAYS OF THE WEEK
	MOVE.B	DOW(A4),D0	;TEST PERIOD'S START TIME DAYS OF THE WEEK
	AND.L	D0,D1
	AND.L	D4,D1	;ONLY THE 7 WEEK DAYS
	BEQ.B	_NoOverlap	;NOT USING THE SAME DAYS
	;HAS SOME COMMON DAYS, CHECK FOR TIME OVERLAP
	MOVE.W	CETIME(A0),D1
	JMP	TIMPER	;carry flag set if overlap


	;BOTH PERIODS WRAP
	;example 16:00 to 8:00 vs 17:00 to 7:00 
_BothPeriodsWrap:
	;check start time to midnight vs start time
	;example 16:00 to 0:00 vs 17:00 
	MOVE.B	DOW(A1),D1	;INPUT PERIOD'S DAYS OF THE WEEK
	MOVE.B	DOW(A4),D0	;TEST PERIOD'S START TIME DAYS OF THE WEEK
	AND.L	D0,D1
	AND.L	D4,D1	;ONLY THE 7 WEEK DAYS
	BEQ.B	_CheckEndTime4	;NOT USING THE SAME DAYS
	;HAS SOME COMMON DAYS, CHECK FOR TIME OVERLAP
	MOVE.W	CSTIME(A0),D3
	MOVEQ	#0,D5
	MOVE.W	CSTIME(A3),D1
	JSR	TIMPER
	BCS.W	OVRLPS	;OVERLAPS
	;check start time to midnight vs end time
	;example 16:00 to 0:00 vs 7:00 
	;CHECK IF THE END TIME DAYS OF THE WEEK OVERLAP
	MOVE.B	DOW(A1),D1	;INPUT PERIOD'S DAYS OF THE WEEK
	MOVE.B	ETVDOW(A3),D0	;TEST PERIOD'S END TIME DAYS OF THE WEEK
	AND.L	D0,D1
	AND.L	D4,D1	;ONLY THE 7 WEEK DAYS
	BEQ.B	_CheckEndTime4	;NOT USING THE SAME DAYS
	;HAS SOME COMMON DAYS, CHECK FOR TIME OVERLAP
	MOVE.W	CETIME(A3),D1
	JSR	TIMPER
	BCS.W	OVRLPS	;OVERLAPS
_CheckEndTime4:
	;check midnight to end time vs start time
	;example 0:00 to 8:00 vs 17:00 
	;CHECK IF THE END TIME DAYS OF THE WEEK OVERLAP
	MOVE.B	ETVDOW(A0),D1	;INPUT PERIOD'S END TIMES DAYS OF THE WEEK
	MOVE.B	DOW(A4),D0	;TEST PERIOD'S DAYS OF THE WEEK
	AND.L	D0,D1
	AND.L	D4,D1	;ONLY THE 7 WEEK DAYS
	BEQ.B	_CheckEndTime5	;NOT USING THE SAME DAYS
	;HAS SOME COMMON DAYS, CHECK FOR TIME OVERLAP
	MOVEQ	#0,D3	;from midnight
	MOVE.W	CETIME(A0),D5
	MOVE.W	CSTIME(A3),D1
	JSR	TIMPER
	BCS.W	OVRLPS	;OVERLAPS
_CheckEndTime5:
	;check midnight to end time vs end time
	;example 0:00 to 8:00 vs 7:00 
	;CHECK IF THE END TIME DAYS OF THE WEEK OVERLAP
	MOVE.B	ETVDOW(A0),D1	;INPUT PERIOD'S END TIMES DAYS OF THE WEEK
	MOVE.B	ETVDOW(A3),D0	;TEST PERIOD'S END TIME DAYS OF THE WEEK
	AND.L	D0,D1
	AND.L	D4,D1	;ONLY THE 7 WEEK DAYS
	BEQ.W	_NoOverlap	;NOT USING THE SAME DAYS
	;HAS SOME COMMON DAYS, CHECK FOR TIME OVERLAP
	MOVEQ	#0,D3	;from midnight
	MOVE.W	CETIME(A0),D5
	MOVE.W	CETIME(A3),D1
	JMP	TIMPER	;carry flag set if overlap



;###
	;CHECK RAMP 1
CHRMP1:	BTST.L	#P1IVAL,D7	;PERIOD 1 VALID?
	BNE.B	CHRP1X
	MOVE.W	PER1VR+CETIME(A2),D3	;START OF RAMP 1
;	MOVE.W	PER1VR+CSTIME(A2),D5	;END OF RAMP 1
	MOVE.B	PER1VR+ETVDOW(A2),D0	;END DAYS OF THE WEEK
	MOVEQ	#1,D1
	BSR.W	RMPEND
	MOVE.B	D2,ERAMP1(A2)	;END OF RAMP 1
CHRP1X:	RTS


;###
	;CHECK RAMP 2
CHRMP2:	BTST.L	#P2IVAL,D7	;PERIOD 2 VALID?
	BNE.B	CHRP2X
	MOVE.W	PER2VR+CETIME(A2),D3	;START OF RAMP 2
;	MOVE.W	PER2VR+CSTIME(A2),D5	;END OF RAMP 2
	MOVE.B	PER2VR+ETVDOW(A2),D0	;END DAYS OF THE WEEK
	MOVEQ	#2,D1
	BSR.W	RMPEND
	MOVE.B	D2,ERAMP2(A2)	;END OF RAMP 2
CHRP2X:	RTS


;###
	;CHECK RAMP 3
CHRMP3:	BTST.L	#P3IVAL,D7	;PERIOD 3 VALID?
	BNE.B	CHRP3X
	MOVE.W	PER3VR+CETIME(A2),D3	;START OF RAMP 3
;	MOVE.W	PER3VR+CSTIME(A2),D5	;END OF RAMP 3
	MOVE.B	PER3VR+ETVDOW(A2),D0	;END DAYS OF THE WEEK
	MOVEQ	#3,D1
	BSR.B	RMPEND
	MOVE.B	D2,ERAMP3(A2)	;END OF RAMP 3
CHRP3X:	RTS


;###
	;CHECK RAMP 4
CHRMP4:	BTST.L	#P4IVAL,D7	;PERIOD 4 VALID?
	BNE.B	CHRP4X
	MOVE.W	PER4VR+CETIME(A2),D3	;START OF RAMP 4
;	MOVE.W	PER4VR+CSTIME(A2),D5	;END OF RAMP 4
	MOVE.B	PER4VR+ETVDOW(A2),D0	;END DAYS OF THE WEEK
	MOVEQ	#4,D1
	BSR.B	RMPEND
	MOVE.B	D2,ERAMP4(A2)	;END OF RAMP 4
CHRP4X:	RTS
	

;###
	;CHECK RAMP 5
CHRMP5:	BTST.L	#P5IVAL,D7	;PERIOD 5 VALID?
	BNE.B	CHRP5X
	MOVE.W	PER5VR+CETIME(A2),D3	;START OF RAMP 5
;	MOVE.W	PER5VR+CSTIME(A2),D5	;END OF RAMP 5
	MOVE.B	PER5VR+ETVDOW(A2),D0	;END DAYS OF THE WEEK
	MOVEQ	#5,D1
	BSR.B	RMPEND
	MOVE.B	D2,ERAMP5(A2)	;END OF RAMP 5
CHRP5X:	RTS
	

;###
	;CHECK RAMP 6
CHRMP6:	BTST.L	#P6IVAL,D7	;PERIOD 6 VALID?
	BNE.B	CHRP6X
	MOVE.W	PER6VR+CETIME(A2),D3	;START OF RAMP 6
;	MOVE.W	PER6VR+CSTIME(A2),D5	;END OF RAMP 6
	MOVE.B	PER6VR+ETVDOW(A2),D0	;END DAYS OF THE WEEK
	MOVEQ	#6,D1
	BSR.B	RMPEND
	MOVE.B	D2,ERAMP6(A2)	;END OF RAMP 6
CHRP6X:	RTS
	

;###
	;CHECK RAMP 7
CHRMP7:	BTST.L	#P7IVAL,D7	;PERIOD 7 VALID?
	BNE.B	CHRP7X
	MOVE.W	PER7VR+CETIME(A2),D3	;START OF RAMP 7
;	MOVE.W	PER7VR+CSTIME(A2),D5	;END OF RAMP 7
	MOVE.B	PER7VR+ETVDOW(A2),D0	;END DAYS OF THE WEEK
	MOVEQ	#7,D1
	BSR.B	RMPEND
	MOVE.B	D2,ERAMP7(A2)	;END OF RAMP 7
CHRP7X:	RTS
	

;###
	;CHECK RAMP 8
CHRMP8:	BTST.L	#P8IVAL,D7	;PERIOD 5 VALID?
	BNE.B	CHRP8X
	MOVE.W	PER8VR+CETIME(A2),D3	;START OF RAMP 8
;	MOVE.W	PER8VR+CSTIME(A2),D5	;END OF RAMP 8
	MOVE.B	PER8VR+ETVDOW(A2),D0	;END DAYS OF THE WEEK
	MOVEQ	#8,D1
	BSR.B	RMPEND
	MOVE.B	D2,ERAMP8(A2)	;END OF RAMP 8
CHRP8X:	RTS
	
;##
	;ON ENTRY 'D3' HAS THE START RAMP TIME
	;ON EXIT 'D1' HAS THE RAMP END TIME
	;AND 'D2' HAS THE PERIOD FOR IT
RMPEND:
	MOVE.B	D1,DebugOn(A2)

	MOVE.L	#$0000173B,D5	;HIGHEST POSSIBLE
	MOVEQ	#0,D6	;NUMBER OF DAYS BETWEEN PERIODS
	MOVEQ	#0,D2
	ANDI.L	#$0000007F,D0	;ISOLATE END TIME DAYS OF THE WEEK
	MOVE.B	C_DYOFWK(A2),D1
	ANDI.L	#DYOFWK_MASK,D1	;IGNORE THE OTHER BITS
	BTST.L	D1,D0	;RAMP END ACTIVE TODAY?
	BNE.B	RampEndActiveToday	;ACTIVE TODAY
	;START OF RAMP NOT TODAY, SCAN BACKWARDS FOR VALID DAY PRIOR TO TODAY
	MOVEQ	#6,D4	;CHECK UP TO 6 PREVIOUS DAYS
TRYNEXTDAY:
	SUBQ.L	#1,D1
	BCC.B	NOTWRAPPED
	MOVEQ	#6,D1	;WRAP AROUND
NOTWRAPPED:
	BTST.L	D1,D0
	BNE.B	GOT_RAMP_START_DAY	;MOST RECENT RAMP START DAY
	SUBQ.L	#1,D4
	BNE.B	TRYNEXTDAY
	;NO END DAYS ENABLED!?
	MOVE.L	D5,D1	;DEFAULT RAMP END
	TST.L	D2	;SET ZERO FLAG IF RAMP END NOT FOUND
	RTS

GOT_RAMP_START_DAY:
	MOVE.L	D3,D5	;Ramp End is after this if Today
	MOVEQ	#7,D3	;CHECK ALL 7 DAYS
	MOVE.L	D1,D0	;DAY OF WEEK FOR START RAMP
	BRA.W	_RMP8
	

RampEndActiveToday:
	MOVE.L	D1,D0	;Get day of the week on		
	BSR.W	CheckForNextHighestPeriodStart
	BEQ.B	CheckTodaysLowestPeriod
	RTS		;Found an active ramp end (period start)

;RMP8:
;	BTST.L	#P8IVAL,D7
;	BNE.B	RMP7	;PERIOD 8 TIMES INVALID
;	BTST.B	#ACTSTM,PER8VR+PERVFL(A2)	;START TIME ACTIVE?
;	BEQ.B	RMP7
;	MOVE.W	PER8VR+CSTIME(A2),D1
;	MOVEQ	#8,D4
;	BSR.W	HINEXT
;RMP7:	BTST.L	#P7IVAL,D7
;	BNE.B	RMP6	;PERIOD 7 TIMES INVALID
;	BTST.B	#ACTSTM,PER7VR+PERVFL(A2)	;START TIME ACTIVE?
;	BEQ.B	RMP6
;	MOVE.W	PER7VR+CSTIME(A2),D1
;	MOVEQ	#7,D4
;	BSR.W	HINEXT
;RMP6:	BTST.L	#P6IVAL,D7
;	BNE.B	RMP5	;PERIOD 6 TIMES INVALID
;	BTST.B	#ACTSTM,PER6VR+PERVFL(A2)	;START TIME ACTIVE?
;	BEQ.B	RMP5
;	MOVE.W	PER6VR+CSTIME(A2),D1
;	MOVEQ	#6,D4
;	BSR.W	HINEXT
;RMP5:	BTST.L	#P5IVAL,D7
;	BNE.B	RMP4	;PERIOD 5 TIMES INVALID
;	BTST.B	#ACTSTM,PER5VR+PERVFL(A2)	;START TIME ACTIVE?
;	BEQ.B	RMP4
;	MOVE.W	PER5VR+CSTIME(A2),D1
;	MOVEQ	#5,D4
;	BSR.W	HINEXT
;RMP4:	BTST.L	#P4IVAL,D7
;	BNE.B	RMP3	;PERIOD 4 TIMES INVALID
;	BTST.B	#ACTSTM,PER4VR+PERVFL(A2)	;START TIME ACTIVE?
;	BEQ.B	RMP3
;	MOVE.W	PER4VR+CSTIME(A2),D1
;	MOVEQ	#4,D4
;	BSR.W	HINEXT
;RMP3:	BTST.L	#P3IVAL,D7
;	BNE.B	RMP2	;PERIOD 3 TIMES INVALID
;	BTST.B	#ACTSTM,PER3VR+PERVFL(A2)	;START TIME ACTIVE?
;	BEQ.B	RMP2
;	MOVE.W	PER3VR+CSTIME(A2),D1
;	MOVEQ	#3,D4
;	BSR.W	HINEXT
;RMP2:	BTST.L	#P2IVAL,D7
;	BNE.B	RMP1	;PERIOD 2 TIMES INVALID
;	BTST.B	#ACTSTM,PER2VR+PERVFL(A2)	;START TIME ACTIVE?
;	BEQ.B	RMP1
;	MOVE.W	PER2VR+CSTIME(A2),D1
;	MOVEQ	#2,D4
;	BSR.W	HINEXT
;RMP1:	BTST.L	#P1IVAL,D7
;	BNE.B	RMP0	;PERIOD 1 TIMES INVALID
;	BTST.B	#ACTSTM,PER1VR+PERVFL(A2)	;START TIME ACTIVE?
;	BEQ.B	RMP0
;	MOVE.W	PER1VR+CSTIME(A2),D1
;	MOVEQ	#1,D4
;	BSR.W	HINEXT
;RMP0:
;	TST.L	D2
;	BNE.W	FOUND_RAMP_END	;FOUND A RAMP END
;	;COULDN'T FIND HIGHER TIME RAMP END FOR TODAY


CheckTodaysLowestPeriod:
	MOVEQ	#0,D6
	MOVE.B	C_DYOFWK(A2),D0
	BSR.W	CheckForLowestActivePeriodStart
	BEQ.B	CheckTodaysNextHighest
	RTS

CheckTodaysNextHighest:
	MOVEQ	#0,D6
	MOVE.B	C_DYOFWK(A2),D0
	BSR.W	CheckForNextHighestPeriodStart
	BEQ.B	CheckSubsequentDays
	RTS

CheckSubsequentDays:
	;CHECK FOR SUBSEQUENT DAYS

	MOVE.B	C_DYOFWK(A2),D0
	MOVEQ	#6,D3	;CHECK ALL 6 SUBSEQUENT DAYS
	BSR.B	CHKNXD
	BEQ.B	CheckTodaysLowerPeriods
	;found a ramp end in subsequent days
	RTS
CheckTodaysLowerPeriods:
	MOVEQ	#1,D3	;Finally check first portion of today

CHKNXD:	ADDQ.L	#1,D6	;NUMBER OF DAYS BETWEEN
	ADDQ.L	#1,D0	;NEXT DAY
	MOVE.W	#$173B,D5	;23:59, HIGHEST POSSIBLE TIME
	ANDI.L	#DYOFWK_MASK,D0	;IGNORE THE OTHER BITS
	CMPI.L	#7,D0
	BLO.B	_RMP8	;0 TO 6 ONLY
	MOVEQ	#0,D0	;WRAP AROUND TO MONDAY
		    
	
_RMP8:	BTST.L	#P8IVAL,D7
	BNE.B	_RMP7	;PERIOD 8 TIMES INVALID
	BTST.B	D0,PER8FX+DOW(A2)	;ACTIVE NEXT DAY?
	BEQ.B	_RMP7
	MOVE.W	PER8VR+CSTIME(A2),D1
	MOVEQ	#8,D4
	BSR.W	LONEXT
_RMP7:	BTST.L	#P7IVAL,D7
	BNE.B	_RMP6	;PERIOD 7 TIMES INVALID
	BTST.B	D0,PER7FX+DOW(A2)	;ACTIVE NEXT DAY?
	BEQ.B	_RMP6
	MOVE.W	PER7VR+CSTIME(A2),D1
	MOVEQ	#7,D4
	BSR.W	LONEXT
_RMP6:	BTST.L	#P6IVAL,D7
	BNE.B	_RMP5	;PERIOD 6 TIMES INVALID
	BTST.B	D0,PER6FX+DOW(A2)	;ACTIVE NEXT DAY?
	BEQ.B	_RMP5
	MOVE.W	PER6VR+CSTIME(A2),D1
	MOVEQ	#6,D4
	BSR.B	LONEXT
_RMP5:	BTST.L	#P5IVAL,D7
	BNE.B	_RMP4	;PERIOD 5 TIMES INVALID
	BTST.B	D0,PER5FX+DOW(A2)	;ACTIVE NEXT DAY?
	BEQ.B	_RMP4
	MOVE.W	PER5VR+CSTIME(A2),D1
	MOVEQ	#5,D4
	BSR.B	LONEXT
_RMP4:	BTST.L	#P4IVAL,D7
	BNE.B	_RMP3	;PERIOD 4 TIMES INVALID
	BTST.B	D0,PER4FX+DOW(A2)	;ACTIVE NEXT DAY?
	BEQ.B	_RMP3
	MOVE.W	PER4VR+CSTIME(A2),D1
	MOVEQ	#4,D4
	BSR.B	LONEXT
_RMP3:	BTST.L	#P3IVAL,D7
	BNE.B	_RMP2	;PERIOD 3 TIMES INVALID
	BTST.B	D0,PER3FX+DOW(A2)	;ACTIVE NEXT DAY?
	BEQ.B	_RMP2
	MOVE.W	PER3VR+CSTIME(A2),D1
	MOVEQ	#3,D4
	BSR.B	LONEXT
_RMP2:	BTST.L	#P2IVAL,D7
	BNE.B	_RMP1	;PERIOD 2 TIMES INVALID
	BTST.B	D0,PER2FX+DOW(A2)	;ACTIVE NEXT DAY?
	BEQ.B	_RMP1
	MOVE.W	PER2VR+CSTIME(A2),D1
	MOVEQ	#2,D4
	BSR.B	LONEXT
_RMP1:	BTST.L	#P1IVAL,D7
	BNE.B	_RMP0	;PERIOD 1 TIMES INVALID
	BTST.B	D0,PER1FX+DOW(A2)	;ACTIVE NEXT DAY?
	BEQ.B	_RMP0
	MOVE.W	PER1VR+CSTIME(A2),D1
	MOVEQ	#1,D4
	BSR.B	LONEXT
_RMP0:	TST.L	D2	;FOUND A RAMP END?
	BNE.B	FOUND_RAMP_END
	SUBQ.L	#1,D3	;TRY NEXT DAY
	BNE.W	CHKNXD
	MOVE.L	D5,D1	;DEFAULT RAMP END
	TST.L	D2	;SET ZERO FLAG IF RAMP END NOT FOUND
	RTS
	;
FOUND_RAMP_END:
	;MERGE THE # DAYS BETWEEN RAMP START AND END
	;INTO UPPER NIBBLE OF D2
	LSL.L	#4,D6	;SHIFT TO UPPER NIBBLE
	OR.L	D6,D2	;MERGE WITH RAMP END PERIOD	
	RTS

;#
	;CHECK IF THE TIME IN 'D1' IS THE NEXT LOWEST
LONEXT:	CMPI.L	#7,D3	;First Time through loop?
	BNE.B	SubsequentDays
	;On the Same Day Ramp End is Higher
	CMP.L	D5,D1
	BLS.B	LONXTX
	BRA.B	IsNextTime
SubsequentDays:
	CMP.L	D5,D1
	BHS.B	LONXTX	;IGNORE IF THE SAME
	;IS THE NEXT LOWEST TIME
IsNextTime:
	MOVE.L	D1,D5	;NEW NEXT LOWEST
	MOVE.L	D4,D2	;PERIOD OF NEXT LOWEST
LONXTX:	RTS

;#
	;CHECK IF THE TIME IN 'D1' IS THE NEXT LOWEST
	;TIME AFTER 'D3' BUT BEFORE 'D5'
HINEXT:	CMP.L	D5,D1
	BEQ.B	_HINXTX	;IGNORE IF THE SAME
	JSR	TIMPER	;BETWEEN TIMES?
	BCC.B	HINXTX
	;IS THE NEXT HIGHEST TIME
NewNextHighest:
	MOVE.L	D1,D5	;NEW NEXT HIGHEST
	MOVE.L	D4,D2	;PERIOD OF NEXT HIGHEST
HINXTX:	RTS
	;
	;IGNORE IF THE SAME, UNLESS 23:59
_HINXTX:
	CMPI.L	#$0000173B,D1	;23:59, HIGHEST POSSIBLE TIME
	BEQ.B	NewNextHighest
	RTS

;###
CheckForLowestActivePeriodStart:
	MOVE.W	#$173B,D5	;23:59, HIGHEST POSSIBLE TIME
	ANDI.L	#DYOFWK_MASK,D0	;IGNORE THE OTHER BITS
	CMPI.L	#7,D0
	BLO.B	_CHK8	;0 TO 6 ONLY
	MOVEQ	#0,D0	;WRAP AROUND TO MONDAY
		    
	
_CHK8:	BTST.L	#P8IVAL,D7
	BNE.B	_CHK7	;PERIOD 8 TIMES INVALID
	BTST.B	#ACTSTM,PER8VR+PERVFL(A2)	;START TIME ACTIVE?
	BEQ.B	_CHK7
	MOVE.W	PER8VR+CSTIME(A2),D1
	MOVEQ	#8,D4
	BSR.W	LONEXT
_CHK7:	BTST.L	#P7IVAL,D7
	BNE.B	_CHK6	;PERIOD 7 TIMES INVALID
	BTST.B	#ACTSTM,PER7VR+PERVFL(A2)	;START TIME ACTIVE?
	BEQ.B	_CHK6
	MOVE.W	PER7VR+CSTIME(A2),D1
	MOVEQ	#7,D4
	BSR.W	LONEXT
_CHK6:	BTST.L	#P6IVAL,D7
	BNE.B	_CHK5	;PERIOD 6 TIMES INVALID
	BTST.B	#ACTSTM,PER6VR+PERVFL(A2)	;START TIME ACTIVE?
	BEQ.B	_CHK5
	MOVE.W	PER6VR+CSTIME(A2),D1
	MOVEQ	#6,D4
	BSR.W	LONEXT
_CHK5:	BTST.L	#P5IVAL,D7
	BNE.B	_CHK4	;PERIOD 5 TIMES INVALID
	BTST.B	#ACTSTM,PER5VR+PERVFL(A2)	;START TIME ACTIVE?
	BEQ.B	_CHK4
	MOVE.W	PER5VR+CSTIME(A2),D1
	MOVEQ	#5,D4
	BSR.W	LONEXT
_CHK4:	BTST.L	#P4IVAL,D7
	BNE.B	_CHK3	;PERIOD 4 TIMES INVALID
	BTST.B	#ACTSTM,PER4VR+PERVFL(A2)	;START TIME ACTIVE?
	BEQ.B	_CHK3
	MOVE.W	PER4VR+CSTIME(A2),D1
	MOVEQ	#4,D4
	BSR.W	LONEXT
_CHK3:	BTST.L	#P3IVAL,D7
	BNE.B	_CHK2	;PERIOD 3 TIMES INVALID
	BTST.B	#ACTSTM,PER3VR+PERVFL(A2)	;START TIME ACTIVE?
	BEQ.B	_CHK2
	MOVE.W	PER3VR+CSTIME(A2),D1
	MOVEQ	#3,D4
	BSR.W	LONEXT
_CHK2:	BTST.L	#P2IVAL,D7
	BNE.B	_CHK1	;PERIOD 2 TIMES INVALID
	BTST.B	#ACTSTM,PER2VR+PERVFL(A2)	;START TIME ACTIVE?
	BEQ.B	_CHK1
	MOVE.W	PER2VR+CSTIME(A2),D1
	MOVEQ	#2,D4
	BSR.W	LONEXT
_CHK1:	BTST.L	#P1IVAL,D7
	BNE.B	_CHK0	;PERIOD 1 TIMES INVALID
	BTST.B	#ACTSTM,PER1VR+PERVFL(A2)	;START TIME ACTIVE?
	BEQ.B	_CHK0
	MOVE.W	PER1VR+CSTIME(A2),D1
	MOVEQ	#1,D4
	BSR.W	LONEXT
_CHK0:	TST.L	D2	;FOUND A RAMP END?
	BNE.W	FOUND_RAMP_END
	MOVE.L	D5,D1	;DEFAULT RAMP END
	TST.L	D2	;SET ZERO FLAG IF RAMP END NOT FOUND
	RTS

	;Must be:
	;	active today
	;	after the ramp start
	;	before the end of the day
CheckForNextHighestPeriodStart:
	MOVE.W	#$173B,D5	;23:59, HIGHEST POSSIBLE TIME
	ANDI.L	#DYOFWK_MASK,D0	;IGNORE THE OTHER BITS
	CMPI.L	#7,D0
	BLO.B	CHK8	;0 TO 6 ONLY
	MOVEQ	#0,D0	;WRAP AROUND TO MONDAY

CHK8:	BTST.L	#P8IVAL,D7
	BNE.B	CHK7	;PERIOD 8 TIMES INVALID
	BTST.B	D0,PER8FX+DOW(A2)	;ACTIVE NEXT DAY?
	BEQ.B	CHK7
	MOVE.W	PER8VR+CSTIME(A2),D1
	MOVEQ	#8,D4
	BSR.W	HINEXT
CHK7:	BTST.L	#P7IVAL,D7
	BNE.B	CHK6	;PERIOD 7 TIMES INVALID
	BTST.B	D0,PER7FX+DOW(A2)	;ACTIVE NEXT DAY?
	BEQ.B	CHK6
	MOVE.W	PER7VR+CSTIME(A2),D1
	MOVEQ	#7,D4
	BSR.W	HINEXT
CHK6:	BTST.L	#P6IVAL,D7
	BNE.B	CHK5	;PERIOD 6 TIMES INVALID
	BTST.B	D0,PER6FX+DOW(A2)	;ACTIVE NEXT DAY?
	BEQ.B	CHK5
	MOVE.W	PER6VR+CSTIME(A2),D1
	MOVEQ	#6,D4
	BSR.W	HINEXT
CHK5:	BTST.L	#P5IVAL,D7
	BNE.B	CHK4	;PERIOD 5 TIMES INVALID
	BTST.B	D0,PER5FX+DOW(A2)	;ACTIVE NEXT DAY?
	BEQ.B	CHK4
	MOVE.W	PER5VR+CSTIME(A2),D1
	MOVEQ	#5,D4
	BSR.W	HINEXT
CHK4:	BTST.L	#P4IVAL,D7
	BNE.B	CHK3	;PERIOD 4 TIMES INVALID
	BTST.B	D0,PER4FX+DOW(A2)	;ACTIVE NEXT DAY?
	BEQ.B	CHK3
	MOVE.W	PER4VR+CSTIME(A2),D1
	MOVEQ	#4,D4
	BSR.W	HINEXT
CHK3:	BTST.L	#P3IVAL,D7
	BNE.B	CHK2	;PERIOD 3 TIMES INVALID
	BTST.B	D0,PER3FX+DOW(A2)	;ACTIVE NEXT DAY?
	BEQ.B	CHK2
	MOVE.W	PER3VR+CSTIME(A2),D1
	MOVEQ	#3,D4
	BSR.W	HINEXT
CHK2:	BTST.L	#P2IVAL,D7
	BNE.B	CHK1	;PERIOD 2 TIMES INVALID
	BTST.B	D0,PER2FX+DOW(A2)	;ACTIVE NEXT DAY?
	BEQ.B	CHK1
	MOVE.W	PER2VR+CSTIME(A2),D1
	MOVEQ	#2,D4
	BSR.W	HINEXT
CHK1:	BTST.L	#P1IVAL,D7
	BNE.B	CHK0	;PERIOD 1 TIMES INVALID
	BTST.B	D0,PER1FX+DOW(A2)	;ACTIVE NEXT DAY?
	BEQ.B	CHK0
	MOVE.W	PER1VR+CSTIME(A2),D1
	MOVEQ	#1,D4
	BSR.W	HINEXT
CHK0:	TST.L	D2	;FOUND A RAMP END?
	BNE.W	FOUND_RAMP_END
	MOVE.L	D5,D1	;DEFAULT RAMP END
	TST.L	D2	;SET ZERO FLAG IF RAMP END NOT FOUND
	RTS

;###
	;DETERMINE TIME SECTION IN
SECTIN:	MOVE.W	C_HOUR(A2),D1	;GET CURRENT TIME
	BSR.B	CLSECN
	TST.B	D0
	BNE.B	NRMSEC
	;IS RETURNING RAMPING FROM PERIOD 1
	EXTB.L	D2
	CMPI.L	#1,D2	;END OF RAMP = PERIOD 1?!
	BNE.B	NRMSEC
	;IS DEFAULTING TO PERIOD 1
	MOVEQ	#-1,D0
NRMSEC:	MOVE.B	D0,SETPON(A2)
	CMPI.L	#PERD1,D0
	BLO.B	IN_A_RAMP
	MOVEQ	#0,D2	;
IN_A_RAMP:	
	MOVE.B	D2,RAMP_END(A2)
	RTS

DEFLT1:	MOVEQ	#PERD1,D0
	RTS
DEFLT2:	MOVEQ	#PERD2,D0
	RTS
DEFLT3:	MOVEQ	#PERD3,D0
	RTS
DEFLT4:	MOVEQ	#PERD4,D0
	RTS
DEFLT5:	MOVEQ	#PERD5,D0
	RTS
DEFLT6:	MOVEQ	#PERD6,D0
	RTS
DEFLT7:	MOVEQ	#PERD7,D0
	RTS
DEFLT8:	MOVEQ	#PERD8,D0
	RTS
DEFLR1:	MOVEQ	#RAMP1,D0
	RTS
DEFLR2:	MOVEQ	#RAMP2,D0
	RTS
DEFLR3:	MOVEQ	#RAMP3,D0
	RTS
DEFLR4:	MOVEQ	#RAMP4,D0
	RTS
DEFLR5:	MOVEQ	#RAMP5,D0
	RTS
DEFLR6:	MOVEQ	#RAMP6,D0
	RTS
DEFLR7:	MOVEQ	#RAMP7,D0
	RTS
DEFLR8:	MOVEQ	#RAMP8,D0
	RTS
	;



	;ON ENTRY 'D1' HAS THE CURRENT TIME
	;'A2' POINTS TO THE TIME VARIABLES
CLSECN:	MOVEQ	#-1,D0	;ASSUME ALL PERIODS DISABLED
	BTST.B	#EXTACT,PER1VR+PERVFL(A2)	;EXTERNAL ACTIVE?
	BEQ.B	_HASP1
	BCLR.L	#P1IVAL,D0
_HASP1:
	BTST.B	#EXTACT,PER2VR+PERVFL(A2)	;EXTERNAL ACTIVE?
	BEQ.B	_HASP2
	BCLR.L	#P2IVAL,D0
_HASP2:
	BTST.B	#EXTACT,PER3VR+PERVFL(A2)	;EXTERNAL ACTIVE?
	BEQ.B	_HASP3
	BCLR.L	#P3IVAL,D0
_HASP3:
	BTST.B	#EXTACT,PER4VR+PERVFL(A2)	;EXTERNAL ACTIVE?
	BEQ.B	_HASP4
	BCLR.L	#P4IVAL,D0
_HASP4:
	BTST.B	#EXTACT,PER5VR+PERVFL(A2)	;EXTERNAL ACTIVE?
	BEQ.B	_HASP5
	BCLR.L	#P5IVAL,D0
_HASP5:
	BTST.B	#EXTACT,PER6VR+PERVFL(A2)	;EXTERNAL ACTIVE?
	BEQ.B	_HASP6
	BCLR.L	#P6IVAL,D0
_HASP6:
	BTST.B	#EXTACT,PER7VR+PERVFL(A2)	;EXTERNAL ACTIVE?
	BEQ.B	_HASP7
	BCLR.L	#P7IVAL,D0
_HASP7:
	BTST.B	#EXTACT,PER8VR+PERVFL(A2)	;EXTERNAL ACTIVE?
	BEQ.B	_HASP8
	BCLR.L	#P8IVAL,D0
_HASP8:
	MOVE.B	D0,InactiveExternals(A2)	;EXTERNAL ACTIVE ENABLED PERIODS

	MOVEQ	#0,D7	;# disabled periods
	MOVEQ	#-1,D0	;ASSUME ALL PERIODS DISABLED
	BTST.B	#ENBPER,PER1FX+PERFFL(A2)
	BEQ.B	HASP1
	BCLR.L	#P1IVAL,D0
	ADDQ.L	#1,D7
HASP1:
	BTST.B	#ENBPER,PER2FX+PERFFL(A2)
	BEQ.B	HASP2
	BCLR.L	#P2IVAL,D0
	ADDQ.L	#1,D7
HASP2:
	BTST.B	#ENBPER,PER3FX+PERFFL(A2)
	BEQ.B	HASP3
	BCLR.L	#P3IVAL,D0
	ADDQ.L	#1,D7
HASP3:
	BTST.B	#ENBPER,PER4FX+PERFFL(A2)
	BEQ.B	HASP4
	BCLR.L	#P4IVAL,D0
	ADDQ.L	#1,D7
HASP4:
	BTST.B	#ENBPER,PER5FX+PERFFL(A2)
	BEQ.B	HASP5
	BCLR.L	#P5IVAL,D0
	ADDQ.L	#1,D7
HASP5:
	BTST.B	#ENBPER,PER6FX+PERFFL(A2)
	BEQ.B	HASP6
	BCLR.L	#P6IVAL,D0
	ADDQ.L	#1,D7
HASP6:
	BTST.B	#ENBPER,PER7FX+PERFFL(A2)
	BEQ.B	HASP7
	BCLR.L	#P7IVAL,D0
	ADDQ.L	#1,D7
HASP7:
	BTST.B	#ENBPER,PER8FX+PERFFL(A2)
	BEQ.B	HASP8
	BCLR.L	#P8IVAL,D0
	ADDQ.L	#1,D7
HASP8:
	MOVE.B	D0,DSBPFL(A2)	;ENABLED PERIODS
;*** 20140128 - don't alarm if only 1 period enabled
	MOVE.B	D7,EnabledPeriodsCount(A2)
;***
	MOVE.B	BADPER(A2),D7	;SETPOINT FLAGS
	OR.L	D0,D7	;UNUSED OR INVALID PERIODS
	;CHECK IF IN ONE OF THE 8 PERIODS
;* January 09,2002
	BTST.B	#ACTIVE,PER1VR+PERVFL(A2)
	BNE.W	DEFLT1
;	BTST.L	#P1IVAL,D7
;	BNE.B	NP1
;	MOVE.W	PER1VR+CSTIME(A2),D3	;START OF PERIOD  
;	MOVE.W	PER1VR+CETIME(A2),D5	;END OF PERIOD
;	JSR	TIMPER
;	BCS.W	DEFLT1
;*
NP1:
;* January 09,2002
	BTST.B	#ACTIVE,PER2VR+PERVFL(A2)
	BNE.W	DEFLT2
;	BTST.B	#ENBPER,PER2FX+PERFFL(A2)
;	BEQ.B	NP2
;	BTST.L	#P2IVAL,D7
;	BNE.B	NP2
;	MOVE.W	PER2VR+CSTIME(A2),D3	;START OF PERIOD  
;	MOVE.W	PER2VR+CETIME(A2),D5	;END OF PERIOD
;	JSR	TIMPER
;	BCS.W	DEFLT2
;*
NP2:
;* January 09,2002
	BTST.B	#ACTIVE,PER3VR+PERVFL(A2)
	BNE.W	DEFLT3
;	BTST.B	#ENBPER,PER3FX+PERFFL(A2)
;	BEQ.B	NP3
;	BTST.L	#P3IVAL,D7
;	BNE.B	NP3
;	MOVE.W	PER3VR+CSTIME(A2),D3	;START OF PERIOD  
;	MOVE.W	PER3VR+CETIME(A2),D5	;END OF PERIOD
;	JSR	TIMPER
;	BCS.W	DEFLT3
;*
NP3:
;* January 09,2002
	BTST.B	#ACTIVE,PER4VR+PERVFL(A2)
	BNE.W	DEFLT4
;	BTST.B	#ENBPER,PER4FX+PERFFL(A2)
;	BEQ.B	NP4
;	BTST.L	#P4IVAL,D7
;	BNE.B	NP4
;	MOVE.W	PER4VR+CSTIME(A2),D3	;START OF PERIOD  
;	MOVE.W	PER4VR+CETIME(A2),D5	;END OF PERIOD
;	JSR	TIMPER
;	BCS.W	DEFLT4
;*
NP4:
;* January 09,2002
	BTST.B	#ACTIVE,PER5VR+PERVFL(A2)
	BNE.W	DEFLT5
;	BTST.B	#ENBPER,PER5FX+PERFFL(A2)
;	BEQ.B	NP5
;	BTST.L	#P5IVAL,D7
;	BNE.B	NP5
;	MOVE.W	PER5VR+CSTIME(A2),D3	;START OF PERIOD  
;	MOVE.W	PER5VR+CETIME(A2),D5	;END OF PERIOD
;	JSR	TIMPER
;	BCS.W	DEFLT5
;*
NP5:
;* January 09,2002
	BTST.B	#ACTIVE,PER6VR+PERVFL(A2)
	BNE.W	DEFLT6
;	BTST.B	#ENBPER,PER6FX+PERFFL(A2)
;	BEQ.B	NP6
;	BTST.L	#P6IVAL,D7
;	BNE.B	NP6
;	MOVE.W	PER6VR+CSTIME(A2),D3	;START OF PERIOD  
;	MOVE.W	PER6VR+CETIME(A2),D5	;END OF PERIOD
;	JSR	TIMPER
;	BCS.W	DEFLT6
NP6:
;* January 09,2002
	BTST.B	#ACTIVE,PER7VR+PERVFL(A2)
	BNE.W	DEFLT7
;	BTST.B	#ENBPER,PER7FX+PERFFL(A2)
;	BEQ.B	NP7
;	BTST.L	#P7IVAL,D7
;	BNE.B	NP7
;	MOVE.W	PER7VR+CSTIME(A2),D3	;START OF PERIOD  
;	MOVE.W	PER7VR+CETIME(A2),D5	;END OF PERIOD
;	JSR	TIMPER
;	BCS.W	DEFLT7
NP7:
;* January 09,2002
	BTST.B	#ACTIVE,PER8VR+PERVFL(A2)
	BNE.W	DEFLT8
;	BTST.B	#ENBPER,PER8FX+PERFFL(A2)
;	BEQ.B	NP8
;	BTST.L	#P8IVAL,D7
;	BNE.B	NP8
;	MOVE.W	PER8VR+CSTIME(A2),D3	;START OF PERIOD  
;	MOVE.W	PER8VR+CETIME(A2),D5	;END OF PERIOD
;	JSR	TIMPER
;	BCS.W	DEFLT8
NP8:
	;CHECK IF IN ONE OF THE 8 RAMPS
	BTST.L	#P1IVAL,D7
	BNE.B	NR1
	BTST.B	#ACTETM,PER1VR+PERVFL(A2)	;END TIME ACTIVE?
	BEQ.B	NR1	
	MOVE.W	PER1VR+CETIME(A2),D3	;START OF RAMP
	MOVE.B	ERAMP1(A2),D2	;END OF RAMP
	BSR.W	CMPEND	;GET RAMP END
	BCS.W	DEFLR1
NR1:	BTST.L	#P2IVAL,D7
	BNE.B	NR2
	BTST.B	#ACTETM,PER2VR+PERVFL(A2)	;END TIME ACTIVE?
	BEQ.B	NR2	
	MOVE.W	PER2VR+CETIME(A2),D3	;START OF RAMP
	MOVE.B	ERAMP2(A2),D2	;END OF RAMP
	BSR.W	CMPEND	;GET RAMP END
	BCS.W	DEFLR2
NR2:	BTST.L	#P3IVAL,D7
	BNE.B	NR3
	BTST.B	#ACTETM,PER3VR+PERVFL(A2)	;END TIME ACTIVE?
	BEQ.B	NR3	
	MOVE.W	PER3VR+CETIME(A2),D3	;START OF RAMP
	MOVE.B	ERAMP3(A2),D2	;END OF RAMP
	BSR.W	CMPEND	;GET RAMP END
	BCS.W	DEFLR3
NR3:	BTST.L	#P4IVAL,D7
	BNE.B	NR4
	BTST.B	#ACTETM,PER4VR+PERVFL(A2)	;END TIME ACTIVE?
	BEQ.B	NR4	
	MOVE.W	PER4VR+CETIME(A2),D3	;START OF RAMP
	MOVE.B	ERAMP4(A2),D2	;END OF RAMP
	BSR.W	CMPEND	;GET RAMP END
	BCS.W	DEFLR4
NR4:	BTST.L	#P5IVAL,D7
	BNE.B	NR5
	BTST.B	#ACTETM,PER5VR+PERVFL(A2)	;END TIME ACTIVE?
	BEQ.B	NR5	
	MOVE.W	PER5VR+CETIME(A2),D3	;START OF RAMP
	MOVE.B	ERAMP5(A2),D2	;END OF RAMP
	BSR.W	CMPEND	;GET RAMP END
	BCS.W	DEFLR5
NR5:	BTST.L	#P6IVAL,D7
	BNE.B	NR6
	BTST.B	#ACTETM,PER6VR+PERVFL(A2)	;END TIME ACTIVE?
	BEQ.B	NR6	
	MOVE.W	PER6VR+CETIME(A2),D3	;START OF RAMP
	MOVE.B	ERAMP6(A2),D2	;END OF RAMP
	BSR.W	CMPEND	;GET RAMP END
	BCS.W	DEFLR6
NR6:	BTST.L	#P7IVAL,D7
	BNE.B	NR7
	BTST.B	#ACTETM,PER7VR+PERVFL(A2)	;END TIME ACTIVE?
	BEQ.B	NR7	
	MOVE.W	PER7VR+CETIME(A2),D3	;START OF RAMP
	MOVE.B	ERAMP7(A2),D2	;END OF RAMP
	BSR.W	CMPEND	;GET RAMP END
	BCS.W	DEFLR7
NR7:	BTST.L	#P8IVAL,D7
	BNE.W	NR8
	BTST.B	#ACTETM,PER8VR+PERVFL(A2)	;END TIME ACTIVE?
	BEQ.B	NR8	
	MOVE.W	PER8VR+CETIME(A2),D3	;END OF PERIOD
	MOVE.B	ERAMP8(A2),D2	;END OF RAMP
	BSR.W	CMPEND	;GET RAMP END
	BCS.W	DEFLR8
NR8:
	;NOT ON A CURRENTLY ACTIVE DAY
	;SCAN BACKWARDS THROUGH THE DAYS OF THE WEEK
	;AND FIND THE NEXT HIGHEST TIME, NOTE THAT WRAPPED
	;DAYS MUST BE CONSIDERED TOO!
	MOVEQ	#7,D3	;CHECK ALL 7 DAYS
	MOVE.W	#0,D5	;LOWEST POSSIBLE TIME
	MOVE.B	C_DYOFWK(A2),D0
	ANDI.L	#DYOFWK_MASK,D0	;IGNORE THE OTHER BITS
CHKPVD:	SUBQ.L	#1,D0	;NEXT DAY
	BCC.B	_GOTD0	;0 TO 6 ONLY
	MOVEQ	#6,D0	;WRAP AROUND TO SUNDAY
_GOTD0:	MOVE.L	D0,D6	;GET THE PREVIOUS DAYS BIT
	SUBQ.L	#1,D6
	BCC.B	_GOTD6
	MOVEQ	#6,D6
_GOTD6:
	
__RMP8:	BTST.L	#P8IVAL,D7
	BNE.B	__RMP7	;PERIOD 8 TIMES INVALID
	BTST.B	D0,PER8FX+DOW(A2)	;ACTIVE PREVIOUS DAY?
	BNE.B	_ACTP8
	BTST.B	#Wraps_Through_Midnight,PER8VR+ETVDOW(A2)
	BEQ.B	__RMP7
	BTST.B	D6,PER8FX+DOW(A2)	;ACTIVE PREVIOUS DAY TO PREVIOUS DAY
	BEQ.B	__RMP7
_ACTP8:	MOVE.W	PER8VR+CETIME(A2),D1
	MOVEQ	#8,D4
	BSR.W	HINXT2
__RMP7:	BTST.L	#P7IVAL,D7
	BNE.B	__RMP6	;PERIOD 7 TIMES INVALID
	BTST.B	D0,PER7FX+DOW(A2)	;ACTIVE NEXT DAY?
	BNE.B	_ACTP7
	BTST.B	#Wraps_Through_Midnight,PER7VR+ETVDOW(A2)
	BEQ.B	__RMP6
	BTST.B	D6,PER7FX+DOW(A2)	;ACTIVE PREVIOUS DAY TO PREVIOUS DAY
	BEQ.B	__RMP6
_ACTP7:	MOVE.W	PER7VR+CETIME(A2),D1
	MOVEQ	#7,D4
	BSR.W	HINXT2
__RMP6:	BTST.L	#P6IVAL,D7
	BNE.B	__RMP5	;PERIOD 6 TIMES INVALID
	BTST.B	D0,PER6FX+DOW(A2)	;ACTIVE NEXT DAY?
	BNE.B	_ACTP6
	BTST.B	#Wraps_Through_Midnight,PER6VR+ETVDOW(A2)
	BEQ.B	__RMP5
	BTST.B	D6,PER6FX+DOW(A2)	;ACTIVE PREVIOUS DAY TO PREVIOUS DAY
	BEQ.B	__RMP5
_ACTP6:	MOVE.W	PER6VR+CETIME(A2),D1
	MOVEQ	#6,D4
	BSR.W	HINXT2
__RMP5:	BTST.L	#P5IVAL,D7
	BNE.B	__RMP4	;PERIOD 5 TIMES INVALID
	BTST.B	D0,PER5FX+DOW(A2)	;ACTIVE NEXT DAY?
	BNE.B	_ACTP5
	BTST.B	#Wraps_Through_Midnight,PER5VR+ETVDOW(A2)
	BEQ.B	__RMP4
	BTST.B	D6,PER5FX+DOW(A2)	;ACTIVE PREVIOUS DAY TO PREVIOUS DAY
	BEQ.B	__RMP4
_ACTP5:	MOVE.W	PER5VR+CETIME(A2),D1
	MOVEQ	#5,D4
	BSR.W	HINXT2
__RMP4:	BTST.L	#P4IVAL,D7
	BNE.B	__RMP3	;PERIOD 4 TIMES INVALID
	BTST.B	D0,PER4FX+DOW(A2)	;ACTIVE NEXT DAY?
	BNE.B	_ACTP4
	BTST.B	#Wraps_Through_Midnight,PER4VR+ETVDOW(A2)
	BEQ.B	__RMP3
	BTST.B	D6,PER4FX+DOW(A2)	;ACTIVE PREVIOUS DAY TO PREVIOUS DAY
	BEQ.B	__RMP3
_ACTP4:	MOVE.W	PER4VR+CETIME(A2),D1
	MOVEQ	#4,D4
	BSR.W	HINXT2
__RMP3:	BTST.L	#P3IVAL,D7
	BNE.B	__RMP2	;PERIOD 3 TIMES INVALID
	BTST.B	D0,PER3FX+DOW(A2)	;ACTIVE NEXT DAY?
	BNE.B	_ACTP3
	BTST.B	#Wraps_Through_Midnight,PER3VR+ETVDOW(A2)
	BEQ.B	__RMP2
	BTST.B	D6,PER3FX+DOW(A2)	;ACTIVE PREVIOUS DAY TO PREVIOUS DAY
	BEQ.B	__RMP2
_ACTP3:	MOVE.W	PER3VR+CETIME(A2),D1
	MOVEQ	#3,D4
	BSR.W	HINXT2
__RMP2:	BTST.L	#P2IVAL,D7
	BNE.B	__RMP1	;PERIOD 2 TIMES INVALID
	BTST.B	D0,PER2FX+DOW(A2)	;ACTIVE NEXT DAY?
	BNE.B	_ACTP2
	BTST.B	#Wraps_Through_Midnight,PER2VR+ETVDOW(A2)
	BEQ.B	__RMP1
	BTST.B	D6,PER2FX+DOW(A2)	;ACTIVE PREVIOUS DAY TO PREVIOUS DAY
	BEQ.B	__RMP1
_ACTP2:	MOVE.W	PER2VR+CETIME(A2),D1
	MOVEQ	#2,D4
	BSR.W	HINXT2
__RMP1:	BTST.L	#P1IVAL,D7
	BNE.B	__RMP0	;PERIOD 1 TIMES INVALID
	BTST.B	D0,PER1FX+DOW(A2)	;ACTIVE NEXT DAY?
	BNE.B	_ACTP1
	BTST.B	#Wraps_Through_Midnight,PER1VR+ETVDOW(A2)
	BEQ.B	__RMP0
	BTST.B	D6,PER1FX+DOW(A2)	;ACTIVE PREVIOUS DAY TO PREVIOUS DAY
	BEQ.B	__RMP0
_ACTP1:	MOVE.W	PER1VR+CETIME(A2),D1
	MOVEQ	#1,D4
	BSR.W	HINXT2
__RMP0:	TST.L	D2	;FOUND A RAMP START?
	BNE.B	_GRST
	SUBQ.L	#1,D3	;TRY PREVIOUS DAY
	BNE.W	CHKPVD
	;COULDN'T FIND ANY START!???
	BRA.W	DEFLT1	;DEFAULT
	
	;FOUND THE START OF THE CURRENT RAMP
_GRST:	MOVE.L	D2,D0	;START
	SUBQ.L	#1,D0	;1 - 8 TO 0 - 7
	LEA	ERAMP1(A2),A0
	MOVE.B	0(A0,D0.L*1),D2	;END OF RAMP
	;'D0' HAS THE RAMP CURRENT IN
	;'D2' HAS THE END OF THE RAMP
	RTS

;#
	;CHECK IF THE TIME IN 'D1' IS THE NEXT LOWEST
HINXT2:	CMP.L	D5,D1
	BLS.B	HINX2X	;IGNORE IF THE SAME
	;IS THE NEXT HIGHEST TIME
	MOVE.L	D1,D5	;NEW NEXT HIGHEST
	MOVE.L	D4,D2	;PERIOD OF NEXT HIGHEST
HINX2X:	RTS





;##
	;GET THE RAMP END FOR THE NUMBER IN 'D2.B'
	;RETURN WITH SKIP FLAG SET IF OUTSIDE RAMP
CMPEND:	ANDI.L	#$0000000F,D2	;END FLAGS
	BEQ.B	CMPEDX
	MOVE.W	PER1VR+CSTIME(A2),D5
	CMPI.L	#1,D2
	BEQ.B	CMPEDC
	MOVE.W	PER2VR+CSTIME(A2),D5
	CMPI.L	#2,D2
	BEQ.B	CMPEDC
	MOVE.W	PER3VR+CSTIME(A2),D5
	CMPI.L	#3,D2
	BEQ.B	CMPEDC
	MOVE.W	PER4VR+CSTIME(A2),D5
	CMPI.L	#4,D2
	BEQ.B	CMPEDC
	MOVE.W	PER5VR+CSTIME(A2),D5
	CMPI.L	#5,D2
	BEQ.B	CMPEDC
	MOVE.W	PER6VR+CSTIME(A2),D5
	CMPI.L	#6,D2
	BEQ.B	CMPEDC
	MOVE.W	PER7VR+CSTIME(A2),D5
	CMPI.L	#7,D2
	BEQ.B	CMPEDC
	MOVE.W	PER8VR+CSTIME(A2),D5
	CMPI.L	#8,D2
	BEQ.B	CMPEDC
CMPEDX:	MOVE.W	#0,CCR	;CLEAR CARRY FLAG
	RTS
CMPEDC:	JMP	TIMPER


;####
	;CALCULATE THE SETPOINTS
SETPTC:	LEA	SET1FX(A2),A4
	LEA	SET1VR(A2),A3
	BSR.B	CLSTPT	;CALCULATE SETPOINT
	LEA	SET2FX(A2),A4
	LEA	SET2VR(A2),A3
	BSR.B	CLSTPT	;CALCULATE SETPOINT
	LEA	SET3FX(A2),A4
	LEA	SET3VR(A2),A3
	BSR.B	CLSTPT	;CALCULATE SETPOINT
	LEA	SET4FX(A2),A4
	LEA	SET4VR(A2),A3
	BSR.B	CLSTPT	;CALCULATE SETPOINT
	LEA	SET5FX(A2),A4
	LEA	SET5VR(A2),A3
	BSR.B	CLSTPT	;CALCULATE SETPOINT
	LEA	SET6FX(A2),A4
	LEA	SET6VR(A2),A3
	BSR.B	CLSTPT	;CALCULATE SETPOINT
	LEA	SET7FX(A2),A4
	LEA	SET7VR(A2),A3
	BSR.B	CLSTPT	;CALCULATE SETPOINT
	LEA	SET8FX(A2),A4
	LEA	SET8VR(A2),A3

;####
	;ON ENTRY 'A4' POINTS TO THE SETPOINT SETTTINGS
	;AND 'A3' POINTS TO THE SETPOINT VARIABLES
CLSTPT:

;*** 20140128 - don't ramp alarm if only 1 period enabled
	MOVEQ	#0,D7
	MOVE.B	EnabledPeriodsCount(A2),D7
	MOVEQ	#1,D0
	CMP.L	D0,D7	;7 (or 8) disabled periods?
	BHI.B	_NotSinglePeriod	;If only 1 (or 0) enabled... clear ramp alarms
	MOVEQ	#-1,D7	;all Invalid
	BRA.B	_IfSinglePeriod
_NotSinglePeriod:
;***

	MOVE.B	DSBPFL(A2),D0	;GET DISABLED PERIODS
	MOVE.B	BADPER(A2),D7	;GET INVALID PERIODS
	OR.L	D0,D7	;DISABLED OR INVALID PERIODS
_IfSinglePeriod:
;* JAN 13,2003
	BSR.W	RRATE1	;CALCULATE RAMP RATE 1
	BSR.W	RRATE2	;CALCULATE RAMP RATE 2
	BSR.W	RRATE3	;CALCULATE RAMP RATE 3
	BSR.W	RRATE4	;CALCULATE RAMP RATE 4
	BSR.W	RRATE5	;CALCULATE RAMP RATE 5
	BSR.W	RRATE6	;CALCULATE RAMP RATE 6
	BSR.W	RRATE7	;CALCULATE RAMP RATE 7
	BSR.W	RRATE8	;CALCULATE RAMP RATE 8

	TST.B	oSTIMER(A6)	;FULLY POWERED UP?
	BNE.B	NO_RAMP_ALARMS	;JUST POWERING UP

;;*** 20140128 - don't alarm if only 1 period enabled
;	MOVEQ	#0,D1
;	MOVE.B	EnabledPeriodsCount(A2),D1
;	MOVEQ	#1,D0
;	CMP.L	D0,D1	;7 (or 8) disabled periods?
;	BLS.B	NO_RAMP_ALARMS	;only 1 (or 0) enabled... clear ramp alarms
;;***

	;CREATE THE RAMP RATE ALARM
	MOVEQ	#0,D0
	MOVE.B	RMPALM(A3),D0	;GET RAMP ALARMS
	BEQ.B	NO_RAMP_ALARMS
	;HAS A RAMP ALARM	
	MOVE.B	RMPHAL(A3),D1	;HAD ALARMS
	OR.L	D0,D1	;MERGE WITH CURRENT
	MOVE.B	D1,RMPHAL(A3)		
	BEQ.B	NO_RAMP_ALARMS
	MOVE.B	RMPALP(A4),D0	;GET THE ALARM PRIOIRITY
	ANDI.L	#Alarm_Priority_Mask,D0
	BEQ.B	CLEAR_RAMP_ALARMS ;CLEAR THE ALARMS
	MOVE.L	LOCAL_ALARMS_BUILDING(A2),D1
	BSET.L	D0,D1	;SET THE ALARM'S PRIORITY
	MOVE.L	D1,LOCAL_ALARMS_BUILDING(A2)
	BRA.B	NO_RAMP_ALARMS
	;	
CLEAR_RAMP_ALARMS:
	CLR.B	RMPHAL(A3)	;NO ALARMS USED
	BRA.B	NO_RAMP_HAD_ALARMS
	;
NO_RAMP_ALARMS:
	TST.B	RMPHAL(A3)	;ANY HAD ALARMS?
	BEQ.B	NO_RAMP_HAD_ALARMS
	MOVE.L	LOCAL_ALARMS_BUILDING(A2),D1
	BSET.L	#Had_An_Alarm_Priority,D1	;SET THE ALARM'S PRIORITY
	MOVE.L	D1,LOCAL_ALARMS_BUILDING(A2)
NO_RAMP_HAD_ALARMS:	
	;
	;
	BRA.W	CLCSET	;CALCULATE THE SETPOINT

;	MOVEQ	#0,D0	;CLEAR UPPER BYTES
;	MOVE.B	SPTVEC(A3),D0	;SETPOINT CALC. VECTOR
;	ADDQ.L	#1,D0
;	CMPI.L	#_NSPVC,D0
;	BLO.B	_CLSTP
;	MOVEQ	#0,D0
;_CLSTP:	MOVE.B	D0,SPTVEC(A3)
;	align	4
;	JMP	2(PC,D0.L*4)
;
;_SPSVC:	BRA.W	RRATE1	;CALCULATE RAMP RATE 1
;	BRA.W	RRATE2	;CALCULATE RAMP RATE 2
;	BRA.W	RRATE3	;CALCULATE RAMP RATE 3
;	BRA.W	RRATE4	;CALCULATE RAMP RATE 4
;	BRA.W	RRATE5	;CALCULATE RAMP RATE 5
;	BRA.W	RRATE6	;CALCULATE RAMP RATE 6
;	BRA.W	RRATE7	;CALCULATE RAMP RATE 7
;	BRA.W	RRATE8	;CALCULATE RAMP RATE 8
;	BRA.W	CLCSET	;CALCULATE THE SETPOINT
;_SPEVC:
;_NSPVC:	EQU	(_SPEVC-_SPSVC)/4
;
;*

;####
	;CHECK RAMP 1
RRATE1:	BTST.L	#P1IVAL,D7	;PERIOD VALID?
	BNE.B	NRMPA1
	BSR.W	GETR1D	;GET RAMP DATA
	BSR.W	CLCRMP	;CALCULATE RAMP
	BCC.B	NRMPA1	;RAMP OKAY
	;RAMP RATE IS TOO FAST
	BSET.B	#RP1ALM,RMPALM(A3)
	RTS
	;
NRMPA1:	BCLR.B	#RP1ALM,RMPALM(A3)
	RTS
	

;####
	;CHECK RAMP RATE 2
RRATE2:	BTST.L	#P2IVAL,D7	;PERIOD VALID?
	BNE.B	NRMPA2
	BSR.W	GETR2D	;GET RAMP DATA
	BSR.W	CLCRMP	;CALCULATE RAMP
	BCC.B	NRMPA2	;RAMP OKAY
	;RAMP RATE IS TOO FAST
	BSET.B	#RP2ALM,RMPALM(A3)
	RTS
	;
NRMPA2:	BCLR.B	#RP2ALM,RMPALM(A3)
	RTS

;####
	;CHECK RAMP RATE 3
RRATE3:	BTST.L	#P3IVAL,D7	;PERIOD VALID?
	BNE.B	NRMPA3
	BSR.W	GETR3D	;GET RAMP DATA
	BSR.W	CLCRMP	;CALCULATE RAMP
	BCC.B	NRMPA3	;RAMP OKAY
	;RAMP RATE IS TOO FAST
	BSET.B	#RP3ALM,RMPALM(A3)
	RTS
	;
NRMPA3:	BCLR.B	#RP3ALM,RMPALM(A3)
	RTS

;####
	;CHECK RAMP RATE 4
RRATE4:	BTST.L	#P4IVAL,D7	;PERIOD VALID?
	BNE.B	NRMPA4
	BSR.W	GETR4D	;GET RAMP DATA
	BSR.W	CLCRMP	;CALCULATE RAMP
	BCC.B	NRMPA4	;RAMP OKAY
	;RAMP RATE IS TOO FAST
	BSET.B	#RP4ALM,RMPALM(A3)
	RTS
	;
NRMPA4:	BCLR.B	#RP4ALM,RMPALM(A3)
	RTS


;####
	;CHECK RAMP RATE 5
RRATE5:	BTST.L	#P5IVAL,D7	;PERIOD VALID?
	BNE.B	NRMPA5
	BSR.W	GETR5D	;GET RAMP DATA
	BSR.B	CLCRMP	;CALCULATE RAMP
	BCC.B	NRMPA5	;RAMP OKAY
	;RAMP RATE IS TOO FAST
	BSET.B	#RP5ALM,RMPALM(A3)
	RTS
	;
NRMPA5:	BCLR.B	#RP5ALM,RMPALM(A3)
	RTS


;####
	;CHECK RAMP RATE 6
RRATE6:	BTST.L	#P6IVAL,D7	;PERIOD VALID?
	BNE.B	NRMPA6
	BSR.W	GETR6D	;GET RAMP DATA
	BSR.B	CLCRMP	;CALCULATE RAMP
	BCC.B	NRMPA6	;RAMP OKAY
	;RAMP RATE IS TOO FAST
	BSET.B	#RP6ALM,RMPALM(A3)
	RTS
	;
NRMPA6:	BCLR.B	#RP6ALM,RMPALM(A3)
	RTS


;####
	;CHECK RAMP RATE 7
RRATE7:	BTST.L	#P7IVAL,D7	;PERIOD VALID?
	BNE.B	NRMPA7
	BSR.W	GETR7D	;GET RAMP DATA
	BSR.B	CLCRMP	;CALCULATE RAMP
	BCC.B	NRMPA7	;RAMP OKAY
	;RAMP RATE IS TOO FAST
	BSET.B	#RP7ALM,RMPALM(A3)
	RTS
	;
NRMPA7:	BCLR.B	#RP7ALM,RMPALM(A3)
	RTS


;####
	;CHECK RAMP RATE 8
RRATE8:	BTST.L	#P8IVAL,D7	;PERIOD VALID?
	BNE.B	NRMPA8
	BSR.W	GETR8D	;GET RAMP DATA
	BSR.B	CLCRMP	;CALCULATE RAMP
	BCC.B	NRMPA8	;RAMP OKAY
	;RAMP RATE IS TOO FAST
	BSET.B	#RP8ALM,RMPALM(A3)
	RTS
	;
NRMPA8:	BCLR.B	#RP8ALM,RMPALM(A3)
	RTS


;###
	;THIS ROUTINE CALCULATES THE RAMP RATE
	;ON ENTRY 'D0.B' HAS END RAMP PERIOD
	;'D1.L' HAS START SETPOINT
	;'D3.L' HAS START RAMP TIME
	;ON EXIT THE CARRY FLAG IS SET IF THE RAMP RATE
	;IS TOO HIGH
CLCRMP:	BSR.B	CLCRRT	;CALCULATE RAMP RATE
	BCC.B	UPWRAT	;UPWARD RATE
	;IS A DOWNWARD SLOPE RATE
	;CURRENT RAMP RATE IS IN 'D3'
	MOVE.W	DRATE(A4),D1	;DOWNWARD RATE
CLRMPC:	EXT.L	D1
	BEQ.B	_ZeroIsDisableAlarm
	;THE RAMP RATE IS ALREADY AN ABSOLUTE VALUE
	;MAKE SURE THAT THE OPERATOR ENTRY IS AS WELL
	BPL.B	ALREADY_POSITIVE
	NEG.L	D1	;ABSOLUTE VALUE (POSITIVE)
ALREADY_POSITIVE:
	CMP.L	D3,D1
_ZeroIsDisableAlarm:
	RTS		;SETS CARRY IF RAMP RATE TOO HIGH
	;
	;IS AN UPWARD SLOPE RATE
UPWRAT:	MOVE.W	URATE(A4),D1	;UPWARD RATE
	BRA.B	CLRMPC

	;THIS ROUTINE CALCULATES THE RAMP RATE
	;ON ENTRY 'D0.B' HAS END RAMP PERIOD
	;'D1' HAS START SETPOINT
	;'D3' HAS START RAMP TIME
	;ON EXIT 'D3' HAS THE RAMP RATE
	;THE CARRY FLAG IS SET IF IT IS A NEGATIVE RAMP.
CLCRRT:	BSR.B	CLCDIF
	SUB.L	D5,D1	;SETPOINTS: END - START
;*** 20150417 signed # support
;	BCS.B	DWNRMP
	BLT.B	DWNRMP
;***
	;IS AN UPWARD RAMP
	MOVE.L	D1,D0
	MOVE.L	D3,D1
	MOVE.L	D0,D3	;SWAP WITH TOTAL TIME
;;*** 20150417 signed # support
	MULU.W	#60,D3	;END-START * 60 MINUTES
	JSR	DIV32
;	MULS.W	#60,D3	;END-START * 60 MINUTES
;	JSR	DIVS32	;signed D3.L / unsigned D1.W ==> signed D3.W
;;***
	;'D3.W' HAS THE RAMP RATE
	MOVE.W	#0,CCR	;CLEAR THE CARRY FLAG
	RTS
	;
	;IS DOWNWARDS RAMP
DWNRMP:	NEG.L	D1
	MOVE.L	D1,D0
	MOVE.L	D3,D1
	MOVE.L	D0,D3	;SWAP WITH TOTAL TIME
;;*** 20150417 signed # support
	MULU.W	#60,D3	;END-START * 60 MINUTES
	JSR	DIV32
;	MULS.W	#60,D3	;END-START * 60 MINUTES
;	JSR	DIVS32	;signed D3.L / unsigned D1.W ==> signed D3.W
;;***
	;'D3.W' HAS THE RAMP RATE
	MOVE.W	#C,CCR	;SET THE CARRY FLAG
	RTS

;##
	;ON ENTRY 'D0.B' HAS END RAMP PERIOD
	;'D1' HAS START SETPOINT
	;'D3' HAS START RAMP TIME
	;NOTE: THE SETPOINT IS A WORD
CLCDIF:	EXT.L	D1	;SIGN EXTEND START SETPOINT
	MOVE.L	D1,D5	;COPY OF START RAMP SETPOINT
	EXT.L	D3	;CLEAR UPPER WORD
	MOVE.L	D0,D6	;GET A COPY
	MOVEQ	#$0F,D2
	AND.L	D2,D0	;CLEAR UPPER PORTION
	LSR.L	#4,D6	;GET THE NUMBER OF DAYS BETWEEN
	AND.L	D2,D6	;CLEAR UPPER PORTION
	MOVE.L	D3,D2	;START RAMP TIME
	MOVE.L	D1,D4	;START RAMP SETPOINT
	;GET END RAMP TIME
	LEA	PER1VR+CSTIME(A2),A0
	MOVEQ	#0,D1
	MOVE.B	D0,D1	;GET A COPY
	BEQ.B	GOT000	;USE FIRST
	CMPI.L	#8,D0
	BLS.B	OK000	;1-8 ONLY
	MOVEQ	#1,D1	;DEFAULT
OK000:	SUBQ.L	#1,D1
GOT000:	MOVE.L	D1,-(A7)
	MULU.W	#PERVRL,D1
	MOVE.W	CSTIME(A0,D1.L*1),D1
	;'D3' HAS THE START RAMP TIME
	;'D1' HAS THE END RAMP TIME
	;'D6' HAS THE NUMBER OF DAYS BETWEEN
	CMP.L	D1,D3
	BLO.B	NO_ADJUSTMENT
	SUBQ.L	#1,D6
	BCC.B	NO_ADJUSTMENT
	MOVEQ	#0,D6	
NO_ADJUSTMENT:
	BSR.W	CNVTIM
	;NOW ADD 24 HOURS FOR EVERY DAY BETWEEN ('D6')
	MULU.W	#24*60,D6
	ADD.L	D6,D3	;NUMBER OF MINUTES BETWEEN
			;RAMP START AND RAMP END
	;GET END RAMP SETPOINT
	MOVE.L	(A7)+,D1
	MOVE.W	RDG(A3,D1.L*4),D1
	EXT.L	D1	;SIGN EXTEND
	;'D5.L' HAS START SETPOINT
	;'D1.L' HAS END SETPOINT
	;'D3.L' HAS # MINUTES BETWEEN START & END RAMP TIMES
	RTS


;####
	;CALCULATE THE SETPOINT FOR THE CURRENT TIME
CLCSET:	BSR.B	CLSETP
	MOVE.W	D1,CLCSPT(A3)	;CALCULATED SETPOINT
	MOVE.W	D3,CRMPRT(A3)	;CALCULATED RAMP RATE
	RTS
	;
	;
CLSETP:	MOVEQ	#0,D0
	MOVE.B	SETPON(A2),D0 ;GET SETPOINT ON
	CMPI.L	#_CLVEC,D0
	BLO.B	_CLSET
	MOVEQ	#0,D0	;DEFAULT
	align	4
_CLSET:	JMP	2(PC,D0.L*4)




	;SETPOINT IS A WORD
_CLSVC:	BRA.W	INRMP1	;IN RAMP 1
	BRA.W	INRMP2	;IN RAMP 2
	BRA.W	INRMP3	;IN RAMP 3
	BRA.W	INRMP4	;IN RAMP 4
	BRA.W	INRMP5	;IN RAMP 5
	BRA.W	INRMP6	;IN RAMP 6
	BRA.W	INRMP7	;IN RAMP 7
	BRA.W	INRMP8	;IN RAMP 8
	BRA.W	INPER1	;IN PERIOD 1
	BRA.W	INPER2	;IN PERIOD 2
	BRA.W	INPER3	;IN PERIOD 3
	BRA.W	INPER4	;IN PERIOD 4
	BRA.W	INPER5	;IN PERIOD 5
	BRA.W	INPER6	;IN PERIOD 6
	BRA.W	INPER7	;IN PERIOD 7
	BRA.W	INPER8	;IN PERIOD 8
_CLEVC:
_CLVEC:	EQU	(_CLEVC-_CLSVC)/4

INPER1:	MOVE.W	RDG+(4*0)(A3),D1
SPCOM2:	MOVEQ	#0,D3	;ZERO CHANGE RATE
	RTS

INPER2:	MOVE.W	RDG+(4*1)(A3),D1
	BRA.B	SPCOM2

INPER3:	MOVE.W	RDG+(4*2)(A3),D1
	BRA.B	SPCOM2

INPER4:	MOVE.W	RDG+(4*3)(A3),D1
	BRA.B	SPCOM2

INPER5:	MOVE.W	RDG+(4*4)(A3),D1
	BRA.B	SPCOM2

INPER6:	MOVE.W	RDG+(4*5)(A3),D1
	BRA.B	SPCOM2

INPER7:	MOVE.W	RDG+(4*6)(A3),D1
	BRA.B	SPCOM2

INPER8:	MOVE.W	RDG+(4*7)(A3),D1
	BRA.B	SPCOM2

	;IN RAMP 1
INRMP1:	BSR.B	GETR1D	;RAMP 1 SETTINGS
	BSR.W	CLCRSP	;CALCULATE RAMP SETPOINT
	MOVE.L	D1,D7
	BSR.B	GETR1D
RRATCM:	BSR.W	CLCRRT	;GET RAMP RATE
	BCC.B	SPCOM3	;RAMP IS POSITIVE
	;RAMP IS NEGATIVE	;PRODUCE NEGATIVE NUMBER
	NEG.L	D3
SPCOM3:	MOVE.L	D7,D1	;CALCULATED SETPOINT
	RTS
	;
	;RETURN SETTINGS/DATA FOR PERIOD 1
GETR1D:	MOVE.B	ERAMP1(A2),D0 ;END RAMP PERIOD
	MOVE.W	PER1VR+CETIME(A2),D3 ;START OF RAMP 1
	MOVE.W	RDG+(4*0)(A3),D1
	RTS

	;IN RAMP 2
INRMP2:	BSR.B	GETR2D	;RAMP 2 SETTINGS
	BSR.W	CLCRSP	;CALCULATE RAMP SETPOINT
	MOVE.L	D1,D7
	BSR.B	GETR2D
	BRA.B	RRATCM
	;
	;RETURN SETTINGS/DATA FOR PERIOD 2
GETR2D:	MOVE.B	ERAMP2(A2),D0 ;END RAMP PERIOD
	MOVE.W	PER2VR+CETIME(A2),D3 ;START OF RAMP
	MOVE.W	RDG+(4*1)(A3),D1
	RTS

	;IN RAMP 3
INRMP3:	BSR.B	GETR3D	;RAMP 3 SETTINGS
	BSR.W	CLCRSP	;CALCULATE RAMP SETPOINT
	MOVE.L	D1,D7
	BSR.B	GETR3D
	BRA.B	RRATCM
	;
	;RETURN SETTINGS/DATA FOR PERIOD 3
GETR3D:	MOVE.B	ERAMP3(A2),D0 ;END RAMP PERIOD
	MOVE.W	PER3VR+CETIME(A2),D3 ;START OF RAMP
	MOVE.W	RDG+(4*2)(A3),D1
	RTS

	;IN RAMP 4
INRMP4:	BSR.B	GETR4D	;RAMP 4 SETTINGS
	BSR.W	CLCRSP	;CALCULATE RAMP SETPOINT
	MOVE.L	D1,D7
	BSR.B	GETR4D
	BRA.B	RRATCM
	;
	;RETURN SETTINGS/DATA FOR PERIOD 4
GETR4D:	MOVE.B	ERAMP4(A2),D0 ;END RAMP PERIOD
	MOVE.W	PER4VR+CETIME(A2),D3 ;START OF RAMP
	MOVE.W	RDG+(4*3)(A3),D1
	RTS

	;IN RAMP 5
INRMP5:	BSR.B	GETR5D	;RAMP 4 SETTINGS
	BSR.W	CLCRSP	;CALCULATE RAMP SETPOINT
	MOVE.L	D1,D7
	BSR.B	GETR5D
	BRA.B	RRATCM
	;
	;RETURN SETTINGS/DATA FOR PERIOD 4
GETR5D:	MOVE.B	ERAMP5(A2),D0 ;END RAMP PERIOD
	MOVE.W	PER5VR+CETIME(A2),D3 ;START OF RAMP
	MOVE.W	RDG+(4*4)(A3),D1
	RTS

	;IN RAMP 6
INRMP6:	BSR.B	GETR6D	;RAMP 4 SETTINGS
	BSR.W	CLCRSP	;CALCULATE RAMP SETPOINT
	MOVE.L	D1,D7
	BSR.B	GETR6D
	BRA.W	RRATCM
	;
	;RETURN SETTINGS/DATA FOR PERIOD 4
GETR6D:	MOVE.B	ERAMP6(A2),D0 ;END RAMP PERIOD
	MOVE.W	PER6VR+CETIME(A2),D3 ;START OF RAMP
	MOVE.W	RDG+(4*5)(A3),D1
	RTS

	;IN RAMP 7
INRMP7:	BSR.B	GETR7D	;RAMP 4 SETTINGS
	BSR.W	CLCRSP	;CALCULATE RAMP SETPOINT
	MOVE.L	D1,D7
	BSR.B	GETR7D
	BRA.W	RRATCM
	;
	;RETURN SETTINGS/DATA FOR PERIOD 4
GETR7D:	MOVE.B	ERAMP7(A2),D0 ;END RAMP PERIOD
	MOVE.W	PER7VR+CETIME(A2),D3 ;START OF RAMP
	MOVE.W	RDG+(4*6)(A3),D1
	RTS

	;IN RAMP 8
INRMP8:	BSR.B	GETR8D	;RAMP 4 SETTINGS
	BSR.W	CLCRSP	;CALCULATE RAMP SETPOINT
	MOVE.L	D1,D7
	BSR.B	GETR8D
	BRA.W	RRATCM
	;
	;RETURN SETTINGS/DATA FOR PERIOD 4
GETR8D:	MOVE.B	ERAMP8(A2),D0 ;END RAMP PERIOD
	MOVE.W	PER8VR+CETIME(A2),D3 ;START OF RAMP
	MOVE.W	RDG+(4*7)(A3),D1
	RTS


	;THIS ROUTINE CALCULATES THE RAMP RATE
	;ON ENTRY 'D0.B' HAS END RAMP PERIOD
	;'D1' HAS START SETPOINT
	;'D3' HAS START RAMP TIME
	;NOTE: THE SETPOINT IS A WORD
CLCRSP:	BSR.W	CLCDIF
	;
	;'D3' HAS THE TOTAL RAMP PERIOD TIME
	;'D5' HAS THE START SETPOINT
	;'D1' HAS THE END SETPOINT
	MOVE.L	D5,D6	;copy of Start Setpoint
	MOVE.L	D1,D7	;copy of End Setpoint
	SUB.L	D5,D1	;GET SETPOINT CHANGE
	BMI.B	DWNSET
	;IS AN UPWARD SLOPE
	BSR.B	STPDIF	;GET CHANGE
	JSR	ADDSGN
	;Limit the results between D6 and D7
	;'D6' HAS THE START SETPOINT
	;'D7' HAS THE END SETPOINT
	CMP.L	D6,D1
	BLT.B	UseD6
	CMP.L	D7,D1
	BGT.B	UseD7
	RTS
UseD6:	MOVE.L	D6,D1
	RTS
UseD7:	MOVE.L	D7,D1
	RTS
	;
	;IS DOWNWARDS SLOPE
DWNSET:	NEG.L	D1	;MAKE DIFFERENCE POSITIVE
	BSR.B	STPDIF	;GET CHANGE
	JSR	SUBSGN
	;Limit the results between D6 and D7
	;'D6' HAS THE START SETPOINT
	;'D7' HAS THE END SETPOINT
	CMP.L	D7,D1
	BLT.B	UseD7
	CMP.L	D6,D1
	BGT.B	UseD6
	RTS
	;
	;COMMON CALCULATION
	;NOTE: THIS ROUTINE ONLY WORKS WITH POSITIVE VALUES
STPDIF:	MOVE.L	D3,D0	;TOTAL TIME IN MINUTES
	MOVE.L	D1,D5	;SETPOINT CHANGE
	MOVE.W	C_HOUR(A2),D1	;GET CURRENT TIME
	MOVE.L	D2,D3	;START RAMP TIME
	BSR.B	CNVTIM
	MULU.W	D5,D3	;* SETPOINT CHANGE
	MOVE.L	D0,D1	;TOTAL TIME IN MINUTES
	JSR	DIV32
	;'D3' HAS THE TEMPERATURE CHANGE
	MOVE.L	D4,D1	;BASE SETPOINT
	RTS


	;SUBTRACT THE TIME IN 'D3' FROM 'D1'
	;AND CONVERT TO BINARY MINUTES
	;AND RETURN IN 'D3'
CNVTIM:	JSR	SUBTME
	MOVEQ	#0,D3
	MOVE.B	D1,D3	;HOLD MINUTES
	LSR.L	#8,D1	;GET THE HOURS
	MULU.W	#60,D1	;* 60 MINUTES/HOUR
	ADD.L	D1,D3	;ADD TO MINUTES
	RTS


;###########
	;THIS IS RUN IF THE DEVICE'S MEMORY BLOCK CRC GOES BAD
	;BASICALLY FORCE ALL PRODUCED VALUES TO FAILED!
DEVICE_FAULT:
	MOVE.W	#FAIL16,D1	;16 BIT FAILED VALUE
	MOVE.W	D1,SET1VR+CLCSPT(A2) ;SET TO DESIRED
	MOVE.W	D1,SET2VR+CLCSPT(A2) ;SET TO DESIRED
	MOVE.W	D1,SET3VR+CLCSPT(A2) ;SET TO DESIRED
	MOVE.W	D1,SET4VR+CLCSPT(A2) ;SET TO DESIRED
	MOVE.W	D1,SET5VR+CLCSPT(A2) ;SET TO DESIRED
	MOVE.W	D1,SET6VR+CLCSPT(A2) ;SET TO DESIRED
	MOVE.W	D1,SET7VR+CLCSPT(A2) ;SET TO DESIRED
	MOVE.W	D1,SET8VR+CLCSPT(A2) ;SET TO DESIRED
	;
	force_reload_alarm
	RTS


;###################
	;
	;THIS ROUTINE IS CALLED DURING POWER UP
	;
PWR_UP:	RTS


;###################
	;RUN WHEN OPERATOR MANUALLY RESETS THE CONTROLLER
RESETP:	BSR.B	RETLNG	;GET VALUES
	JMP	CLRVAR


;###################
	;RETURN VARIOUS LENGTHS AND POINTERS
RETLNG:	LEA	DEVVST(A2),A0	;START OF VARIABLES
	MOVE.L	#DVLENG,D1	;VARIABLES LENGTH
	MOVE.L	#DEVVST,D0	;CHECKSUM LENGTH
	RTS


;###################
	;
	;THIS ROUTINE IS CALLED DURING APPLICATION BACKGROUND
	;
	;ON ENTRY 'D7' HAS THE DEVICE PROGRAM ON
BACKGD:	check_settings_CRC
	BNE.B	xBACKGD	;Already ran this second
	;NOW CHECK IF OPERATOR WANTS TO CLEAR
	;ALL THE LATCHED ALARM FLAGS
	BCLR.B	#Clear_All_Device_Program_Alarms,DEVVFL(A2)
	BEQ.B	CLEAR_LATCHED_ALARMS_EXIT
	CLR.B	SET1VR+RMPHAL(A2)
	CLR.B	SET2VR+RMPHAL(A2)
	CLR.B	SET3VR+RMPHAL(A2)
	CLR.B	SET4VR+RMPHAL(A2)
	CLR.B	SET5VR+RMPHAL(A2)
	CLR.B	SET6VR+RMPHAL(A2)
	CLR.B	SET7VR+RMPHAL(A2)
	CLR.B	SET8VR+RMPHAL(A2)
	CLR.B	PERHAL(A2)
	MOVEQ	#0,D0	;Flag that ran the background code
CLEAR_LATCHED_ALARMS_EXIT:
xBACKGD:
	RTS
	

;###################
	;THIS ROUTINE IS CALLED BY QUASI-BACKGROUND
	;AND INITIATES ANY MESSAGING
UNSMSG:	LEA	SETPXR,A4	;LIST OF READINGS FOR THIS PROGRAM
	LEA	REQTMR(A2),A1	;POINT TO REQUEST TIMERS
	JMP	XMODA4	;GET ANY CROSS MODULE READINGS


;###################
	;THIS IS A LIST OF THE READINGS WITHIN THIS
	;CONTROLLER THAT ARE EXTERNALLY DEFINED
	;THESE READINGS CAN COME FROM A DEVICE PROGRAM
	;EITHER LOCALLY OR OVER THE NETWORK
SETPXR:
DEV_XR:	DC.W	N_PTRS
	;SETPOINT 1
	DC.W	SET1FX+SETPTS+(4*0)	;SETPOINT 1 PERIOD 1 OVERRIDE VALUE
	DC.W	SET1VR+RDG+(4*0)	;CAN ONLY BE A WORD
	DC.W	SET1FX+SETPTS+(4*1)	;SETPOINT 1 PERIOD 2 OVERRIDE VALUE
	DC.W	SET1VR+RDG+(4*1)	;CAN ONLY BE A WORD
	DC.W	SET1FX+SETPTS+(4*2)	;SETPOINT 1 PERIOD 3 OVERRIDE VALUE
	DC.W	SET1VR+RDG+(4*2)	;CAN ONLY BE A WORD
	DC.W	SET1FX+SETPTS+(4*3)	;SETPOINT 1 PERIOD 4 OVERRIDE VALUE
	DC.W	SET1VR+RDG+(4*3)	;CAN ONLY BE A WORD
	DC.W	SET1FX+SETPTS+(4*4)	;SETPOINT 1 PERIOD 5 OVERRIDE VALUE
	DC.W	SET1VR+RDG+(4*4)	;CAN ONLY BE A WORD
	DC.W	SET1FX+SETPTS+(4*5)	;SETPOINT 1 PERIOD 6 OVERRIDE VALUE
	DC.W	SET1VR+RDG+(4*5)	;CAN ONLY BE A WORD
	DC.W	SET1FX+SETPTS+(4*6)	;SETPOINT 1 PERIOD 7 OVERRIDE VALUE
	DC.W	SET1VR+RDG+(4*6)	;CAN ONLY BE A WORD
	DC.W	SET1FX+SETPTS+(4*7)	;SETPOINT 1 PERIOD 8 OVERRIDE VALUE
	DC.W	SET1VR+RDG+(4*7)	;CAN ONLY BE A WORD
	;SETPOINT 2
	DC.W	SET2FX+SETPTS+(4*0)	;PERIOD 1 OVERRIDE VALUE
	DC.W	SET2VR+RDG+(4*0)	;CAN ONLY BE A WORD
	DC.W	SET2FX+SETPTS+(4*1)	;PERIOD 2 OVERRIDE VALUE
	DC.W	SET2VR+RDG+(4*1)	;CAN ONLY BE A WORD
	DC.W	SET2FX+SETPTS+(4*2)	;PERIOD 3 OVERRIDE VALUE
	DC.W	SET2VR+RDG+(4*2)	;CAN ONLY BE A WORD
	DC.W	SET2FX+SETPTS+(4*3)	;PERIOD 4 OVERRIDE VALUE
	DC.W	SET2VR+RDG+(4*3)	;CAN ONLY BE A WORD
	DC.W	SET2FX+SETPTS+(4*4)	;PERIOD 5 OVERRIDE VALUE
	DC.W	SET2VR+RDG+(4*4)	;CAN ONLY BE A WORD
	DC.W	SET2FX+SETPTS+(4*5)	;PERIOD 6 OVERRIDE VALUE
	DC.W	SET2VR+RDG+(4*5)	;CAN ONLY BE A WORD
	DC.W	SET2FX+SETPTS+(4*6)	;PERIOD 7 OVERRIDE VALUE
	DC.W	SET2VR+RDG+(4*6)	;CAN ONLY BE A WORD
	DC.W	SET2FX+SETPTS+(4*7)	;PERIOD 8 OVERRIDE VALUE
	DC.W	SET2VR+RDG+(4*7)	;CAN ONLY BE A WORD
	;SETPOINT 3
	DC.W	SET3FX+SETPTS+(4*0)	;PERIOD 1 OVERRIDE VALUE
	DC.W	SET3VR+RDG+(4*0)	;CAN ONLY BE A WORD
	DC.W	SET3FX+SETPTS+(4*1)	;PERIOD 2 OVERRIDE VALUE
	DC.W	SET3VR+RDG+(4*1)	;CAN ONLY BE A WORD
	DC.W	SET3FX+SETPTS+(4*2)	;PERIOD 3 OVERRIDE VALUE
	DC.W	SET3VR+RDG+(4*2)	;CAN ONLY BE A WORD
	DC.W	SET3FX+SETPTS+(4*3)	;PERIOD 4 OVERRIDE VALUE
	DC.W	SET3VR+RDG+(4*3)	;CAN ONLY BE A WORD
	DC.W	SET3FX+SETPTS+(4*4)	;PERIOD 5 OVERRIDE VALUE
	DC.W	SET3VR+RDG+(4*4)	;CAN ONLY BE A WORD
	DC.W	SET3FX+SETPTS+(4*5)	;PERIOD 6 OVERRIDE VALUE
	DC.W	SET3VR+RDG+(4*5)	;CAN ONLY BE A WORD
	DC.W	SET3FX+SETPTS+(4*6)	;PERIOD 7 OVERRIDE VALUE
	DC.W	SET3VR+RDG+(4*6)	;CAN ONLY BE A WORD
	DC.W	SET3FX+SETPTS+(4*7)	;PERIOD 8 OVERRIDE VALUE
	DC.W	SET3VR+RDG+(4*7)	;CAN ONLY BE A WORD
	;SETPOINT 4
	DC.W	SET4FX+SETPTS+(4*0)	;PERIOD 1 OVERRIDE VALUE
	DC.W	SET4VR+RDG+(4*0)	;CAN ONLY BE A WORD
	DC.W	SET4FX+SETPTS+(4*1)	;PERIOD 2 OVERRIDE VALUE
	DC.W	SET4VR+RDG+(4*1)	;CAN ONLY BE A WORD
	DC.W	SET4FX+SETPTS+(4*2)	;PERIOD 3 OVERRIDE VALUE
	DC.W	SET4VR+RDG+(4*2)	;CAN ONLY BE A WORD
	DC.W	SET4FX+SETPTS+(4*3)	;PERIOD 4 OVERRIDE VALUE
	DC.W	SET4VR+RDG+(4*3)	;CAN ONLY BE A WORD
	DC.W	SET4FX+SETPTS+(4*4)	;PERIOD 5 OVERRIDE VALUE
	DC.W	SET4VR+RDG+(4*4)	;CAN ONLY BE A WORD
	DC.W	SET4FX+SETPTS+(4*5)	;PERIOD 6 OVERRIDE VALUE
	DC.W	SET4VR+RDG+(4*5)	;CAN ONLY BE A WORD
	DC.W	SET4FX+SETPTS+(4*6)	;PERIOD 7 OVERRIDE VALUE
	DC.W	SET4VR+RDG+(4*6)	;CAN ONLY BE A WORD
	DC.W	SET4FX+SETPTS+(4*7)	;PERIOD 8 OVERRIDE VALUE
	DC.W	SET4VR+RDG+(4*7)	;CAN ONLY BE A WORD
	;SETPOINT 5
	DC.W	SET5FX+SETPTS+(4*0)	;PERIOD 1 OVERRIDE VALUE
	DC.W	SET5VR+RDG+(4*0)	;CAN ONLY BE A WORD
	DC.W	SET5FX+SETPTS+(4*1)	;PERIOD 2 OVERRIDE VALUE
	DC.W	SET5VR+RDG+(4*1)	;CAN ONLY BE A WORD
	DC.W	SET5FX+SETPTS+(4*2)	;PERIOD 3 OVERRIDE VALUE
	DC.W	SET5VR+RDG+(4*2)	;CAN ONLY BE A WORD
	DC.W	SET5FX+SETPTS+(4*3)	;PERIOD 4 OVERRIDE VALUE
	DC.W	SET5VR+RDG+(4*3)	;CAN ONLY BE A WORD
	DC.W	SET5FX+SETPTS+(4*4)	;PERIOD 5 OVERRIDE VALUE
	DC.W	SET5VR+RDG+(4*4)	;CAN ONLY BE A WORD
	DC.W	SET5FX+SETPTS+(4*5)	;PERIOD 6 OVERRIDE VALUE
	DC.W	SET5VR+RDG+(4*5)	;CAN ONLY BE A WORD
	DC.W	SET5FX+SETPTS+(4*6)	;PERIOD 7 OVERRIDE VALUE
	DC.W	SET5VR+RDG+(4*6)	;CAN ONLY BE A WORD
	DC.W	SET5FX+SETPTS+(4*7)	;PERIOD 8 OVERRIDE VALUE
	DC.W	SET5VR+RDG+(4*7)	;CAN ONLY BE A WORD
	;SETPOINT 6
	DC.W	SET6FX+SETPTS+(4*0)	;PERIOD 1 OVERRIDE VALUE
	DC.W	SET6VR+RDG+(4*0)	;CAN ONLY BE A WORD
	DC.W	SET6FX+SETPTS+(4*1)	;PERIOD 2 OVERRIDE VALUE
	DC.W	SET6VR+RDG+(4*1)	;CAN ONLY BE A WORD
	DC.W	SET6FX+SETPTS+(4*2)	;PERIOD 3 OVERRIDE VALUE
	DC.W	SET6VR+RDG+(4*2)	;CAN ONLY BE A WORD
	DC.W	SET6FX+SETPTS+(4*3)	;PERIOD 4 OVERRIDE VALUE
	DC.W	SET6VR+RDG+(4*3)	;CAN ONLY BE A WORD
	DC.W	SET6FX+SETPTS+(4*4)	;PERIOD 5 OVERRIDE VALUE
	DC.W	SET6VR+RDG+(4*4)	;CAN ONLY BE A WORD
	DC.W	SET6FX+SETPTS+(4*5)	;PERIOD 6 OVERRIDE VALUE
	DC.W	SET6VR+RDG+(4*5)	;CAN ONLY BE A WORD
	DC.W	SET6FX+SETPTS+(4*6)	;PERIOD 7 OVERRIDE VALUE
	DC.W	SET6VR+RDG+(4*6)	;CAN ONLY BE A WORD
	DC.W	SET6FX+SETPTS+(4*7)	;PERIOD 8 OVERRIDE VALUE
	DC.W	SET6VR+RDG+(4*7)	;CAN ONLY BE A WORD
	;SETPOINT 7
	DC.W	SET7FX+SETPTS+(4*0)	;PERIOD 1 OVERRIDE VALUE
	DC.W	SET7VR+RDG+(4*0)	;CAN ONLY BE A WORD
	DC.W	SET7FX+SETPTS+(4*1)	;PERIOD 2 OVERRIDE VALUE
	DC.W	SET7VR+RDG+(4*1)	;CAN ONLY BE A WORD
	DC.W	SET7FX+SETPTS+(4*2)	;PERIOD 3 OVERRIDE VALUE
	DC.W	SET7VR+RDG+(4*2)	;CAN ONLY BE A WORD
	DC.W	SET7FX+SETPTS+(4*3)	;PERIOD 4 OVERRIDE VALUE
	DC.W	SET7VR+RDG+(4*3)	;CAN ONLY BE A WORD
	DC.W	SET7FX+SETPTS+(4*4)	;PERIOD 5 OVERRIDE VALUE
	DC.W	SET7VR+RDG+(4*4)	;CAN ONLY BE A WORD
	DC.W	SET7FX+SETPTS+(4*5)	;PERIOD 6 OVERRIDE VALUE
	DC.W	SET7VR+RDG+(4*5)	;CAN ONLY BE A WORD
	DC.W	SET7FX+SETPTS+(4*6)	;PERIOD 7 OVERRIDE VALUE
	DC.W	SET7VR+RDG+(4*6)	;CAN ONLY BE A WORD
	DC.W	SET7FX+SETPTS+(4*7)	;PERIOD 8 OVERRIDE VALUE
	DC.W	SET7VR+RDG+(4*7)	;CAN ONLY BE A WORD
	;SETPOINT 8
	DC.W	SET8FX+SETPTS+(4*0)	;PERIOD 1 OVERRIDE VALUE
	DC.W	SET8VR+RDG+(4*0)	;CAN ONLY BE A WORD
	DC.W	SET8FX+SETPTS+(4*1)	;PERIOD 2 OVERRIDE VALUE
	DC.W	SET8VR+RDG+(4*1)	;CAN ONLY BE A WORD
	DC.W	SET8FX+SETPTS+(4*2)	;PERIOD 3 OVERRIDE VALUE
	DC.W	SET8VR+RDG+(4*2)	;CAN ONLY BE A WORD
	DC.W	SET8FX+SETPTS+(4*3)	;PERIOD 4 OVERRIDE VALUE
	DC.W	SET8VR+RDG+(4*3)	;CAN ONLY BE A WORD
	DC.W	SET8FX+SETPTS+(4*4)	;PERIOD 5 OVERRIDE VALUE
	DC.W	SET8VR+RDG+(4*4)	;CAN ONLY BE A WORD
	DC.W	SET8FX+SETPTS+(4*5)	;PERIOD 6 OVERRIDE VALUE
	DC.W	SET8VR+RDG+(4*5)	;CAN ONLY BE A WORD
	DC.W	SET8FX+SETPTS+(4*6)	;PERIOD 7 OVERRIDE VALUE
	DC.W	SET8VR+RDG+(4*6)	;CAN ONLY BE A WORD
	DC.W	SET8FX+SETPTS+(4*7)	;PERIOD 8 OVERRIDE VALUE
	DC.W	SET8VR+RDG+(4*7)	;CAN ONLY BE A WORD
	;TIME PERIODS
	DC.W	PER1FX+EXTPTR	;PERIOD EXTERNAL OVERRIDE
	DC.W	PER1VR+OVRRDG+NOT_WORD_MASK ;BYTE DESTINATION
	DC.W	PER2FX+EXTPTR	;PERIOD EXTERNAL OVERRIDE
	DC.W	PER2VR+OVRRDG+NOT_WORD_MASK ;BYTE DESTINATION
	DC.W	PER3FX+EXTPTR	;PERIOD EXTERNAL OVERRIDE
	DC.W	PER3VR+OVRRDG+NOT_WORD_MASK ;BYTE DESTINATION
	DC.W	PER4FX+EXTPTR	;PERIOD EXTERNAL OVERRIDE
	DC.W	PER4VR+OVRRDG+NOT_WORD_MASK ;BYTE DESTINATION
	DC.W	PER5FX+EXTPTR	;PERIOD EXTERNAL OVERRIDE
	DC.W	PER5VR+OVRRDG+NOT_WORD_MASK ;BYTE DESTINATION
	DC.W	PER6FX+EXTPTR	;PERIOD EXTERNAL OVERRIDE
	DC.W	PER6VR+OVRRDG+NOT_WORD_MASK ;BYTE DESTINATION
	DC.W	PER7FX+EXTPTR	;PERIOD EXTERNAL OVERRIDE
	DC.W	PER7VR+OVRRDG+NOT_WORD_MASK ;BYTE DESTINATION
	DC.W	PER8FX+EXTPTR	;PERIOD EXTERNAL OVERRIDE
	DC.W	PER8VR+OVRRDG+NOT_WORD_MASK ;BYTE DESTINATION
DEV_XE:
N_PTRS:	EQU ((DEV_XE-DEV_XR)-2)>>2

	.ifne N_PTRS-NMPTRS	;already aligned on 8 byte boundary?
	.error "Number of Pointers has changed in DIURNAL.ASM\n"
	.endif

	ALIGN	4	;LONG WORD ALIGNMENT
CODEND:

	END

# Technical Architecture & Configuration Guide: Diurnal Setpoints Program

### **Technical Architecture & Configuration Guide: Diurnal Setpoints Program**

### **1. Introduction: System Design Philosophy**

**1.1. Target Audience and Document Purpose**
This document provides a comprehensive technical overview of the **Diurnal Setpoints Program**. It is intended for system designers, developers, and advanced technical operators who require a deep understanding of the program's architecture, logic flow, and configuration parameters for the purposes of system integration, maintenance, troubleshooting, and advanced scheduling. This guide will move beyond user-level descriptions to explain the underlying engineering principles and data processing pipelines.

**1.2. Architectural Role: The "Goal Setter"**
The Diurnal Setpoints program is the foundational scheduling engine of the entire climate control system. Its sole purpose is to establish the desired environmental targets (setpoints) over a cyclical 24-hour period. It translates a grower's strategic goals—such as creating distinct day/night climates, providing a midday temperature boost, or managing gradual morning warm-ups—into a precise, time-based schedule.

Architecturally, this module acts as the **"Goal Setter."** The output values it generates, found under **`SCHEDULED SETPOINTS (CURRENT OUTPUT VALUES)`**, are not direct commands to equipment. They are the critical, live-updating inputs that are consumed by more advanced control programs, such as the **Climate Energy Balance** module, which then determines how to achieve these goals. This separation of "what to do" (the goal) from "how to do it" (the execution) is a core design principle that allows for immense flexibility and modularity in the overall system design.

**1.3. Rate Management vs. State Management**
A key architectural distinction of this system is its focus on **rate management**, not just state management. A basic thermostat manages a state (e.g., keeping the temperature at 21°C). This advanced system manages the *rate of change*. Through its ramp functions and the associated ramp rate alarms, it controls how *fast* the environment is allowed to change. This prevents shocking the crop, reduces equipment wear, and provides a much more stable and energy-efficient climate.

**1.4. Economic and Biological Implications of Configuration**
It is critical for a developer or designer to understand that the configuration parameters discussed herein have direct and significant financial and biological consequences. A poorly configured schedule with abrupt setpoint changes can cause plant stress, leading to reduced growth and increased disease susceptibility. It can also cause heating and ventilation systems to cycle inefficiently, wasting thousands of dollars in energy costs. Conversely, a well-designed diurnal schedule that mimics natural environmental transitions can significantly improve crop quality, uniformity, and yield while minimizing resource consumption.

### **2. System Architecture & Logic Flow**

The program's logic is to continuously generate a single `Current Setpoint Value` for each of its up to eight configurable setpoints. It does this through a constant, cyclical process:

1. **Identify the Active Period:** The system continuously scans Periods 1 through 8 to see if the current time and day of the week fall within the configured window of any **Enabled** Period.
2. **Identify the Next Period:** Simultaneously, it scans the schedule to find the next upcoming enabled Period in the 24-hour cycle.
3. **Execute State & Calculate Outputs:** Based on the current time's position relative to the enabled periods, the system enters one of two states:
    - **In-Period:** If the current time falls within an active Period, the `Current Setpoint Value` for each setpoint is held steady at the value defined for that specific Period. The `SCHEDULE STATUS` on the main screen will indicate this state (e.g., `In Period 2`).
    - **Ramping:** If the current time is between the `End Time` of the last active Period and the `Start Time` of the next active Period, the system enters a "ramping" state. It performs a linear interpolation for each setpoint, calculating a `Current Setpoint Value` that gradually transitions from the previous Period's setpoint to the next Period's setpoint over the available time. This ensures smooth, gradual changes in the target values. The `SCHEDULE STATUS` on the main screen will indicate this state (e.g., `Ramping From Period 1 to Period 2`).
    
    **Example of Ramping Calculation:**
    
    - Period 1 Ends at 17:00 with a Heating Target of 22°C.
    - Period 2 Starts at 20:00 with a Heating Target of 18°C.
    - The time between periods is 3 hours (180 minutes). The setpoint change is -4°C.
    - At 18:30 (90 minutes into the ramp, or 50% of the way), the system calculates the `Current Setpoint Value` as: `22°C + ((-4°C) * 0.50) = 20.0°C`.

This continuous calculation results in a smooth, 24-hour curve of target values for each of the eight setpoints, which are then broadcast to the rest of the control system.

### **3. Detailed Configuration Guide: The Operator's Toolkit**

This section provides a detailed, parameter-by-parameter explanation of how to configure the Diurnal Setpoints program, based on the provided screenshots and official manual.

**3.1. The Main Screen & Big Overview**
This is the central dashboard, providing a high-level summary of all Period statuses and the final `SCHEDULED SETPOINTS (CURRENT OUTPUT VALUES)`. These output values are the live, ramped targets that are fed to other programs. The `Big Overview` provides a condensed matrix view of all settings for at-a-glance verification.

**3.2. Period Configuration**
This is where the user defines the "when" for each of the up to eight time blocks. This is typically configured from the `Big Overview` screen or the individual `Period` screens.

- **Period Enable/Disable**
    - **Input Type:** User Selection (`Enabled`/`Disabled`).
    - **Function:** Activates or deactivates a period. Disabled periods are completely ignored by the ramping and scheduling logic. This is useful for testing or for creating seasonal schedules that can be turned on or off.
- **Start Time / End Time**
    - **Input Type:** User Input (for both time and type).
    - **Function:** Defines the time window for the period. The user enters a time (e.g., `11:00`) and selects a type.
    - **Types:**
        - `Hours`: An absolute time based on the 24-hour clock.
        - `Before/After Dawn` & `Before/After Dusk`: A relative time calculated based on the system's geographical settings and the time of year.
    - **Strategy:** Using relative times is architecturally superior for agricultural applications as it automatically adjusts for seasonal changes in day length, ensuring schedules remain synchronized with natural light. To prevent overlaps and maintain consistent ramp times, it is best practice to use the same time type (absolute or relative) for a Period's start and end, and for the end of one period and the start of the next.
- **Active Days**
    - **Input Type:** User Selection (checklist for days of the week).
    - **Function:** Allows for creating different schedules for different days, such as a more energy-conservative schedule for weekends.
- **External Enable Settings (Advanced)**
    - **Input Type:** External Pointer.
    - **Function:** Provides an additional, conditional logic layer. A Period can be configured to only become active if an external value (e.g., a sensor reading or the output of another program) is `Zero` or `Non-Zero`. This allows for creating schedules that only run if certain external conditions are met, for example, "run this 'high light' period only if an external `Is_Sunny` variable from another program is non-zero."

**3.3. Setpoint Configuration**
This is where the user defines the "what" – the actual target values. This is primarily done from the individual `Setpoint` screens (e.g., `Heating Target` screen).

- **Setpoint Name and Purpose (The 8 Channels)**
    - **Input Type:** User Input (text label).
    - **Function:** This assigns a descriptive name and purpose to each of the 8 available setpoint "slots". While commonly used for climate, the program is a generic 8-channel scheduler. Below are the common climate applications, which directly map to the inputs required by the Climate Energy Balance program.
        - **1. Heating Target & 2. Cooling Target:** These two setpoints work together to define the acceptable temperature band. The range between them is the "dead band," where the system is idle. A wider dead band (e.g., heating at 20°C, cooling at 24°C) dramatically reduces energy consumption and wear on equipment by preventing rapid cycling. These targets are the primary goals for the Temperature Control PID loops in the execution engine.
        - **3. Dehumidify Target & 4. Humidify Target:** These define the acceptable humidity band. The consequences of improper humidity are severe. Too high, and you risk catastrophic outbreaks of fungal diseases like botrytis. Too low, and plants may close their stomata to conserve water, effectively halting growth. These targets are the primary goals for the Dehumidification PID loops.
        - **5. Max Dehumid Vent:** A crucial energy-saving **limiter**. This sets the maximum percentage the vents are allowed to open *specifically for the purpose of dehumidification*. The Energy Balance program will respect this limit, ensuring that the attempt to lower humidity doesn't compromise temperature stability by opening vents too wide on a cold day.
        - **6. Max Dehumid Heat:** An advanced dehumidification **limiter**. This setpoint sets the maximum percentage of heating power that can be applied *specifically for the purpose of dehumidification*. This prevents the system from wasting energy and overheating the space while trying to solve a humidity problem.
        - **7. CO2 Target:** Defines the desired concentration of Carbon Dioxide in Parts Per Million (PPM) to enhance photosynthesis.
        - **8. SPARE (Flexibility):** This highlights the program's flexibility as a generic scheduler. It can be named and used for any schedulable numeric value in the entire system. For example, it could be named "Irrigation EC Target" and used by an irrigation program to change nutrient concentration throughout the day, or "Shade Screen %" to control the position of a shade screen, or "Light Intensity" to control the output of dimmable lights. This demonstrates that the program is fundamentally a powerful, generic 8-channel function generator.
- **Display Scaling**
    - **Input Type:** User Selection from a predefined list.
    - **Function:** Assigns engineering units (e.g., `Arg: Temp. (Abs)` for °C, `Arg: Humidity` for %Rh). This is critical for ensuring data is displayed correctly and that ramp rate calculations are performed using the correct units.
- **Period Setpoint Values**
    - **Input Type:** User Input OR External Pointer.
    - **Function:** This is the core data entry for the module. For each of the 8 Periods, the user defines the target value for the currently selected setpoint.
    - **Distinction:**
        - **Local Setting (`User Input`):** The value is a fixed number entered directly by the user (e.g., `14.00 C`). The `Address` field will read `Local Setting`. This is the most common configuration.
        - **External Pointer:** The value is dynamically read from another location in the system. The `Address` field will show the path to that value (e.g., `Address: Z; ...`, where 'Z' indicates a Zone). This allows for highly advanced strategies where the diurnal program acts as a scheduler that *selects* between different complex, pre-calculated values based on the time of day.
- **Setpoint Ramp Rate Settings**
    - **Purpose:** An optional safety and monitoring feature to ensure climate transitions are smooth and within acceptable limits.
    - **`Alarm if Increase Rate Exceeds` & `Alarm if Decrease Rate Exceeds`:**
        - **Input Type:** User Input.
        - **Function:** Defines the maximum allowable rate of change per hour for the setpoint. The system calculates the `Current Ramp Rate` based on the change in setpoint value between two consecutive enabled periods and the time between them. If the calculated rate exceeds this user-defined limit, an alarm is triggered. This helps designers identify schedules that may cause equipment stress or harm the crop.
    - **`Ramp Rate Alarm Priority`:**
        - **Input Type:** User Selection.
        - **Function:** Sets the severity of the alarm if a ramp rate violation occurs, from `No Alarm` to a critical `Level #5` alarm.

**3.4. Alarms Configuration**
This screen provides a central dashboard for configuring and monitoring the two main types of alarms within the Diurnal Setpoints program.

- **Setpoint Ramp Alarms:**
    - **Purpose:** Provides a matrix view (`Period Ramp Rate Analysis`) showing the ramp status between every possible period transition for all 8 setpoints. A `Fault!` status indicates that the transition between those two periods violates the configured ramp rate for that setpoint. This allows a designer to pinpoint exactly which part of their schedule is too aggressive.
- **Period Overlap Alarm:**
    - **Purpose:** A critical configuration safeguard. It is logically impossible for two periods to be active at the same time.
    - **Mechanism:** The system constantly checks the start and end times of all **Enabled** periods. If it finds any overlap (e.g., Period 1 ends at 08:00 and Period 2 starts at 07:59), the higher-numbered overlapping period is disabled and its `Period Status` is set to `Not Used: Overlap`. This alarm screen notifies the user of the conflict so it can be corrected.
# CEB Verbose Logging Feature

## Overview
The Climate Energy Balance (CEB) system now supports configurable logging verbosity through a command-line flag. This allows you to control the amount of detail shown in the logs.

## Usage

### Default Mode (Quiet)
Run the program normally without any flags:
```bash
go run main.go
```

**Output Format:**
```
CEB: T:20.0°C | Heat: 3.3% 4.7% 4.7% 8.0% 0.0°C | Vent: 0.0% 8.0% 8.0% 8.0%
```

This compact format shows:
- **T**: Zone Temperature
- **Heat**: 5 heating values (temp control, humidity control, highest request, sum of requests, system temp request)
- **Vent**: 4 ventilation values (temp control, humidity control, highest request, sum of requests)

### Verbose Mode (Detailed)
Run the program with the `-ceb-verbose` flag:
```bash
go run main.go -ceb-verbose
```

**Output Format:**
```
CEB: Starting processing cycle...
CEB: Retrieved heatingTarget = 15.0 from hub:h1:zone:z1:instance:I1:setpoint:heatingTarget
CEB: Retrieved coolingTarget = 20.0 from hub:h1:zone:z1:instance:I1:setpoint:coolingTarget
CEB: Retrieved dehumidifyVentilationTarget = 35.0 from hub:h1:zone:z1:instance:I1:setpoint:dehumidifyVentTarget
CEB: Retrieved dehumidifyHeatingTarget = 75.0 from hub:h1:zone:z1:instance:I1:setpoint:dehumidifyHeatTarget
CEB: Retrieved maxLimitForDehumidifyVentilation = 8.0 from hub:h1:zone:z1:instance:I1:setpoint:maxDehumidVent
CEB: Retrieved maxLimitForDehumidifyHeating = 3.0 from hub:h1:zone:z1:instance:I1:setpoint:maxDehumidHeat
CEB: Setpoints - heatingTarget: 15.0°C, coolingTarget: 20.0°C, dehumidifyHeatTarget: 75.0%, dehumidifyVentTarget: 35.0%
CEB: Dehumidification limits - maxDehumidVent: 8.0%, maxDehumidHeat: 3.0%
CEB: Sensors - integrated temperature: 20.0°C, integrated humidity: 50.0%, outdoor temperature: 15.0°C, light level: 0

CEB: Zone Temperature:                    20.00 °C
CEB: Current Heating Target:              15.00 °C
CEB: Current Cooling Target:              20.00 °C

CEB: Heating Output Values:
    1. Heat req for Temp control:            3.3%
    2. Heat req for Dehumidification:        4.7%
    3. Highest Heat Request:                 4.7%
    4. Sum of Heat Requests:                 8.0%
    5. Heating System Request Temp:          0.0°C

CEB: Ventilation Output Values:
    6. Vent require for temp control:       0.0%
    7. Vent require for humidity control:   8.0%
    8. Highest Ventilation Request:         8.0%
    9. Sum of Ventilation Requests:         8.0%
CEB: Processing cycle completed successfully
```

## When to Use Each Mode

### Use Default Mode When:
- Running in production environments
- You want minimal log output
- You only need to monitor the final calculated values
- Running automated systems where detailed logs aren't needed

### Use Verbose Mode When:
- Debugging CEB calculations
- Troubleshooting setpoint retrieval issues
- Analyzing system behavior in detail
- Development and testing
- Understanding how the 9 outputs are calculated

## The 9 Required Outputs

Both modes display all 9 required CEB outputs:

**Heating Outputs (5):**
1. Heat req for Temp control
2. Heat req for Dehumidification  
3. Highest Heat Request
4. Sum of Heat Requests
5. Heating System Request Temp

**Ventilation Outputs (4):**
6. Vent require for temp control
7. Vent require for humidity control
8. Highest Ventilation Request
9. Sum of Ventilation Requests

## Implementation Details

The verbose logging flag is implemented at the controller level and affects:
- Setpoint retrieval logging
- Sensor reading logging
- Processing cycle start/completion messages
- Detailed output formatting vs. compact summary formatting

The flag does not affect:
- Error messages (always shown)
- System startup messages
- Redis storage operations
- The actual calculation logic

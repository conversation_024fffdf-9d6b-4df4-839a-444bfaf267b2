# CEB Program Plan

## Overview

The Climate Energy Balance (CEB) program is designed to control heating and ventilation systems based on environmental conditions and user-defined setpoints. This program produces 9 specific outputs for comprehensive climate control.

CEB takes redis keys as addresses for input and use those for 9 output calculations. diaurnal setpoints for current active period are fetched and used for 9 output calculations.

**Important Notes:**

-   Legacy Redis topics support has been removed from the CEB system (no backward compatibility maintained)
-   Setpoint names are configurable and can be dynamically set by users rather than using hardcoded names
-   The diurnal program supports a maximum of 8 setpoints with specific configurations

## Inputs

### Input Format

The CEB program uses an enhanced configuration structure with Redis key mappings and tuning parameters. Key features of the updated format:

-   **CEBInputRedisKeys**: Direct Redis key mappings for all input values
-   **CEBOutputRedisKeys**: Direct Redis key mappings for all output values
-   **Enhanced Structure**: Simplified configuration with concrete Redis addresses instead of abstract references
-   **Integrated Tuning**: Heat and ventilation tuning parameters embedded in the configuration
-   **Cross-Module Integration**: Direct integration with diurnal program setpoints using standardized Redis key format

```json
{
    "programName": "climate energy balance",
    "enabled": true,
    "CEBInputRedisKeys": {
        "zoneTemperature": "hub:h1:io:sensorTemperature",
        "backupZoneTemperature": "hub:h1:io:backupSensorTemperature",
        "zoneHumidity": "hub:h1:io:sensorHumidity",
        "shadePosition": "hub:h1:zone:z1:shade:shadePosition",
        "coolingTarget": "hub:h1:zone:z1:instance:I1:setpoint:coolingTarget",
        "heatingTarget": "hub:h1:zone:z1:instance:I1:setpoint:heatingTarget",
        "dehumidifyVentilationTarget": "hub:h1:zone:z1:instance:I1:setpoint:dehumidifyVentTarget",
        "dehumidifyHeatingTarget": "hub:h1:zone:z1:instance:I1:setpoint:dehumidifyHeatTarget",
        "maxLimitForDehumidifyVentilation": "hub:h1:zone:z1:instance:I1:setpoint:maxDehumidVent",
        "maxLimitForDehumidifyHeating": "hub:h1:zone:z1:instance:I1:setpoint:maxDehumidHeat",
        "outdoorTemperature": "hub:h1:instance:I1:weather:outdoorTemperature",
        "lightLevelNI": "hub:h1:instance:I1:weather:lightLevelNI",
        "lightLevelI": "hub:h1:instance:I1:weather:lightLevelI"
    },
    "CEBOutputRedisKeys": {
        "ventTempControl": "hub:h1:zone:z1:instance:I1:ceb:ventTempControl",
        "ventHumidityControl": "hub:h1:zone:z1:instance:I1:ceb:ventHumidityControl",
        "highestVentRequest": "hub:h1:zone:z1:instance:I1:ceb:highestVentRequest",
        "sumVentRequests": "hub:h1:zone:z1:instance:I1:ceb:sumVentRequests",
        "heatTempControl": "hub:h1:zone:z1:instance:I1:ceb:heatTempControl",
        "heatHumidityControl": "hub:h1:zone:z1:instance:I1:ceb:heatHumidityControl",
        "highestHeatRequest": "hub:h1:zone:z1:instance:I1:ceb:highestHeatRequest",
        "sumHeatRequests": "hub:h1:zone:z1:instance:I1:ceb:sumHeatRequests",
        "heatingSystemTempRequest": "hub:h1:zone:z1:instance:I1:ceb:heatingSystemTempRequest",
        "integratedTemp": "hub:h1:zone:z1:instance:I1:ceb:integratedTemp",
        "integratedHumidity": "hub:h1:zone:z1:instance:I1:ceb:integratedHumidity"
    },
    "heatTuning": {
        "heatReqForTemperatureControl": {
            "heatingTarget": {
                "used": true,
                "address": "hub:h1:zone:z1:instance:I1:setpoint:heatingTarget",
                "crossModuleReqTime": 30
            },
            "zoneTemperature": {
                "used": true,
                "address": "hub:h1:io:sensorTemperature",
                "crossModuleReqTime": 30
            },
            "backupZoneTemperature": {
                "used": true,
                "address": "hub:h1:io:backupSensorTemperature",
                "crossModuleReqTime": 30
            },
            "heatingProportionalSpanP": 4,
            "heatingIntegralTimeI": 50
        },
        "heatReqForDehumidification": {
            "dehumidificationLimit": {
                "used": false,
                "address": "",
                "crossModuleReqTime": 0
            },
            "currentHumidity": {
                "used": false,
                "address": "",
                "crossModuleReqTime": 0
            },
            "dehumidifyHeatTarget": {
                "used": false,
                "address": "",
                "crossModuleReqTime": 0
            },
            "dehumidifyHeatOffset": 0,
            "dehumidifyHeatProportionalSpanP": 0,
            "dehumidifyHeatIntegralTimeI": 0
        },
        "heatingOutdoorTemperatureEffect": {
            "heatingTarget": {
                "used": true,
                "address": "hub:h1:zone:z1:instance:I1:setpoint:heatingTarget",
                "crossModuleReqTime": 30
            },
            "outdoorTemperature": {
                "used": true,
                "address": "hub:h1:instance:I1:weather:outdoorTemperature",
                "crossModuleReqTime": 30
            },
            "shadePosition": {
                "used": true,
                "address": "hub:h1:zone:z1:shade:shadePosition",
                "crossModuleReqTime": 30
            },
            "shadeRetractedSettings": {
                "minTempDifference": 0,
                "maxTempDifference": 60,
                "minOutdoorEffect": 0,
                "maxOutdoorEffect": 40
            },
            "shadeExtendedSettings": {
                "minTempDifference": 0,
                "maxTempDifference": 60,
                "minOutdoorEffect": 0,
                "maxOutdoorEffect": 30
            }
        },
        "heatingLightEffect": {
            "currentLightReading": {
                "used": false,
                "address": "",
                "crossModuleReqTime": 0
            },
            "integratedLightReading": {
                "used": false,
                "address": "",
                "crossModuleReqTime": 0
            },
            "lightPredictionModifier": 0,
            "lightEffectScaling": {
                "minInput": 0,
                "maxInput": 0,
                "minOutput": 0,
                "maxOutput": 0
            }
        },
        "heatingSystemRequest": {
            "requestNumber": 0,
            "useHigherOrSum": "",
            "minHeatingSystemTemp": 0,
            "maxHeatingSystemTemp": 0
        }
    },
    "ventilationTuning": {
        "ventReqForTemperatureControl": {
            "coolingTarget": {
                "used": true,
                "address": "hub:h1:zone:z1:instance:I1:setpoint:coolingTarget",
                "crossModuleReqTime": 30
            },
            "zoneTemperature": {
                "used": true,
                "address": "hub:h1:io:sensorTemperature",
                "crossModuleReqTime": 30
            },
            "backupZoneTemperature": {
                "used": true,
                "address": "hub:h1:io:backupSensorTemperature",
                "crossModuleReqTime": 30
            },
            "coolingProportionalSpanP": 4,
            "coolingIntegralTimeI": 50
        },
        "ventReqForDehumidification": {
            "dehumidifyVentTarget": {
                "used": false,
                "address": "",
                "crossModuleReqTime": 0
            },
            "zoneHumidity": {
                "used": false,
                "address": "",
                "crossModuleReqTime": 0
            },
            "maxLimitForDehumidifyVentilation": {
                "used": false,
                "address": "",
                "crossModuleReqTime": 0
            },
            "ventilationDehumidifyProportionalSpanP": 0,
            "integralAccumulationTimeI": 0
        },
        "ventilationOutdoorTemperatureEffect": {
            "coolingTarget": {
                "used": true,
                "address": "hub:h1:zone:z1:instance:I1:setpoint:coolingTarget",
                "crossModuleReqTime": 30
            },
            "outdoorTemperature": {
                "used": true,
                "address": "hub:h1:instance:I1:weather:outdoorTemperature",
                "crossModuleReqTime": 30
            },
            "ventilationEffectScaling": {
                "minInput": 0,
                "maxInput": 0,
                "minOutput": 0,
                "maxOutput": 0
            }
        },
        "ventilationLightEffect": {
            "currentLightReading": {
                "used": true,
                "address": "hub:h1:instance:I1:weather:lightLevelNI",
                "crossModuleReqTime": 30
            },
            "integratedLightReading": {
                "used": true,
                "address": "hub:h1:instance:I1:weather:lightLevelI",
                "crossModuleReqTime": 30
            },
            "lightPredictionModifier": 1,
            "lightEffectScaling": {
                "minInput": 0,
                "maxInput": 1000,
                "minOutput": 0,
                "maxOutput": 1
            }
        }
    }
}
```

## Outputs

### CEB Program 9 Required Outputs

The CEB program produces exactly 9 specific outputs for comprehensive climate control:

```txt
CEB: Zone Temperature:                    25.30 °C
CEB: Current Heating Target:              25.00 °C
CEB: Current Cooling Target:              27.00 °C

CEB: Heating Output Values:
    1. Heat req for Temp control:            19.4%
    2. Heat req for Dehumidification:        40.0%
    3. Highest Heat Request:                 40.0%
    4. Sum of Heat Requests:                 59.4%
    5. Heating System Request Temp:          50.0°C

CEB: Ventilation Output Values:
    6. Vent require for temp control:       0.0%
    7. Vent require for humidity control:   0.0%
    8. Highest Ventilation Request:         0.0%
    9. Sum of Ventilation Requests:         0.0%
```

**Output Descriptions:**

1. **Ventilation required for temperature control** - Percentage of ventilation needed for temperature regulation
2. **Ventilation required for humidity control** - Percentage of ventilation needed for humidity regulation
3. **Highest ventilation request** - Maximum of all ventilation requests
4. **Sum of ventilation requests** - Total of all ventilation requests
5. **Heating required for temperature control** - Percentage of heating needed for temperature regulation
6. **Heating required for humidity control** - Percentage of heating needed for humidity regulation
7. **Highest heating request** - Maximum of all heating requests
8. **Sum of heating requests** - Total of all heating requests
9. **Current temperature request to heating system** - Temperature setpoint sent to heating system

### Heat Tuning

-   Heat required for temperature control settings

```txt
CEB: HRTCS: Heating target Address:                    CEBInputValues.heatingTarget
CEB: HRTCS: Cross Module Request Time:                 30 sec

CEB: HRTCS: Heating target:                            18.84 °C (from address)
CEB: HRTCS: Integrated temperature:                    16.32 °C
CEB: HRTCS: Temperature heating P difference:          2.52 °C

CEB: HRTCS: Heating Proportional span 'P':             4.00 °C
CEB: HRTCS: Heating Interval time 'I':                 50
CEB: HRTCS: Temperature heating I timer:               11 // continuously updated

CEB: HRTCS: Temperature heating 'P' Component:         63.00 %
CEB: HRTCS: Temperature heating 'I' Component:         -0.07 %
CEB: HRTCS: Heating outdoor temperature effect:        20.31 %
CEB: HRTCS: Heating light effect setting:              -7.16 %

CEB: HRTCS: Total Heating Required for Temperature Control: 76.08%
```

-   Heat required for dehumidification settings

```txt
CEB: HRDS: Current max limit for dehumidification:     10.00 % (from address)
CEB: HRDS: Dehumidification limit address:             CEBInputValues.maxLimitForDehumidifyHeating
CEB: HRDS: Cross module request time:                  30 sec
// This value will limit both the current integral and the total heating required value

CEB: HRDS: Proportioning values:
CEB: HRDS: Current humidity:                           92.0 %Rh
CEB: HRDS: Current humidity address:                   CEBInputValues.zoneHumidity
CEB: HRDS: Cross module request time:                  30 sec

CEB: HRDS: Integrated humidity reading:                92.0 %Rh
CEB: HRDS: Heating dehumidify target:                  80.0 %Rh (from address)
CEB: HRDS: Heating dehumidify target address:          CEBInputValues.dehumidifyHeatingTarget
CEB: HRDS: Cross module request time:                  30 sec

CEB: HRDS: Dehumidify heat offset:                     0.00 %Rh
CEB: HRDS: Dehumidify heating "P" difference:          12.00 %Rh

CEB: HRDS: Dehumidify heat proportional span "P":      33.3 %Ph
CEB: HRDS: Current Proportional:                       36.04 %
CEB: HRDS: Dehumidify heat integral time "I":          30 sec
CEB: HRDS: Dehumidify heat I timer:                    25 // continuously updated
CEB: HRDS: Current Integral:                           0.83 %

CEB: HRDS: Heating Required for Dehumidification:      10.00% (P+I)
// 36.05 + 0.83 = 36.88, but limited by 10.00%
```

-   Heating outdoor temperature effect setting

```txt
CEB: HOTES: Heating target:                            28.00 °C (from address)
CEB: HOTES: Heating target address:                    CEBInputValues.heatingTarget
CEB: HOTES: Cross module request time:                 30 sec

CEB: HOTES: Outdoor temperature:                       14.00 °C (from address)
CEB: HOTES: Outdoor temperature address:               CEBInputValues.outdoorTemperature
CEB: HOTES: Cross module request time:                 30 sec

CEB: HOTES: Heating target minus outdoor temperature:  14.00 °C

CEB: HOTES: Shade curtain position:                    0.00 % (from address)
// 0% = closed (fully extended) 100% = open (fully retracted)
CEB: HOTES: Shade position address:                    CEBInputValues.shadePosition
CEB: HOTES: Cross module request time:                 30 sec
// If shade = failed or not used, the shade RETRACTED position will be used.
// 0% = closed (fully extended) 100% = open (fully retracted)

CEB: HOTES: Shade/Thermal curtain RETRACTED settings (Curtain is open or not used) (shade position > 0%)
CEB: HOTES: Use these settings when the shade is RETRACTED, or not used,
CEB: HOTES: as the temperature difference varies from 0.00 °C to 60.00 °C
CEB: HOTES: modify the outdoor temperature effect from 0.00 % to 40.00 %

CEB: HOTES: Shade/Thermal curtain EXTENDED settings (Curtain is closed) (shade position = 0%)
CEB: HOTES: Use these settings when the shade is EXTENDED,
CEB: HOTES: as the temperature difference varies from 0.00 °C to 60.00 °C
CEB: HOTES: modify the outdoor temperature effect from 0.00 % to 30.00 %

CEB: HOTES: Outdoor temperature effect setting:        7.00 %
// 14/60 * 0.3 * 100 = 7.00%
```

-   Heating light effect setting

```txt
CEB: HLES: NOTE: The value will not exceed the current outdoor temperature effect amount.
CEB: HLES: It is used to reduce the outdoor effect to compensate for solar gain

CEB: HLES: Current light reading:                      754 W/m2 (from address)
CEB: HLES: Light reading address:                      CEBInputValues.lightLevelNI
CEB: HLES: Cross module request time:                  30 sec

CEB: HLES: 15 minute integrated light reading:         751 W/m2 (from address)
CEB: HLES: Integrated light reading address:           CEBInputValues.lightLevelI
CEB: HLES: Cross module request time:                  30 sec

CEB: HLES: Light prediction modifier:                  1.00
CEB: HLES: Predicted light value for heating:          757 W/m2 (current light reading * light prediction modifier)

CEB: HLES: Light effect scaling:
CEB: HLES: As predicted light value changes from 100 W/m2 to 1000 W/m2,
CEB: HLES: modify the heating light effect from 0.00 % to 10.00 %

CEB: HLES: Calculated temperature heating light effect: -7.30 %
CEB: HLES: Current heating light effect:               -3.45 % (limited by the HOTES)
// Linear interpolation formula is used to calculate -7.30%
// Output = MinOutput + (Input - MinInput) / (MaxInput - MinInput) × (MaxOutput - MinOutput)
// Ratio = (757 - 100) / (1000 - 100) = 657 / 900 = 0.73
// Light Effect = 0.00% + 0.73 × (10.00% - 0.00%) = 0.73 × 10.00% = 7.30%
// Limited by HOTES to -3.45% as for this calculation HOTES = 3.45%
```

-   Heating system request temperature setting

```txt
CEB: HSRTS: Make this request to heating system:       1 // values can be 1-16
CEB: HSRTS: This section is used to generate a proportioned temperature request
CEB: HSRTS: for use by a heating system (i.e. Hot Water Temperature).
CEB: HSRTS: The current heating required percentage is proportioned between the
CEB: HSRTS: minimum and maximum temperature settings.
CEB: HSRTS: Heating required for temperature control:   76.61 %
CEB: HSRTS: Heating required for dehumidification:     0.00 %
CEB: HSRTS: Use the:                                   Higher of the heat requests // OR Sum of the heat requests

CEB: HSRTS: Minimum heating system temperature:        20.00 °C
CEB: HSRTS: Maximum heating system temperature:        100.00 °C

CEB: HSRTS: Current heating system request temperature: 81.28 °C
// Linear interpolation
// 20 + (76.61/100) * (100-20) = 81.28°C
```

### Ventilation Tuning

-   Ventilation for temperature control settings

```txt
CEB: VTCS: Integrated Temperature:                     24.00 °C
CEB: VTCS: Cooling Target:                             23.00 °C (From Address)
CEB: VTCS: Cooling Target address:                     CEBInputValues.coolingTarget
CEB: VTCS: Cross module request time:                  30 sec

CEB: VTCS: Cooling Band:                               0.28 °C
CEB: VTCS: Ventilation Cooling 'P' difference:         1.00 °C

CEB: VTCS: Cooling Proportional span 'P':              7.00 °C
CEB: VTCS: Cooling Integral time 'I':                  50 sec
CEB: VTCS: Cooling I timer:                            40 // continuously updated

CEB: VTCS: For "I": 0=Disabled, 1=Fastest, 255=Slowest
CEB: VTCS: Note: "I" is zeroed if heating required > 0%

CEB: VTCS: Temperature cooling P component:            10.29 %
CEB: VTCS: Temperature cooling I component:            0.07 %
CEB: VTCS: P + I:                                      10.36 %

CEB: VTCS: Ventilation outdoor temperature effect:     0.60

CEB: VTCS: Total Ventilation required for temperature control: 6.21%
// 10.36 * 0.60 = 6.21%
```

-   Ventilation required for dehumidification settings

```txt
CEB: VRDS: Current maximum ventilation for
CEB: VRDS: dehumidification limit:                     15.00 % (from address)
CEB: VRDS: Dehumidification limit address:             CEBInputValues.maxLimitForDehumidifyVentilation
CEB: VRDS: Cross module request time:                  30 sec
CEB: VRDS: This will limit the integral, outdoor effect and ventilation required values

CEB: VRDS: Proportioning Values:
CEB: VRDS: --------------------
CEB: VRDS: Integrated humidity reading:                92.0 %Rh
CEB: VRDS: Ventilation humidity target:                80.0 %Rh (from address)
CEB: VRDS: Ventilation humidity target address:        CEBInputValues.dehumidifyVentilationTarget
CEB: VRDS: Cross module request time:                  30 sec

CEB: VRDS: Ventilation dehumidify "P" difference:      12.00 %Rh

CEB: VRDS: Ventilation dehumidify proportional span "P":   33.3 %Ph
CEB: VRDS: Integral accumulation time "I":             30 sec
CEB: VRDS: Integral accumulation I timer:              20 // continuously updated

CEB: VRDS: For "I": 0=Disabled, 1=Fastest, 255=Slowest

CEB: VRDS: Dehumidify ventilation P component:         36.04 %
CEB: VRDS: Dehumidify ventilation I component:         15.00 %
CEB: VRDS: P + I:                                      51.04 %
CEB: VRDS: Ventilation outdoor temperature effect:     0.60
CEB: VRDS: Outdoor effect adjusted:                    15.00 %
CEB: VRDS: Less P heat component:                      -100.00 % (Heating current proportional)(Only positive values are used)

CEB: VRDS: NOTE: If heat reduction is disabled the total
CEB: VRDS: will be zeroed when the temperature is
CEB: VRDS: > 2.5 °C below the target.

CEB: VRDS: Total ventilation required for dehumidification: 15.00 %
```

-   Ventilation light effect settings

```txt
CEB: VLTS: Current light reading:                      550 W/m2 (from address)
CEB: VLTS: Light reading address:                      CEBInputValues.lightLevelNI
CEB: VLTS: Cross module request time:                  30 sec

CEB: VLTS: Ventilation light effect scaling:

CEB: VLTS: As the current light reading changes from 200 W/m2 to 500 W/m2
CEB: VLTS: Modify the ventilation light effect from 0.30 x to 1.00 x

CEB: VLTS: Current ventilation light effect multiplier: 1.00
```

-   Ventilation outdoor temperature effect settings

```txt
CEB: VOTES: Cooling target:                            23.00 °C (from address)
CEB: VOTES: Cooling target address:                    CEBInputValues.coolingTarget
CEB: VOTES: Cross module request time:                 30 sec

CEB: VOTES: Outdoor temperature:                       13.00 °C (from address)
CEB: VOTES: Outdoor temperature address:               CEBInputValues.outdoorTemperature
CEB: VOTES: Cross module request time:                 30 sec

CEB: VOTES: Cooling target minus outdoor temperature:  10.00 °C

CEB: VOTES: Ventilation outdoor temperature effect scaling:

CEB: VOTES: As the cooling target minus outdoor temperature changes from 4.00 °C to 15.00 °C
CEB: VOTES: Modify the ventilation outdoor temperature effect from 1.00 x to 0.25 x

CEB: VOTES: Current ventilation outdoor temperature effect multiplier: 0.60
// Linear interpolation
// 0.25 + (10.00 - 4.00) / (15.00 - 4.00) * (1.00 - 0.25) = 0.60
```

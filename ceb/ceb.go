package ceb

import (
	"context"
	"log"
	"math"
	"time"

	"program-manager/redis"
	"program-manager/types"
)

// CEBController manages the Climate Energy Balance system
type CEBController struct {
	config         types.CEBConfig
	state          types.CEBState
	lastUpdate     time.Time
	verboseLogging bool
}

// NewCEBController creates a new CEB controller instance
func NewCEBController(config types.CEBConfig) *CEBController {
	controller := &CEBController{
		config:         config,
		state:          types.CEBState{},
		lastUpdate:     time.Now(),
		verboseLogging: false, // Default to quiet mode
	}

	log.Println("CEB Controller initialized")
	return controller
}

// SetVerboseLogging enables or disables detailed logging for the 9 outputs
func (c *CEBController) SetVerboseLogging(enabled bool) {
	c.verboseLogging = enabled
	if enabled {
		log.Println("CEB: Verbose logging enabled - detailed logs for 9 outputs will be printed")
	} else {
		log.Println("CEB: Verbose logging disabled - only final summary values will be printed")
	}
}

// ProcessCycle executes one complete CEB calculation cycle
func (c *CEBController) ProcessCycle(ctx context.Context) error {
	if !c.config.Enabled {
		return nil
	}

	if c.verboseLogging {
		log.Printf("\nCEB: Starting processing cycle...")
	}

	// Stage 1: Get setpoints from diurnal program
	setpoints, err := c.getSetpoints(ctx)
	if err != nil {
		log.Printf("CEB: Error getting setpoints: %v", err)
		return err
	}

	// Conditional logging for setpoints based on verbose flag
	if c.verboseLogging {
		log.Printf("CEB: Setpoints - heatingTarget: %.1f°C, coolingTarget: %.1f°C, dehumidifyHeatTarget: %.1f%%, dehumidifyVentTarget: %.1f%%",
			setpoints[types.PurposeHeatingTarget], setpoints[types.PurposeCoolingTarget],
			setpoints[types.PurposeDehumidifyHeatTarget], setpoints[types.PurposeDehumidifyVentTarget])
		if val, exists := setpoints[types.PurposeMaxDehumidVent]; exists {
			log.Printf("CEB: Dehumidification limits - maxDehumidVent: %.1f%%, maxDehumidHeat: %.1f%%",
				val, setpoints[types.PurposeMaxDehumidHeat])
		}
		if val, exists := setpoints[types.PurposeCO2Target]; exists {
			log.Printf("CEB: Additional setpoints - co2Target: %.1f ppm", val)
		}
	}

	// Stage 2: Get current sensor readings and integrate them
	if err := c.updateSensorReadings(ctx); err != nil {
		log.Printf("CEB: Error updating sensor readings: %v", err)
		return err
	}

	if c.verboseLogging {
		log.Printf("CEB: Sensors - integrated temperature: %.1f°C, integrated humidity: %.1f%%, outdoor temperature: %.1f°C, light level: %.0f",
			c.state.IntegratedTemp, c.state.IntegratedHumidity, c.state.OutdoorTemp, c.state.LightLevel)
	}

	// Stage 3: Calculate independent demands using PID controllers
	c.calculateDemands(setpoints)

	// Stage 4: Apply feed-forward adjustments
	c.applyFeedForwardAdjustments()

	// Stage 5: Select final equipment requests (highest demand wins)
	c.selectFinalRequests()

	// Display outputs based on verbose logging flag
	if c.verboseLogging {
		// Detailed output format when verbose logging is enabled
		log.Printf("\nCEB: Zone Temperature:                    %.2f °C", c.state.IntegratedTemp)
		if c.config.IsEnhancedConfig() {
			// Enhanced configuration - get current targets from setpoints
			heatingTarget := setpoints[types.PurposeHeatingTarget]
			coolingTarget := setpoints[types.PurposeCoolingTarget]
			log.Printf("CEB: Current Heating Target:              %.2f °C", heatingTarget)
			log.Printf("CEB: Current Cooling Target:              %.2f °C", coolingTarget)
		}

		log.Printf("\nCEB: Heating Output Values:")
		log.Printf("    1. Heat req for Temp control:            %.1f%%", c.state.HeatReqForTemperatureControl)
		log.Printf("    2. Heat req for Dehumidification:        %.1f%%", c.state.HeatReqForHumidityControl)
		log.Printf("    3. Highest Heat Request:                 %.1f%%", c.state.HighestHeatRequest)
		log.Printf("    4. Sum of Heat Requests:                 %.1f%%", c.state.SumOfHeatRequests)
		log.Printf("    5. Heating System Request Temp:          %.1f°C", c.state.HeatingSystemTempRequest)

		log.Printf("\nCEB: Ventilation Output Values:")
		log.Printf("    6. Vent require for temp control:       %.1f%%", c.state.VentReqForTemperatureControl)
		log.Printf("    7. Vent require for humidity control:   %.1f%%", c.state.VentReqForHumidityControl)
		log.Printf("    8. Highest Ventilation Request:         %.1f%%", c.state.HighestVentRequest)
		log.Printf("    9. Sum of Ventilation Requests:         %.1f%%", c.state.SumOfVentRequests)
	} else {
		// Compact summary format when verbose logging is disabled
		log.Printf("CEB: T:%.1f°C | Heat: %.1f%% %.1f%% %.1f%% %.1f%% %.1f°C | Vent: %.1f%% %.1f%% %.1f%% %.1f%%",
			c.state.IntegratedTemp,
			c.state.HeatReqForTemperatureControl, c.state.HeatReqForHumidityControl,
			c.state.HighestHeatRequest, c.state.SumOfHeatRequests, c.state.HeatingSystemTempRequest,
			c.state.VentReqForTemperatureControl, c.state.VentReqForHumidityControl,
			c.state.HighestVentRequest, c.state.SumOfVentRequests)
	}

	// Stage 6: Apply safety limits and validation
	if err := c.applySafetyLimits(); err != nil {
		log.Printf("CEB: Safety limit violation: %v", err)
		return err
	}

	// Stage 7: Store results in Redis
	if err := c.storeResults(ctx); err != nil {
		log.Printf("CEB: Error storing results: %v", err)
		return err
	}

	c.lastUpdate = time.Now()
	if c.verboseLogging {
		log.Printf("CEB: Processing cycle completed successfully")
	}
	return nil
}

// getSetpoints retrieves current setpoints from diurnal program
func (c *CEBController) getSetpoints(ctx context.Context) (map[types.SetpointPurpose]float64, error) {
	// Use enhanced configuration only
	return c.getSetpointsEnhanced(ctx)
}

// getSetpointsEnhanced retrieves setpoints using the enhanced configuration structure
func (c *CEBController) getSetpointsEnhanced(ctx context.Context) (map[types.SetpointPurpose]float64, error) {
	setpoints := make(map[types.SetpointPurpose]float64)

	// Get setpoints from enhanced configuration structure
	inputKeys := c.config.CEBInputRedisKeys

	// Heating Target
	if val, err := redis.GetFloat(ctx, inputKeys.HeatingTarget); err == nil {
		setpoints[types.PurposeHeatingTarget] = val
		if c.verboseLogging {
			log.Printf("CEB: Retrieved heatingTarget = %.1f from %s", val, inputKeys.HeatingTarget)
		}
	} else {
		setpoints[types.PurposeHeatingTarget] = 20.0 // Default
		if c.verboseLogging {
			log.Printf("CEB: Using default heatingTarget = 20.0 (Redis key %s not available: %v)", inputKeys.HeatingTarget, err)
		}
	}

	// Cooling Target
	if val, err := redis.GetFloat(ctx, inputKeys.CoolingTarget); err == nil {
		setpoints[types.PurposeCoolingTarget] = val
		if c.verboseLogging {
			log.Printf("CEB: Retrieved coolingTarget = %.1f from %s", val, inputKeys.CoolingTarget)
		}
	} else {
		setpoints[types.PurposeCoolingTarget] = 24.0 // Default
		if c.verboseLogging {
			log.Printf("CEB: Using default coolingTarget = 24.0 (Redis key %s not available: %v)", inputKeys.CoolingTarget, err)
		}
	}

	// Dehumidify Ventilation Target
	if val, err := redis.GetFloat(ctx, inputKeys.DehumidifyVentilationTarget); err == nil {
		setpoints[types.PurposeDehumidifyVentTarget] = val
		if c.verboseLogging {
			log.Printf("CEB: Retrieved dehumidifyVentilationTarget = %.1f from %s", val, inputKeys.DehumidifyVentilationTarget)
		}
	} else {
		setpoints[types.PurposeDehumidifyVentTarget] = 70.0 // Default
		if c.verboseLogging {
			log.Printf("CEB: Using default dehumidifyVentilationTarget = 70.0 (Redis key %s not available: %v)", inputKeys.DehumidifyVentilationTarget, err)
		}
	}

	// Dehumidify Heating Target
	if val, err := redis.GetFloat(ctx, inputKeys.DehumidifyHeatingTarget); err == nil {
		setpoints[types.PurposeDehumidifyHeatTarget] = val
		if c.verboseLogging {
			log.Printf("CEB: Retrieved dehumidifyHeatingTarget = %.1f from %s", val, inputKeys.DehumidifyHeatingTarget)
		}
	} else {
		setpoints[types.PurposeDehumidifyHeatTarget] = 35.0 // Default
		if c.verboseLogging {
			log.Printf("CEB: Using default dehumidifyHeatingTarget = 35.0 (Redis key %s not available: %v)", inputKeys.DehumidifyHeatingTarget, err)
		}
	}

	// Max Dehumidify Ventilation Limit
	if val, err := redis.GetFloat(ctx, inputKeys.MaxLimitForDehumidifyVentilation); err == nil {
		setpoints[types.PurposeMaxDehumidVent] = val
		if c.verboseLogging {
			log.Printf("CEB: Retrieved maxLimitForDehumidifyVentilation = %.1f from %s", val, inputKeys.MaxLimitForDehumidifyVentilation)
		}
	} else {
		setpoints[types.PurposeMaxDehumidVent] = 20.0 // Default
		if c.verboseLogging {
			log.Printf("CEB: Using default maxLimitForDehumidifyVentilation = 20.0 (Redis key %s not available: %v)", inputKeys.MaxLimitForDehumidifyVentilation, err)
		}
	}

	// Max Dehumidify Heating Limit
	if val, err := redis.GetFloat(ctx, inputKeys.MaxLimitForDehumidifyHeating); err == nil {
		setpoints[types.PurposeMaxDehumidHeat] = val
		if c.verboseLogging {
			log.Printf("CEB: Retrieved maxLimitForDehumidifyHeating = %.1f from %s", val, inputKeys.MaxLimitForDehumidifyHeating)
		}
	} else {
		setpoints[types.PurposeMaxDehumidHeat] = 10.0 // Default
		if c.verboseLogging {
			log.Printf("CEB: Using default maxLimitForDehumidifyHeating = 10.0 (Redis key %s not available: %v)", inputKeys.MaxLimitForDehumidifyHeating, err)
		}
	}

	return setpoints, nil
}

// updateSensorReadings gets current sensor data and applies integration
func (c *CEBController) updateSensorReadings(ctx context.Context) error {
	var rawTemp, rawHumidity float64
	var err error

	// Use enhanced configuration - get CEBInputRedisKeys
	inputKeys := c.config.CEBInputRedisKeys

	// Get zone temperature (with backup)
	rawTemp, err = redis.GetFloat(ctx, inputKeys.ZoneTemperature)
	if err != nil {
		// Try backup sensor
		if rawTemp, err = redis.GetFloat(ctx, inputKeys.BackupZoneTemperature); err != nil {
			// Use last known value if both sensors fail
			rawTemp = c.state.IntegratedTemp
			if c.state.IntegratedTemp == 0 {
				rawTemp = 20.0 // Default fallback temperature
			}
		}
	}

	// Get zone humidity
	rawHumidity, err = redis.GetFloat(ctx, inputKeys.ZoneHumidity)
	if err != nil {
		// Use last known value if sensor reading fails
		rawHumidity = c.state.IntegratedHumidity
		if c.state.IntegratedHumidity == 0 {
			rawHumidity = 50.0 // Default fallback humidity
		}
	}

	// Get outdoor conditions for feed-forward (with defaults)
	if temp, err := redis.GetFloat(ctx, inputKeys.OutdoorTemperature); err == nil {
		c.state.OutdoorTemp = temp
	} else {
		c.state.OutdoorTemp = 15.0 // Default outdoor temperature
	}

	// Get light level (non-integrated)
	if light, err := redis.GetFloat(ctx, inputKeys.LightLevelNI); err == nil {
		c.state.LightLevel = light
	} else {
		c.state.LightLevel = 0.0 // Default light level
	}

	// Apply simple sensor integration (no complex buffering needed)
	c.state.IntegratedTemp = rawTemp
	c.state.IntegratedHumidity = rawHumidity

	return nil
}

// calculateDemands calculates control demands using enhanced configuration
func (c *CEBController) calculateDemands(setpoints map[types.SetpointPurpose]float64) {
	// Get tuning parameters from enhanced configuration
	heatTuning := c.config.HeatTuning.HeatReqForTemperatureControl
	ventTuning := c.config.VentilationTuning.VentReqForTemperatureControl

	// Temperature-based demands using proportional control
	heatingError := setpoints[types.PurposeHeatingTarget] - c.state.IntegratedTemp
	coolingError := c.state.IntegratedTemp - setpoints[types.PurposeCoolingTarget]

	if heatingError > 0 {
		// Need heating - use proportional span from enhanced config
		proportionalSpan := heatTuning.HeatingProportionalSpanP
		tempOutput := (heatingError / proportionalSpan) * 100.0 // Convert to percentage
		c.state.HeatReqForTemperatureControl = math.Max(0, math.Min(100, tempOutput))
		c.state.VentReqForTemperatureControl = 0
	} else if coolingError > 0 {
		// Need cooling (ventilation) - use proportional span from enhanced config
		proportionalSpan := ventTuning.CoolingProportionalSpanP
		tempOutput := (coolingError / proportionalSpan) * 100.0 // Convert to percentage
		c.state.VentReqForTemperatureControl = math.Max(0, math.Min(100, tempOutput))
		c.state.HeatReqForTemperatureControl = 0
	} else {
		// In temperature deadband
		c.state.HeatReqForTemperatureControl = 0
		c.state.VentReqForTemperatureControl = 0
	}

	// Humidity-based demands using enhanced configuration
	dehumidifyError := c.state.IntegratedHumidity - setpoints[types.PurposeDehumidifyVentTarget]
	humidifyError := setpoints[types.PurposeDehumidifyHeatTarget] - c.state.IntegratedHumidity

	if dehumidifyError > 0 {
		// Need dehumidification - use enhanced configuration parameters
		heatDehumidTuning := c.config.HeatTuning.HeatReqForDehumidification
		ventDehumidTuning := c.config.VentilationTuning.VentReqForDehumidification

		// Calculate heating demand for dehumidification
		heatProportionalSpan := heatDehumidTuning.DehumidifyHeatProportionalSpanP
		heatOutput := (dehumidifyError / heatProportionalSpan) * 100.0

		// Apply maxDehumidHeat limit if configured
		if maxDehumidHeat, exists := setpoints[types.PurposeMaxDehumidHeat]; exists {
			heatOutput = math.Min(heatOutput, maxDehumidHeat)
		}
		c.state.HeatReqForHumidityControl = math.Max(0, heatOutput)

		// Calculate ventilation demand for dehumidification
		ventProportionalSpan := ventDehumidTuning.VentilationDehumidifyProportionalSpanP
		ventOutput := (dehumidifyError / ventProportionalSpan) * 100.0

		// Apply maxDehumidVent limit if configured
		if maxDehumidVent, exists := setpoints[types.PurposeMaxDehumidVent]; exists {
			ventOutput = math.Min(ventOutput, maxDehumidVent)
		}
		c.state.VentReqForHumidityControl = math.Max(0, ventOutput)

	} else if humidifyError > 0 {
		// Need humidification - conservative approach
		c.state.HeatReqForHumidityControl = math.Max(0, humidifyError*2.0) // Simple proportional
		c.state.VentReqForHumidityControl = 0                              // Reduce ventilation to retain moisture
	} else {
		// In humidity deadband
		c.state.HeatReqForHumidityControl = 0
		c.state.VentReqForHumidityControl = 0
	}
}

// applyFeedForwardAdjustments applies predictive adjustments using enhanced configuration
func (c *CEBController) applyFeedForwardAdjustments() {
	// Get enhanced configuration parameters
	heatOutdoorEffect := c.config.HeatTuning.HeatingOutdoorTemperatureEffect
	heatLightEffect := c.config.HeatTuning.HeatingLightEffect
	ventLightEffect := c.config.VentilationTuning.VentilationLightEffect

	// Outdoor temperature effect for heating
	if heatOutdoorEffect.OutdoorTemperature.Used {
		// Calculate effect based on temperature differential and shade position
		tempDiff := 20.0 - c.state.OutdoorTemp // Assume 20°C as baseline
		if tempDiff > 0 {
			// Apply shade settings based on current shade position (assume retracted for now)
			shadeSettings := heatOutdoorEffect.ShadeRetractedSettings
			outdoorEffect := c.calculateLinearEffect(tempDiff,
				shadeSettings.MinTempDifference, shadeSettings.MaxTempDifference,
				shadeSettings.MinOutdoorEffect, shadeSettings.MaxOutdoorEffect)

			c.state.HeatReqForTemperatureControl += outdoorEffect
			c.state.HeatReqForHumidityControl += outdoorEffect * 0.5
		}
	}

	// Light effect for heating (reduces heating demand)
	if heatLightEffect.CurrentLightReading.Used {
		lightScaling := heatLightEffect.LightEffectScaling
		lightEffect := c.calculateLinearEffect(c.state.LightLevel,
			lightScaling.MinInput, lightScaling.MaxInput,
			lightScaling.MinOutput, lightScaling.MaxOutput)

		// Light reduces heating demand
		c.state.HeatReqForTemperatureControl = math.Max(0, c.state.HeatReqForTemperatureControl-lightEffect)
		c.state.HeatReqForHumidityControl = math.Max(0, c.state.HeatReqForHumidityControl-lightEffect*0.5)
	}

	// Light effect for ventilation (increases cooling demand)
	if ventLightEffect.CurrentLightReading.Used {
		lightScaling := ventLightEffect.LightEffectScaling
		lightEffect := c.calculateLinearEffect(c.state.LightLevel,
			lightScaling.MinInput, lightScaling.MaxInput,
			lightScaling.MinOutput, lightScaling.MaxOutput)

		// Light increases ventilation demand
		c.state.VentReqForTemperatureControl += lightEffect
		c.state.VentReqForHumidityControl += lightEffect * 0.7
	}
}

// calculateLinearEffect applies linear interpolation for feed-forward effects
func (c *CEBController) calculateLinearEffect(input, minInput, maxInput, minOutput, maxOutput float64) float64 {
	// Clamp input to configured range
	if input < minInput {
		return minOutput
	}
	if input > maxInput {
		return maxOutput
	}

	// Linear interpolation
	ratio := (input - minInput) / (maxInput - minInput)
	return minOutput + ratio*(maxOutput-minOutput)
}

// selectFinalRequests chooses the highest demand for each equipment type and calculates all 9 outputs
func (c *CEBController) selectFinalRequests() {
	// Calculate the 9 energy balance outputs as per specification:

	// 3. Highest Ventilation Request (highest of temperature or dehumidification demands)
	c.state.HighestVentRequest = math.Max(c.state.VentReqForTemperatureControl, c.state.VentReqForHumidityControl)

	// 4. Sum of Ventilation Requests
	c.state.SumOfVentRequests = c.state.VentReqForTemperatureControl + c.state.VentReqForHumidityControl

	// 7. Highest Heating Request (highest of temperature or dehumidification demands)
	c.state.HighestHeatRequest = math.Max(c.state.HeatReqForTemperatureControl, c.state.HeatReqForHumidityControl)

	// 8. Sum of Heating Requests
	c.state.SumOfHeatRequests = c.state.HeatReqForTemperatureControl + c.state.HeatReqForHumidityControl

	// 9. Current Temperature Request to the Heating System
	// This maps the final heating request percentage to actual heating system temperature
	// Use enhanced configuration heating system request parameters
	heatSystemRequest := c.config.HeatTuning.HeatingSystemRequest
	minTemp := heatSystemRequest.MinHeatingSystemTemp
	maxTemp := heatSystemRequest.MaxHeatingSystemTemp

	// Map 0-100% heating request to temperature range using linear interpolation
	// Formula: HeatingSystemTempRequest = MinTemp + (HighestHeatRequest/100) * (MaxTemp - MinTemp)
	c.state.HeatingSystemTempRequest = minTemp + (c.state.HighestHeatRequest/100.0)*(maxTemp-minTemp)

	// Apply equipment limits to final requests (percentage values 0-100%)
	c.state.HighestHeatRequest = math.Min(c.state.HighestHeatRequest, 100.0)
	c.state.HighestHeatRequest = math.Max(c.state.HighestHeatRequest, 0.0)

	c.state.HighestVentRequest = math.Min(c.state.HighestVentRequest, 100.0)
	c.state.HighestVentRequest = math.Max(c.state.HighestVentRequest, 0.0)

	// Ensure heating system temperature stays within configured range
	c.state.HeatingSystemTempRequest = math.Min(c.state.HeatingSystemTempRequest, maxTemp)
	c.state.HeatingSystemTempRequest = math.Max(c.state.HeatingSystemTempRequest, minTemp)
}

// applySafetyLimits validates outputs against safety constraints
func (c *CEBController) applySafetyLimits() error {
	// Basic safety limits - ensure outputs are within valid ranges
	c.state.HighestHeatRequest = math.Max(0, math.Min(100, c.state.HighestHeatRequest))
	c.state.HighestVentRequest = math.Max(0, math.Min(100, c.state.HighestVentRequest))
	c.state.SumOfHeatRequests = math.Max(0, c.state.SumOfHeatRequests)
	c.state.SumOfVentRequests = math.Max(0, c.state.SumOfVentRequests)

	return nil
}

// storeResults saves current state to Redis for monitoring and other programs
func (c *CEBController) storeResults(ctx context.Context) error {
	// Get the appropriate output Redis keys based on configuration type
	outputKeys := c.config.GetOutputRedisKeys()

	// Store all 9 Energy Balance Program outputs as per specification

	// 1. Ventilation Required for Temperature Control
	if err := redis.SetFloat(ctx, outputKeys.VentTempControl, c.state.VentReqForTemperatureControl); err != nil {
		return err
	}

	// 2. Ventilation Required for Humidity Control
	if err := redis.SetFloat(ctx, outputKeys.VentHumidityControl, c.state.VentReqForHumidityControl); err != nil {
		return err
	}

	// 3. Highest Ventilation Request
	if err := redis.SetFloat(ctx, outputKeys.HighestVentRequest, c.state.HighestVentRequest); err != nil {
		return err
	}

	// 4. Sum of Ventilation Requests
	if err := redis.SetFloat(ctx, outputKeys.SumVentRequests, c.state.SumOfVentRequests); err != nil {
		return err
	}

	// 5. Heating Required for Temperature Control
	if err := redis.SetFloat(ctx, outputKeys.HeatTempControl, c.state.HeatReqForTemperatureControl); err != nil {
		return err
	}

	// 6. Heating Required for Humidity Control
	if err := redis.SetFloat(ctx, outputKeys.HeatHumidityControl, c.state.HeatReqForHumidityControl); err != nil {
		return err
	}

	// 7. Highest Heating Request
	if err := redis.SetFloat(ctx, outputKeys.HighestHeatRequest, c.state.HighestHeatRequest); err != nil {
		return err
	}

	// 8. Sum of Heating Requests
	if err := redis.SetFloat(ctx, outputKeys.SumHeatRequests, c.state.SumOfHeatRequests); err != nil {
		return err
	}

	// 9. Current Temperature Request to the Heating System
	if err := redis.SetFloat(ctx, outputKeys.HeatingSystemTempRequest, c.state.HeatingSystemTempRequest); err != nil {
		return err
	}

	// Store integrated sensor values for monitoring
	if err := redis.SetFloat(ctx, outputKeys.IntegratedTemp, c.state.IntegratedTemp); err != nil {
		return err
	}

	if err := redis.SetFloat(ctx, outputKeys.IntegratedHumidity, c.state.IntegratedHumidity); err != nil {
		return err
	}

	return nil
}

// GetState returns the current CEB state for monitoring
func (c *CEBController) GetState() types.CEBState {
	return c.state
}

// IsEnabled returns whether the CEB system is currently enabled
func (c *CEBController) IsEnabled() bool {
	return c.config.Enabled
}

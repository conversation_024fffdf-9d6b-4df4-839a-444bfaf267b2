package program

import (
	"errors"
	"strconv"
	"strings"

	"program-manager/types"
)

// Selection processes the user input and returns the program status list and the JSON structure
func Selection(programs []string, input string) ([]types.ProgramStatus, map[string]interface{}, error) {
	input = strings.TrimSpace(input)
	if input == "" {
		return nil, nil, nil
	}
	for _, r := range input {
		if !(r >= '0' && r <= '9') && r != ',' && r != ' ' {
			return nil, nil, errors.New("invalid input: only numbers, commas, and spaces are allowed")
		}
	}
	selected := make(map[int]bool)
	for _, s := range strings.Split(input, ",") {
		s = strings.TrimSpace(s)
		if s == "" {
			continue
		}
		if idx, err := strconv.Atoi(s); err == nil && idx > 0 && idx <= len(programs) {
			selected[idx-1] = true
		} else {
			return nil, nil, errors.New("invalid input: only numbers, commas, and spaces are allowed")
		}
	}
	programManager := make(map[string]interface{})
	statuses := make([]types.ProgramStatus, len(programs))
	for i, programName := range programs {
		enabled := selected[i]
		if programName == "diurnalSetpoint" && enabled {
			programManager[programName] = types.DiurnalSetpointConfig{Instances: []types.DiurnalInstance{}}
		} else if programName == "climateEnergyBalance" && enabled {
			programManager[programName] = types.CEBConfig{Enabled: true}
		} else if programName == "clock" && enabled {
			programManager[programName] = types.ClockConfig{Enabled: true}
		} else {
			programManager[programName] = map[string]bool{"enabled": enabled}
		}
		statuses[i] = types.ProgramStatus{Name: programName, Enabled: enabled}
	}
	return statuses, programManager, nil
}

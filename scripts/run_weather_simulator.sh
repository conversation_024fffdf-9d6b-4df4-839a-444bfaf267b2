#!/bin/bash

# Weather Simulator Runner Script
# This script runs the weather data simulator for CEB testing

echo "🌤️  Starting Weather Data Simulator for CEB System"
echo "=================================================="
echo ""
echo "This simulator will generate realistic weather data and publish to Redis:"
echo "  • Outdoor Temperature (daily cycle with random variation)"
echo "  • Light Level Non-Integrated (based on time of day)"
echo "  • Light Level Integrated (accumulated with decay)"
echo ""
echo "Redis Topics:"
echo "  • hub:h1:instance:I1:weather:outdoorTemperature"
echo "  • hub:h1:instance:I1:weather:lightLevelNI"
echo "  • hub:h1:instance:I1:weather:lightLevelI"
echo ""
echo "Press Ctrl+C to stop the simulator"
echo ""

# Check if Redis is running
if ! redis-cli ping > /dev/null 2>&1; then
    echo "❌ Error: Redis server is not running!"
    echo "Please start Redis server first:"
    echo "  brew services start redis  (on macOS)"
    echo "  sudo systemctl start redis (on Linux)"
    exit 1
fi

echo "✅ Redis server is running"
echo ""

# Build and run the weather simulator
cd "$(dirname "$0")/.."
echo "🔨 Building weather simulator..."
go build -o bin/weather_simulator scripts/weather_simulator.go

if [ $? -ne 0 ]; then
    echo "❌ Failed to build weather simulator"
    exit 1
fi

echo "🚀 Starting weather simulator..."
echo ""
./bin/weather_simulator

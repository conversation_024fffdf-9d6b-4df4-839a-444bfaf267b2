package main

import (
	"context"
	"fmt"
	"log"
	"os"
	"os/signal"
	"syscall"
	"time"

	"program-manager/redis"
)

// CEBWeatherMonitor monitors weather data flow between simulator and CEB
type CEBWeatherMonitor struct {
	ctx context.Context
}

// weatherTopics defines the Redis topics for weather data
var weatherTopics = map[string]string{
	"Outdoor Temperature": "hub:h1:instance:I1:weather:outdoorTemperature",
	"Light Level (NI)":    "hub:h1:instance:I1:weather:lightLevelNI",
	"Light Level (I)":     "hub:h1:instance:I1:weather:lightLevelI",
}

// cebOutputTopics defines key CEB output topics to monitor
var cebOutputTopics = map[string]string{
	"Integrated Temperature": "hub:h1:zone:z1:instance:I1:ceb:integratedTemp",
	"Integrated Humidity":    "hub:h1:zone:z1:instance:I1:ceb:integratedHumidity",
	"Vent Temp Control":      "hub:h1:zone:z1:instance:I1:ceb:ventTempControl",
	"Heat Temp Control":      "hub:h1:zone:z1:instance:I1:ceb:heatTempControl",
	"Highest Vent Request":   "hub:h1:zone:z1:instance:I1:ceb:highestVentRequest",
	"Highest Heat Request":   "hub:h1:zone:z1:instance:I1:ceb:highestHeatRequest",
}

// NewCEBWeatherMonitor creates a new monitor instance
func NewCEBWeatherMonitor() *CEBWeatherMonitor {
	return &CEBWeatherMonitor{}
}

// checkWeatherDataAvailability checks if weather data is present
func (m *CEBWeatherMonitor) checkWeatherDataAvailability() (bool, map[string]float64) {
	values := make(map[string]float64)
	allPresent := true

	for name, topic := range weatherTopics {
		if value, err := redis.GetFloat(m.ctx, topic); err == nil {
			values[name] = value
		} else {
			allPresent = false
		}
	}

	return allPresent, values
}

// checkCEBOutputs checks if CEB is producing outputs
func (m *CEBWeatherMonitor) checkCEBOutputs() (bool, map[string]float64) {
	values := make(map[string]float64)
	anyPresent := false

	for name, topic := range cebOutputTopics {
		if value, err := redis.GetFloat(m.ctx, topic); err == nil {
			values[name] = value
			anyPresent = true
		}
	}

	return anyPresent, values
}

// displayStatus shows current status of weather data and CEB outputs
func (m *CEBWeatherMonitor) displayStatus() {
	fmt.Printf("\n🔍 [%s] CEB Weather Data Monitor\n", time.Now().Format("15:04:05"))
	fmt.Println("=" + string(make([]byte, 50)))

	// Check weather data
	weatherPresent, weatherValues := m.checkWeatherDataAvailability()
	
	fmt.Println("\n📡 Weather Data (Simulator → Redis):")
	if weatherPresent {
		for name, value := range weatherValues {
			var unit string
			switch name {
			case "Outdoor Temperature":
				unit = "°C"
			case "Light Level (NI)", "Light Level (I)":
				unit = "lux"
			}
			fmt.Printf("  ✅ %-20s: %8.1f %s\n", name, value, unit)
		}
	} else {
		fmt.Println("  ❌ Weather data missing! Start weather simulator first.")
		return
	}

	// Check CEB outputs
	cebActive, cebValues := m.checkCEBOutputs()
	
	fmt.Println("\n🏭 CEB Outputs (Redis ← CEB System):")
	if cebActive {
		for name, value := range cebValues {
			var unit string
			switch name {
			case "Integrated Temperature":
				unit = "°C"
			case "Integrated Humidity":
				unit = "%"
			default:
				unit = "%"
			}
			fmt.Printf("  ✅ %-20s: %8.1f %s\n", name, value, unit)
		}
	} else {
		fmt.Println("  ⚠️  No CEB outputs detected. CEB system may not be running.")
	}

	// Status summary
	fmt.Println("\n📊 System Status:")
	if weatherPresent && cebActive {
		fmt.Println("  🟢 EXCELLENT: Weather simulator and CEB system both active!")
		fmt.Println("  🔄 Data flow: Simulator → Redis → CEB → Redis")
	} else if weatherPresent && !cebActive {
		fmt.Println("  🟡 PARTIAL: Weather data available, but CEB not running")
		fmt.Println("  💡 Start CEB system: ./bin/program-manager (option 2)")
	} else if !weatherPresent && cebActive {
		fmt.Println("  🟡 PARTIAL: CEB running, but no weather data")
		fmt.Println("  💡 Start weather simulator: ./scripts/run_weather_simulator.sh")
	} else {
		fmt.Println("  🔴 INACTIVE: Neither weather simulator nor CEB detected")
		fmt.Println("  💡 Start both systems to see data flow")
	}
}

// monitor runs the monitoring loop
func (m *CEBWeatherMonitor) monitor() {
	log.Println("🔍 CEB Weather Data Monitor Started")
	log.Println("📊 Monitoring data flow: Weather Simulator → Redis → CEB System")
	log.Println("⏱️  Update interval: 10 seconds")
	log.Println("🛑 Press Ctrl+C to stop monitoring")

	// Initial status check
	m.displayStatus()

	ticker := time.NewTicker(10 * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-m.ctx.Done():
			log.Println("\n🛑 CEB Weather Monitor stopped")
			return
		case <-ticker.C:
			m.displayStatus()
		}
	}
}

func main() {
	// Initialize Redis
	redis.Initialize()

	// Create context for graceful shutdown
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	// Handle interrupt signals
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)

	// Create and start monitor
	monitor := NewCEBWeatherMonitor()
	monitor.ctx = ctx

	// Start monitoring in a goroutine
	go monitor.monitor()

	// Wait for interrupt signal
	<-sigChan
	log.Println("\n🔄 Shutting down monitor...")
	cancel()

	// Give some time for cleanup
	time.Sleep(1 * time.Second)
	log.Println("✅ Monitor stopped")
}

package main

import (
	"context"
	"log"
	"math"
	"math/rand"
	"os"
	"os/signal"
	"syscall"
	"time"

	"program-manager/redis"
)

// WeatherSimulator simulates realistic weather data for CEB testing
type WeatherSimulator struct {
	ctx               context.Context
	outdoorTempTopic  string
	lightLevelNITopic string
	lightLevelITopic  string
	updateInterval    time.Duration
	baseTemp          float64
	tempVariation     float64
	lightIntegrated   float64
	lightDecayFactor  float64
}

// NewWeatherSimulator creates a new weather simulator instance
func NewWeatherSimulator() *WeatherSimulator {
	return &WeatherSimulator{
		// Redis topics from cebConfigEnhanced.json
		outdoorTempTopic:  "hub:h1:instance:I1:weather:outdoorTemperature",
		lightLevelNITopic: "hub:h1:instance:I1:weather:lightLevelNI",
		lightLevelITopic:  "hub:h1:instance:I1:weather:lightLevelI",
		updateInterval:    30 * time.Second, // Update every 30 seconds
		baseTemp:          20.0,             // Base temperature in Celsius
		tempVariation:     15.0,             // Temperature variation range
		lightIntegrated:   0.0,              // Initial integrated light value
		lightDecayFactor:  0.95,             // Decay factor for integrated light
	}
}

// generateOutdoorTemperature generates realistic outdoor temperature based on time of day
func (ws *WeatherSimulator) generateOutdoorTemperature() float64 {
	now := time.Now()

	// Create daily temperature cycle (sinusoidal)
	// Peak temperature around 2 PM (14:00), minimum around 6 AM
	hoursFromMidnight := float64(now.Hour()) + float64(now.Minute())/60.0

	// Shift the sine wave so minimum is at 6 AM and maximum at 2 PM
	timeRadians := (hoursFromMidnight - 6.0) * math.Pi / 12.0

	// Base daily cycle
	dailyCycle := math.Sin(timeRadians) * ws.tempVariation

	// Add some random variation (±2°C)
	randomVariation := (rand.Float64() - 0.5) * 4.0

	// Seasonal variation (simplified - assume spring/autumn)
	temperature := ws.baseTemp + dailyCycle + randomVariation

	// Ensure reasonable bounds (0°C to 45°C)
	if temperature < 0 {
		temperature = 0
	} else if temperature > 45 {
		temperature = 45
	}

	return temperature
}

// generateLightLevel generates realistic light levels based on time of day
func (ws *WeatherSimulator) generateLightLevel() float64 {
	now := time.Now()
	hour := now.Hour()
	minute := now.Minute()

	// Convert to decimal hours
	decimalHour := float64(hour) + float64(minute)/60.0

	// Define sunrise and sunset times (approximate for most locations)
	sunrise := 6.0 // 6:00 AM
	sunset := 18.0 // 6:00 PM

	var lightLevel float64

	if decimalHour < sunrise || decimalHour > sunset {
		// Nighttime - very low light
		lightLevel = rand.Float64() * 10.0 // 0-10 lux
	} else {
		// Daytime - calculate based on sun position
		dayLength := sunset - sunrise
		timeFromSunrise := decimalHour - sunrise

		// Create a bell curve for light intensity
		sunPosition := timeFromSunrise / dayLength // 0 to 1

		// Peak light at solar noon (middle of the day)
		lightIntensity := math.Sin(sunPosition * math.Pi)

		// Scale to realistic lux values (100-1000 lux for typical indoor/outdoor)
		maxLight := 800.0 + rand.Float64()*200.0 // 800-1000 lux max
		lightLevel = lightIntensity * maxLight

		// Add cloud variation (random reduction)
		cloudFactor := 0.7 + rand.Float64()*0.3 // 70-100% of clear sky
		lightLevel *= cloudFactor

		// Ensure minimum daytime light
		if lightLevel < 50.0 {
			lightLevel = 50.0
		}
	}

	return lightLevel
}

// updateIntegratedLight updates the integrated light value with decay
func (ws *WeatherSimulator) updateIntegratedLight(currentLight float64) {
	// Apply decay to existing integrated value
	ws.lightIntegrated *= ws.lightDecayFactor

	// Add current light contribution (scaled down)
	ws.lightIntegrated += currentLight * 0.1

	// Ensure reasonable bounds
	if ws.lightIntegrated > 1000.0 {
		ws.lightIntegrated = 1000.0
	} else if ws.lightIntegrated < 0.0 {
		ws.lightIntegrated = 0.0
	}
}

// simulate runs the weather simulation loop
func (ws *WeatherSimulator) simulate() {
	log.Println("🌤️  Weather Simulator Started")
	log.Printf("📡 Publishing to Redis topics:")
	log.Printf("   • Outdoor Temperature: %s", ws.outdoorTempTopic)
	log.Printf("   • Light Level (NI):    %s", ws.lightLevelNITopic)
	log.Printf("   • Light Level (I):     %s", ws.lightLevelITopic)
	log.Printf("⏱️  Update interval: %v", ws.updateInterval)
	log.Println()

	ticker := time.NewTicker(ws.updateInterval)
	defer ticker.Stop()

	for {
		select {
		case <-ws.ctx.Done():
			log.Println("🛑 Weather simulation stopped")
			return
		case <-ticker.C:
			// Generate weather data
			outdoorTemp := ws.generateOutdoorTemperature()
			lightLevelNI := ws.generateLightLevel()

			// Update integrated light
			ws.updateIntegratedLight(lightLevelNI)

			// Store in Redis
			if err := redis.SetFloat(ws.ctx, ws.outdoorTempTopic, outdoorTemp); err != nil {
				log.Printf("❌ Error setting outdoor temperature: %v", err)
				continue
			}

			if err := redis.SetFloat(ws.ctx, ws.lightLevelNITopic, lightLevelNI); err != nil {
				log.Printf("❌ Error setting light level (NI): %v", err)
				continue
			}

			if err := redis.SetFloat(ws.ctx, ws.lightLevelITopic, ws.lightIntegrated); err != nil {
				log.Printf("❌ Error setting light level (I): %v", err)
				continue
			}

			// Display current values
			now := time.Now()
			log.Printf("🌡️  [%s] Outdoor Temp: %6.1f°C | Light (NI): %6.1f lux | Light (I): %6.1f lux",
				now.Format("15:04:05"), outdoorTemp, lightLevelNI, ws.lightIntegrated)
		}
	}
}

func main() {
	// Initialize Redis
	redis.Initialize()

	// Create context for graceful shutdown
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	// Handle interrupt signals for graceful shutdown
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)

	// Create and start weather simulator
	simulator := NewWeatherSimulator()
	simulator.ctx = ctx

	// Start simulation in a goroutine
	go simulator.simulate()

	// Wait for interrupt signal
	<-sigChan
	log.Println("\n🔄 Shutting down weather simulator...")
	cancel()

	// Give some time for cleanup
	time.Sleep(1 * time.Second)
	log.Println("✅ Weather simulator stopped")
}

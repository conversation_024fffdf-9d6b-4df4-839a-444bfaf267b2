#!/bin/bash

# CEB Interactive Test Demo Script
# This script demonstrates how to use the interactive CEB test

echo "🏭 CEB Interactive Test Demo"
echo "============================"
echo ""
echo "This demo shows how to test the CEB system with custom input values"
echo "and see all 9 outputs that the Climate Energy Balance system produces."
echo ""

# Check if Redis is running
if ! redis-cli ping > /dev/null 2>&1; then
    echo "❌ Error: Redis server is not running!"
    echo "Please start Redis server first:"
    echo "  brew services start redis  (on macOS)"
    echo "  sudo systemctl start redis (on Linux)"
    exit 1
fi

echo "✅ Redis server is running"
echo ""

# Build the interactive test
echo "🔨 Building CEB interactive test..."
make test-ceb-interactive > /dev/null 2>&1

if [ $? -ne 0 ]; then
    echo "❌ Failed to build CEB interactive test"
    exit 1
fi

echo "✅ CEB interactive test built successfully"
echo ""

echo "📋 About the CEB Interactive Test:"
echo "=================================="
echo ""
echo "The CEB (Climate Energy Balance) system takes various inputs and produces 9 outputs:"
echo ""
echo "📥 Input Values:"
echo "  • Zone Temperature (°C)"
echo "  • Zone Humidity (%)"
echo "  • Outdoor Temperature (°C)"
echo "  • Light Level (lux)"
echo "  • Shade Position (%)"
echo "  • Heating Target (°C)"
echo "  • Cooling Target (°C)"
echo "  • Dehumidify Ventilation Target (%)"
echo "  • Dehumidify Heating Target (%)"
echo "  • Max Dehumidify Ventilation (%)"
echo "  • Max Dehumidify Heating (%)"
echo ""
echo "📤 CEB Outputs (9 Values):"
echo "  1. Ventilation Required for Temperature Control (%)"
echo "  2. Ventilation Required for Humidity Control (%)"
echo "  3. Highest Ventilation Request (%)"
echo "  4. Sum of Ventilation Requests (%)"
echo "  5. Heating Required for Temperature Control (%)"
echo "  6. Heating Required for Humidity Control (%)"
echo "  7. Highest Heating Request (%)"
echo "  8. Sum of Heating Requests (%)"
echo "  9. Current Temperature Request to Heating System (°C)"
echo ""
echo "🎯 Test Scenarios Available:"
echo "  1. Custom input values (you enter each value)"
echo "  2. Hot Day preset (high temperatures, need cooling)"
echo "  3. Cold Day preset (low temperatures, need heating)"
echo "  4. High Humidity preset (high humidity, need dehumidification)"
echo ""
echo "💡 Usage Tips:"
echo "  • Press Enter to use default values when entering custom inputs"
echo "  • Try different scenarios to see how CEB responds"
echo "  • Watch how the 9 outputs change based on different conditions"
echo "  • The system uses PID control logic for realistic responses"
echo ""

echo "🚀 Starting CEB Interactive Test..."
echo "===================================="
echo ""
echo "The interactive test will now start. You can:"
echo "  • Enter custom values to test specific scenarios"
echo "  • Use presets to see typical building automation scenarios"
echo "  • Run multiple tests to understand CEB behavior"
echo ""
echo "Press any key to continue..."
read -n 1 -s

echo ""
echo "🏭 Launching CEB Interactive Test..."
echo ""

# Run the interactive test
./bin/test-ceb-interactive

echo ""
echo "🏁 CEB Interactive Test Demo Complete"
echo ""
echo "💡 Next Steps:"
echo "  • Run the test again: make run-ceb-test"
echo "  • Try with weather simulation: ./scripts/run_weather_simulator.sh"
echo "  • Test with diurnal setpoints: ./bin/program-manager (option 1)"
echo "  • Run full CEB system: ./bin/program-manager (option 2)"

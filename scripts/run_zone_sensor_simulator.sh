#!/bin/bash

# Zone Sensor Simulator Startup Script
# This script starts the zone sensor simulator with different scenarios

echo "🏠 Zone Sensor Simulator"
echo "========================"
echo ""
echo "This simulator generates realistic zone sensor data for the CEB system:"
echo "  • Zone Temperature (hub:h1:io:sensorTemperature)"
echo "  • Backup Zone Temperature (hub:h1:io:backupSensorTemperature)"  
echo "  • Zone Humidity (hub:h1:io:sensorHumidity)"
echo ""

# Check if Redis is running
if ! redis-cli ping > /dev/null 2>&1; then
    echo "❌ Error: Redis server is not running!"
    echo "Please start Redis server first:"
    echo "  brew services start redis  (on macOS)"
    echo "  sudo systemctl start redis (on Linux)"
    exit 1
fi

echo "✅ Redis server is running"
echo ""

# Build the simulator
echo "🔨 Building zone sensor simulator..."
go build -o bin/zone_sensor_simulator scripts/zone_sensor_simulator.go

if [ $? -ne 0 ]; then
    echo "❌ Failed to build zone sensor simulator"
    exit 1
fi

echo "✅ Zone sensor simulator built successfully"
echo ""

# Check for scenario argument
SCENARIO=${1:-"comfortable"}

echo "📋 Available Scenarios:"
echo "  comfortable - Normal indoor conditions (22°C, 50% humidity)"
echo "  hot         - Hot conditions (26°C, 40% humidity)"
echo "  cold        - Cold conditions (18°C, 65% humidity)"
echo "  humid       - High humidity conditions (22°C, 75% humidity)"
echo ""

case $SCENARIO in
    "comfortable"|"hot"|"cold"|"humid")
        echo "🎯 Starting with scenario: $SCENARIO"
        ;;
    *)
        echo "⚠️  Unknown scenario '$SCENARIO', using 'comfortable'"
        SCENARIO="comfortable"
        ;;
esac

echo ""
echo "🔄 Starting zone sensor simulation..."
echo "📊 Data will be updated every 5 seconds"
echo "🛑 Press Ctrl+C to stop"
echo ""

# Run the simulator
./bin/zone_sensor_simulator $SCENARIO

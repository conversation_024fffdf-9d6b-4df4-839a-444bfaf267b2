package main

import (
	"context"
	"fmt"
	"log"
	"time"

	"program-manager/redis"
)

// testWeatherData reads and displays current weather data from Redis
func testWeatherData() {
	// Initialize Redis
	redis.Initialize()
	ctx := context.Background()

	// Redis topics from cebConfigEnhanced.json
	topics := map[string]string{
		"Outdoor Temperature": "hub:h1:instance:I1:weather:outdoorTemperature",
		"Light Level (NI)":    "hub:h1:instance:I1:weather:lightLevelNI",
		"Light Level (I)":     "hub:h1:instance:I1:weather:lightLevelI",
	}

	fmt.Println("🌤️  Weather Data Test - Reading from Redis")
	fmt.Println("==========================================")
	fmt.Println()

	// Check if data exists and display current values
	fmt.Println("📊 Current Weather Data:")
	fmt.Println()

	allDataPresent := true
	for name, topic := range topics {
		value, err := redis.GetFloat(ctx, topic)
		if err != nil {
			fmt.Printf("❌ %-20s: No data (key: %s)\n", name, topic)
			allDataPresent = false
		} else {
			var unit string
			switch name {
			case "Outdoor Temperature":
				unit = "°C"
			case "Light Level (NI)", "Light Level (I)":
				unit = "lux"
			}
			fmt.Printf("✅ %-20s: %8.1f %s\n", name, value, unit)
		}
	}

	fmt.Println()

	if !allDataPresent {
		fmt.Println("⚠️  Some weather data is missing!")
		fmt.Println("💡 To generate weather data, run:")
		fmt.Println("   ./scripts/run_weather_simulator.sh")
		fmt.Println()
		fmt.Println("   Or manually run:")
		fmt.Println("   go run scripts/weather_simulator.go")
	} else {
		fmt.Println("✅ All weather data is present and ready for CEB system!")
		fmt.Println()
		fmt.Println("🔄 Monitoring weather data updates (press Ctrl+C to stop)...")
		fmt.Println()

		// Monitor updates for a few cycles
		ticker := time.NewTicker(5 * time.Second)
		defer ticker.Stop()

		for i := 0; i < 6; i++ { // Monitor for 30 seconds
			select {
			case <-ticker.C:
				fmt.Printf("📈 [%s] ", time.Now().Format("15:04:05"))
				
				for name, topic := range topics {
					value, err := redis.GetFloat(ctx, topic)
					if err != nil {
						fmt.Printf("%s: ERROR ", name)
					} else {
						switch name {
						case "Outdoor Temperature":
							fmt.Printf("Temp: %5.1f°C ", value)
						case "Light Level (NI)":
							fmt.Printf("Light(NI): %6.1f ", value)
						case "Light Level (I)":
							fmt.Printf("Light(I): %6.1f ", value)
						}
					}
				}
				fmt.Println()
			}
		}
	}

	fmt.Println()
	fmt.Println("🏁 Weather data test completed")
}

func main() {
	log.SetFlags(0) // Remove timestamp from logs for cleaner output
	testWeatherData()
}

package main

import (
	"context"
	"fmt"
	"log"
	"math"
	"math/rand"
	"os"
	"time"

	"program-manager/redis"
)

// ZoneSensorSimulator simulates realistic zone sensor data
type ZoneSensorSimulator struct {
	// Current sensor values
	zoneTemp       float64
	backupZoneTemp float64
	zoneHumidity   float64

	// Simulation parameters
	baseTemp      float64 // Base temperature around which to vary
	tempRange     float64 // Temperature variation range
	baseHumidity  float64 // Base humidity around which to vary
	humidityRange float64 // Humidity variation range

	// Redis topics from cebConfig.json
	zoneTempTopic       string
	backupZoneTempTopic string
	zoneHumidityTopic   string

	// Simulation state
	timeOffset float64 // For creating daily cycles
}

// NewZoneSensorSimulator creates a new zone sensor simulator
func NewZoneSensorSimulator() *ZoneSensorSimulator {
	return &ZoneSensorSimulator{
		// Default comfortable indoor conditions
		baseTemp:      22.0, // 22°C base temperature
		tempRange:     3.0,  // ±3°C variation
		baseHumidity:  50.0, // 50% base humidity
		humidityRange: 15.0, // ±15% variation

		// Redis topics from cebConfig.json
		zoneTempTopic:       "hub:h1:io:sensorTemperature",
		backupZoneTempTopic: "hub:h1:io:backupSensorTemperature",
		zoneHumidityTopic:   "hub:h1:io:sensorHumidity",

		// Initialize with base values
		zoneTemp:       22.0,
		backupZoneTemp: 22.0,
		zoneHumidity:   50.0,
		timeOffset:     rand.Float64() * 24.0, // Random start time
	}
}

// generateRealisticValues creates realistic sensor readings with daily patterns
func (z *ZoneSensorSimulator) generateRealisticValues() {
	now := time.Now()

	// Create daily temperature cycle (warmer during day, cooler at night)
	hourOfDay := float64(now.Hour()) + float64(now.Minute())/60.0 + z.timeOffset

	// Temperature follows a sine wave with peak around 2 PM (14:00)
	tempCycle := math.Sin((hourOfDay-14.0)*math.Pi/12.0) * (z.tempRange / 2.0)

	// Add some random variation (±0.5°C)
	tempNoise := (rand.Float64() - 0.5) * 1.0

	// Calculate zone temperature
	z.zoneTemp = z.baseTemp + tempCycle + tempNoise

	// Backup sensor should be very close but slightly different
	backupNoise := (rand.Float64() - 0.5) * 0.2 // ±0.1°C difference
	z.backupZoneTemp = z.zoneTemp + backupNoise

	// Humidity is inversely related to temperature (warmer = less humid)
	// Also add daily cycle and random variation
	humidityCycle := -tempCycle * 0.8             // Inverse relationship with temperature
	humidityNoise := (rand.Float64() - 0.5) * 5.0 // ±2.5% random variation

	z.zoneHumidity = z.baseHumidity + humidityCycle + humidityNoise

	// Ensure humidity stays within realistic bounds (30-80%)
	if z.zoneHumidity < 30.0 {
		z.zoneHumidity = 30.0
	}
	if z.zoneHumidity > 80.0 {
		z.zoneHumidity = 80.0
	}

	// Ensure temperatures stay within reasonable indoor bounds (18-28°C)
	if z.zoneTemp < 18.0 {
		z.zoneTemp = 18.0
	}
	if z.zoneTemp > 28.0 {
		z.zoneTemp = 28.0
	}

	// Keep backup temperature close to main temperature
	if z.backupZoneTemp < 18.0 {
		z.backupZoneTemp = 18.0
	}
	if z.backupZoneTemp > 28.0 {
		z.backupZoneTemp = 28.0
	}
}

// writeToRedis writes current sensor values to Redis
func (z *ZoneSensorSimulator) writeToRedis(ctx context.Context) error {
	// Write zone temperature
	if err := redis.SetFloat(ctx, z.zoneTempTopic, z.zoneTemp); err != nil {
		return fmt.Errorf("failed to write zone temperature: %v", err)
	}

	// Write backup zone temperature
	if err := redis.SetFloat(ctx, z.backupZoneTempTopic, z.backupZoneTemp); err != nil {
		return fmt.Errorf("failed to write backup zone temperature: %v", err)
	}

	// Write zone humidity
	if err := redis.SetFloat(ctx, z.zoneHumidityTopic, z.zoneHumidity); err != nil {
		return fmt.Errorf("failed to write zone humidity: %v", err)
	}

	return nil
}

// printStatus displays current sensor readings
func (z *ZoneSensorSimulator) printStatus() {
	timestamp := time.Now().Format("15:04:05")
	fmt.Printf("[%s] 🌡️  Zone Sensors:\n", timestamp)
	fmt.Printf("  Zone Temperature:        %.1f °C\n", z.zoneTemp)
	fmt.Printf("  Backup Zone Temperature: %.1f °C\n", z.backupZoneTemp)
	fmt.Printf("  Zone Humidity:           %.1f %%\n", z.zoneHumidity)
	fmt.Printf("  Temperature Difference:  %.2f °C\n", math.Abs(z.zoneTemp-z.backupZoneTemp))
	fmt.Println()
}

// setScenario allows setting specific scenarios for testing
func (z *ZoneSensorSimulator) setScenario(scenario string) {
	switch scenario {
	case "hot":
		z.baseTemp = 26.0
		z.baseHumidity = 40.0
		log.Println("🔥 Set to HOT scenario (26°C, 40% humidity)")
	case "cold":
		z.baseTemp = 18.0
		z.baseHumidity = 65.0
		log.Println("❄️  Set to COLD scenario (18°C, 65% humidity)")
	case "humid":
		z.baseTemp = 22.0
		z.baseHumidity = 75.0
		log.Println("💧 Set to HUMID scenario (22°C, 75% humidity)")
	case "comfortable":
		z.baseTemp = 22.0
		z.baseHumidity = 50.0
		log.Println("😊 Set to COMFORTABLE scenario (22°C, 50% humidity)")
	default:
		log.Printf("⚠️  Unknown scenario '%s', using comfortable defaults", scenario)
		z.baseTemp = 22.0
		z.baseHumidity = 50.0
	}
}

func main() {
	fmt.Println("🏠 Zone Sensor Simulator")
	fmt.Println("========================")
	fmt.Println("Simulating realistic zone sensor data for CEB system")
	fmt.Println()

	// Initialize Redis
	redis.Initialize()
	ctx := context.Background()

	// Test Redis connection by trying to set a test value
	testKey := "test:connection"
	if err := redis.SetString(ctx, testKey, "test"); err != nil {
		log.Fatalf("❌ Failed to connect to Redis: %v", err)
	}
	fmt.Println("✅ Connected to Redis")

	// Create simulator
	simulator := NewZoneSensorSimulator()

	// Check for scenario argument
	scenario := "comfortable" // default
	if len(os.Args) > 1 {
		scenario = os.Args[1]
	}
	simulator.setScenario(scenario)

	fmt.Println()
	fmt.Println("📊 Redis Topics:")
	fmt.Printf("  Zone Temperature:        %s\n", simulator.zoneTempTopic)
	fmt.Printf("  Backup Zone Temperature: %s\n", simulator.backupZoneTempTopic)
	fmt.Printf("  Zone Humidity:           %s\n", simulator.zoneHumidityTopic)
	fmt.Println()
	fmt.Println("🔄 Starting simulation... (Press Ctrl+C to stop)")
	fmt.Println()

	// Simulation loop
	ticker := time.NewTicker(5 * time.Second) // Update every 5 seconds
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			// Generate new realistic values
			simulator.generateRealisticValues()

			// Write to Redis
			if err := simulator.writeToRedis(ctx); err != nil {
				log.Printf("❌ Error writing to Redis: %v", err)
				continue
			}

			// Display current status
			simulator.printStatus()

		case <-ctx.Done():
			fmt.Println("👋 Simulation stopped")
			return
		}
	}
}
